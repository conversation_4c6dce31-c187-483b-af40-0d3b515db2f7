[2025-07-02 21:48:46,788][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-02 21:53:30,936][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-02 21:55:58,375][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\conf\env.py changed, reloading.
[2025-07-02 21:56:00,668][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-02 21:56:16,867][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\conf\env.py changed, reloading.
[2025-07-02 21:56:18,937][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-02 21:56:25,245][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-02 21:58:15,585][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:14:47,566][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:15:11,503][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:17:29,827][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:21:26,654][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:22:00,723][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\conf\env.py changed, reloading.
[2025-07-03 21:22:03,228][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:22:44,462][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:23:09,298][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:24:00,926][django.server.log_message():212] [INFO] "OPTIONS /api/captcha/ HTTP/1.1" 200 0
[2025-07-03 21:24:00,926][django.server.log_message():212] [INFO] "OPTIONS /api/init/settings/ HTTP/1.1" 200 0
[2025-07-03 21:24:01,028][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-03 21:24:01,107][django.server.log_message():212] [INFO] "GET /api/captcha/ HTTP/1.1" 200 5421
[2025-07-03 21:24:16,848][django.server.log_message():212] [INFO] "OPTIONS /api/login/ HTTP/1.1" 200 0
[2025-07-03 21:24:17,027][dvadmin.utils.backends.authenticate():22] [INFO] superadmin 正在使用本地登录...
[2025-07-03 21:24:17,693][django.server.log_message():212] [INFO] "POST /api/login/ HTTP/1.1" 200 744
[2025-07-03 21:24:17,701][django.server.log_message():212] [INFO] "OPTIONS /api/system/user/user_info/ HTTP/1.1" 200 0
[2025-07-03 21:24:17,701][django.server.log_message():212] [INFO] "OPTIONS /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 0
[2025-07-03 21:24:17,705][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-03 21:24:17,867][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-03 21:24:17,871][django.server.log_message():212] [INFO] "OPTIONS /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 0
[2025-07-03 21:24:17,874][django.server.log_message():212] [INFO] "OPTIONS /api/system/dept/all_dept/ HTTP/1.1" 200 0
[2025-07-03 21:24:17,874][django.server.log_message():212] [INFO] "OPTIONS /api/system/menu/web_router/ HTTP/1.1" 200 0
[2025-07-03 21:24:17,881][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-03 21:24:18,137][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-03 21:24:18,179][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1408
[2025-07-03 21:24:18,179][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-03 21:24:18,179][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 6497
[2025-07-03 21:24:18,238][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-03 21:24:18,248][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-03 21:24:18,417][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 6497
[2025-07-03 21:24:18,417][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-03 21:24:18,425][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-03 21:24:18,464][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1408
[2025-07-03 21:24:21,430][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNjM1NDU3LCJpYXQiOjE3NTE1NDkwNTcsImp0aSI6IjkzOTdhNzAwOTY4MzQzMmZhOTk2ZTRiYmRmMjFlYzA4IiwidXNlcl9pZCI6MX0._odRMmGCquhh2gYcU6OrXTclIDeH8i28kto8dWgMV9s HTTP/1.1" 500 59
[2025-07-03 21:26:57,594][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\conf\env.py changed, reloading.
[2025-07-03 21:26:59,890][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:27:36,951][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\conf\env.py changed, reloading.
[2025-07-03 21:27:39,252][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:51:39,171][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:51:47,052][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-03 21:51:47,068][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-03 21:51:47,207][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-03 21:51:47,207][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1408
[2025-07-03 21:51:47,217][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-03 21:51:47,236][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 6497
[2025-07-03 21:51:49,490][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNjM1NDU3LCJpYXQiOjE3NTE1NDkwNTcsImp0aSI6IjkzOTdhNzAwOTY4MzQzMmZhOTk2ZTRiYmRmMjFlYzA4IiwidXNlcl9pZCI6MX0._odRMmGCquhh2gYcU6OrXTclIDeH8i28kto8dWgMV9s HTTP/1.1" 500 59
