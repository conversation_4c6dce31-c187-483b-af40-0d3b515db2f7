{"version": 3, "sources": ["../../process/browser.js"], "sourcesContent": ["// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n"], "mappings": ";;;;;AAAA;AAAA;AACA,QAAI,UAAU,OAAO,UAAU,CAAC;AAOhC,QAAI;AACJ,QAAI;AAEJ,aAAS,mBAAmB;AACxB,YAAM,IAAI,MAAM,iCAAiC;AAAA,IACrD;AACA,aAAS,sBAAuB;AAC5B,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACvD;AACA,KAAC,WAAY;AACT,UAAI;AACA,YAAI,OAAO,eAAe,YAAY;AAClC,6BAAmB;AAAA,QACvB,OAAO;AACH,6BAAmB;AAAA,QACvB;AAAA,MACJ,SAAS,GAAG;AACR,2BAAmB;AAAA,MACvB;AACA,UAAI;AACA,YAAI,OAAO,iBAAiB,YAAY;AACpC,+BAAqB;AAAA,QACzB,OAAO;AACH,+BAAqB;AAAA,QACzB;AAAA,MACJ,SAAS,GAAG;AACR,6BAAqB;AAAA,MACzB;AAAA,IACJ,GAAG;AACH,aAAS,WAAW,KAAK;AACrB,UAAI,qBAAqB,YAAY;AAEjC,eAAO,WAAW,KAAK,CAAC;AAAA,MAC5B;AAEA,WAAK,qBAAqB,oBAAoB,CAAC,qBAAqB,YAAY;AAC5E,2BAAmB;AACnB,eAAO,WAAW,KAAK,CAAC;AAAA,MAC5B;AACA,UAAI;AAEA,eAAO,iBAAiB,KAAK,CAAC;AAAA,MAClC,SAAQ,GAAE;AACN,YAAI;AAEA,iBAAO,iBAAiB,KAAK,MAAM,KAAK,CAAC;AAAA,QAC7C,SAAQA,IAAE;AAEN,iBAAO,iBAAiB,KAAK,MAAM,KAAK,CAAC;AAAA,QAC7C;AAAA,MACJ;AAAA,IAGJ;AACA,aAAS,gBAAgB,QAAQ;AAC7B,UAAI,uBAAuB,cAAc;AAErC,eAAO,aAAa,MAAM;AAAA,MAC9B;AAEA,WAAK,uBAAuB,uBAAuB,CAAC,uBAAuB,cAAc;AACrF,6BAAqB;AACrB,eAAO,aAAa,MAAM;AAAA,MAC9B;AACA,UAAI;AAEA,eAAO,mBAAmB,MAAM;AAAA,MACpC,SAAS,GAAE;AACP,YAAI;AAEA,iBAAO,mBAAmB,KAAK,MAAM,MAAM;AAAA,QAC/C,SAASA,IAAE;AAGP,iBAAO,mBAAmB,KAAK,MAAM,MAAM;AAAA,QAC/C;AAAA,MACJ;AAAA,IAIJ;AACA,QAAI,QAAQ,CAAC;AACb,QAAI,WAAW;AACf,QAAI;AACJ,QAAI,aAAa;AAEjB,aAAS,kBAAkB;AACvB,UAAI,CAAC,YAAY,CAAC,cAAc;AAC5B;AAAA,MACJ;AACA,iBAAW;AACX,UAAI,aAAa,QAAQ;AACrB,gBAAQ,aAAa,OAAO,KAAK;AAAA,MACrC,OAAO;AACH,qBAAa;AAAA,MACjB;AACA,UAAI,MAAM,QAAQ;AACd,mBAAW;AAAA,MACf;AAAA,IACJ;AAEA,aAAS,aAAa;AAClB,UAAI,UAAU;AACV;AAAA,MACJ;AACA,UAAI,UAAU,WAAW,eAAe;AACxC,iBAAW;AAEX,UAAI,MAAM,MAAM;AAChB,aAAM,KAAK;AACP,uBAAe;AACf,gBAAQ,CAAC;AACT,eAAO,EAAE,aAAa,KAAK;AACvB,cAAI,cAAc;AACd,yBAAa,UAAU,EAAE,IAAI;AAAA,UACjC;AAAA,QACJ;AACA,qBAAa;AACb,cAAM,MAAM;AAAA,MAChB;AACA,qBAAe;AACf,iBAAW;AACX,sBAAgB,OAAO;AAAA,IAC3B;AAEA,YAAQ,WAAW,SAAU,KAAK;AAC9B,UAAI,OAAO,IAAI,MAAM,UAAU,SAAS,CAAC;AACzC,UAAI,UAAU,SAAS,GAAG;AACtB,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC7B;AAAA,MACJ;AACA,YAAM,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC;AAC9B,UAAI,MAAM,WAAW,KAAK,CAAC,UAAU;AACjC,mBAAW,UAAU;AAAA,MACzB;AAAA,IACJ;AAGA,aAAS,KAAK,KAAK,OAAO;AACtB,WAAK,MAAM;AACX,WAAK,QAAQ;AAAA,IACjB;AACA,SAAK,UAAU,MAAM,WAAY;AAC7B,WAAK,IAAI,MAAM,MAAM,KAAK,KAAK;AAAA,IACnC;AACA,YAAQ,QAAQ;AAChB,YAAQ,UAAU;AAClB,YAAQ,MAAM,CAAC;AACf,YAAQ,OAAO,CAAC;AAChB,YAAQ,UAAU;AAClB,YAAQ,WAAW,CAAC;AAEpB,aAAS,OAAO;AAAA,IAAC;AAEjB,YAAQ,KAAK;AACb,YAAQ,cAAc;AACtB,YAAQ,OAAO;AACf,YAAQ,MAAM;AACd,YAAQ,iBAAiB;AACzB,YAAQ,qBAAqB;AAC7B,YAAQ,OAAO;AACf,YAAQ,kBAAkB;AAC1B,YAAQ,sBAAsB;AAE9B,YAAQ,YAAY,SAAU,MAAM;AAAE,aAAO,CAAC;AAAA,IAAE;AAEhD,YAAQ,UAAU,SAAU,MAAM;AAC9B,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACtD;AAEA,YAAQ,MAAM,WAAY;AAAE,aAAO;AAAA,IAAI;AACvC,YAAQ,QAAQ,SAAU,KAAK;AAC3B,YAAM,IAAI,MAAM,gCAAgC;AAAA,IACpD;AACA,YAAQ,QAAQ,WAAW;AAAE,aAAO;AAAA,IAAG;AAAA;AAAA;", "names": ["e"]}