import{d as h,a as _,Q as k,p as d,m as c,y as I,c as n,e as o,l as w,F as O,j as L,I as x,A as p,f as R,w as T,D as b,T as v}from"./vue.zNq9Glab.js";import{x as B}from"./index.GuQX7xXE.js";const D={class:"layout-padding layout-padding-unset layout-iframe"},P={class:"layout-padding-auto layout-padding-view"},S=["src","data-url"],V=h({name:"layoutIframeView"}),E=h({...V,props:{refreshKey:{type:String,default:()=>""},name:{type:String,default:()=>"slide-right"},list:{type:Array,default:()=>[]}},setup(f){const s=f,l=_(),r=k(),g=d(()=>s.list.filter(e=>{var t,i,a;if((t=e.meta)!=null&&t.isIframeOpen){const m=((i=e.meta)==null?void 0:i.isLink)||"";m.includes("{{token}}")&&(e.meta.isLink=m.replace("{{token}}",B.cookie.get("token")))}return(a=e.meta)==null?void 0:a.isIframeOpen})),y=d(()=>r.path),u=(e,t)=>{x(()=>{if(!l.value)return!1;l.value.forEach(i=>{i.dataset.url===e&&(i.onload=()=>{var a;(a=t.meta)!=null&&a.isIframeOpen&&t.meta.loading&&(t.meta.loading=!1)})})})};return c(()=>r.fullPath,e=>{const t=s.list.find(i=>i.path===e);if(!t)return!1;t.meta.isIframeOpen||(t.meta.isIframeOpen=!0),u(e,t)},{immediate:!0}),c(()=>s.refreshKey,()=>{const e=s.list.find(t=>t.path===r.path);if(!e)return!1;e.meta.isIframeOpen&&(e.meta.isIframeOpen=!1),setTimeout(()=>{e.meta.isIframeOpen=!0,e.meta.loading=!0,u(r.fullPath,e)})},{deep:!0}),(e,t)=>{const i=I("loading");return o(),n("div",D,[w("div",P,[(o(!0),n(O,null,L(g.value,a=>p((o(),n("div",{class:"w100",key:a.path,"element-loading-background":"white"},[R(v,{name:f.name,mode:"out-in"},{default:T(()=>[p((o(),n("iframe",{src:a.meta.isLink,key:a.path,frameborder:"0",height:"100%",width:"100%",style:{position:"absolute"},"data-url":a.path,ref_for:!0,ref_key:"iframeRef",ref:l},null,8,S)),[[b,y.value===a.path]])]),_:2},1032,["name"])])),[[i,a.meta.loading]])),128))])])}}});export{E as default};
