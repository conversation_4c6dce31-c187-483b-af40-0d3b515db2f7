import{r as a,d as m,v as n}from"./index.GuQX7xXE.js";import{a as o}from"./authFunction.BcROZVTX.js";const l="/api/plant-encyclopedia/plants/";function d(e){return a({url:l,method:"get",params:e})}function h(e){return a({url:l,method:"post",data:e})}function f(e){return a({url:l+e.id+"/",method:"put",data:e})}function w(e){return a({url:l+e+"/",method:"delete",data:{id:e}})}function y(e){return m({url:l+"export_data/",params:e,method:"get"})}function q(){return a({url:l+"category_stats/",method:"get"})}const x=[{value:"草本植物",label:"草本植物"},{value:"木本植物",label:"木本植物"},{value:"藤本植物",label:"藤本植物"},{value:"兰科植物",label:"兰科植物"},{value:"水生植物",label:"水生植物"},{value:"球根植物",label:"球根植物"},{value:"宿根植物",label:"宿根植物"}],g=[{value:0,label:"草稿",color:"info"},{value:1,label:"已发布",color:"success"},{value:2,label:"已下架",color:"warning"}];function b({crudExpose:e}){const c=async t=>await d(t),i=async({form:t,row:r})=>(r.id&&(t.id=r.id),await f(t)),s=async({row:t})=>await w(t.id),p=async({form:t})=>await h(t),u=async t=>await y(t);return{crudOptions:{request:{pageRequest:c,addRequest:p,editRequest:i,delRequest:s},actionbar:{buttons:{export:{show:o("PlantEncyclopedia:Export"),text:"导出",title:"导出植物百科数据",click(){return u(e.getSearchFormData())}},add:{show:o("PlantEncyclopedia:Create"),text:"新增植物"}}},search:{show:!0,initialForm:{},options:{layout:"multi-line"},col:{span:6},buttons:{search:{text:"搜索"},reset:{text:"重置"}}},rowHandle:{fixed:"right",width:250,buttons:{view:{type:"text",order:1,show:o("PlantEncyclopedia:Retrieve"),text:"查看"},edit:{type:"text",order:2,show:o("PlantEncyclopedia:Update"),text:"编辑"},copy:{type:"text",order:3,show:o("PlantEncyclopedia:Copy"),text:"复制"},remove:{type:"text",order:4,show:o("PlantEncyclopedia:Delete"),text:"删除"}}},columns:{id:{title:"ID",key:"id",type:"number",column:{width:80},form:{show:!1}},name:{title:"植物名称",type:"text",search:{show:!0},column:{width:150,sortable:!0},form:{rules:[{required:!0,message:"请输入植物名称"},{max:100,message:"植物名称不能超过100个字符"}],component:{placeholder:"请输入植物名称"}}},scientific_name:{title:"学名",type:"text",search:{show:!0},column:{width:180},form:{component:{placeholder:"请输入植物学名"}}},category:{title:"分类",type:"dict-select",search:{show:!0},dict:n({data:x}),column:{width:120,component:{name:"fs-dict-select",color:"auto"}},form:{rules:[{required:!0,message:"请选择植物分类"}],component:{placeholder:"请选择植物分类"}}},main_image_url:{title:"主图",type:"image-uploader",column:{width:100,component:{name:"el-image",style:{width:"60px",height:"60px"},fit:"cover",previewSrcList:t=>[t.row.main_image_url]}},form:{component:{name:"fs-file-uploader",accept:".jpg,.jpeg,.png,.gif",limit:1,uploader:{type:"form",buildUrl:()=>"/api/common/upload/"}}}},short_description:{title:"简短描述",type:"text",column:{width:200,ellipsis:!0},form:{show:!1}},status:{title:"状态",type:"dict-select",search:{show:!0},dict:n({data:g}),column:{width:100,component:{name:"fs-dict-select",color:"auto"}},form:{value:1,component:{placeholder:"请选择状态"}}},view_count:{title:"浏览次数",type:"number",column:{width:100,sortable:!0},form:{show:!1}},family:{title:"科",type:"text",column:{width:120,show:!1},form:{component:{placeholder:"请输入植物科名"}}},genus:{title:"属",type:"text",column:{width:120,show:!1},form:{component:{placeholder:"请输入植物属名"}}},description:{title:"植物描述",type:"textarea",column:{show:!1},form:{component:{name:"el-input",type:"textarea",rows:4,placeholder:"请输入植物详细描述"}}},care_tips:{title:"养护技巧",type:"textarea",column:{show:!1},form:{component:{name:"el-input",type:"textarea",rows:3,placeholder:"请输入养护技巧"}}},growth_habit:{title:"生长习性",type:"textarea",column:{show:!1},form:{component:{name:"el-input",type:"textarea",rows:3,placeholder:"请输入生长习性"}}},flowering_period:{title:"花期",type:"text",column:{width:120,show:!1},form:{component:{placeholder:"请输入花期，如：春季、3-5月"}}},images:{title:"图片列表",type:"file-uploader",column:{show:!1},form:{component:{name:"fs-file-uploader",accept:".jpg,.jpeg,.png,.gif",limit:10,multiple:!0,uploader:{type:"form",buildUrl:()=>"/api/common/upload/"}}}},source_url:{title:"来源URL",type:"text",column:{show:!1},form:{component:{placeholder:"请输入数据来源URL"}}},source_site:{title:"来源网站",type:"text",column:{width:100,show:!1},form:{value:"花百科",component:{placeholder:"请输入来源网站"}}},tags:{title:"标签",type:"text",column:{show:!1},form:{component:{placeholder:"请输入标签，多个标签用逗号分隔"},helper:"多个标签用逗号分隔，如：观叶植物,室内植物,净化空气"}},search_keywords:{title:"搜索关键词",type:"text",column:{show:!1},form:{component:{placeholder:"请输入搜索关键词"},helper:"用于提高搜索匹配度的关键词"}},create_datetime:{title:"创建时间",type:"datetime",column:{width:160,sortable:!0},form:{show:!1}}}}}}const O=Object.freeze(Object.defineProperty({__proto__:null,default:b},Symbol.toStringTag,{value:"Module"}));export{O as a,b as c,q as g};
