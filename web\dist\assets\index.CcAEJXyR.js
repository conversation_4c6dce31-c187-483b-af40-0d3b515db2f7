import{Q as j,a as I,e as K}from"./index.GuQX7xXE.js";import{g as L,c as P}from"./crud.BRDA4iHy.js";import{_ as G}from"./index.vue_vue_type_script_setup_true_name_importExcel_lang.BmTOIKpl.js";import{d as H,C as J,a as f,r as W,o as X,b as s,y as Y,g as p,e as n,w as l,h as Z,f as t,c as h,F as V,j as k,l as E,t as y,n as x,_ as ee,k as i,A as ae}from"./vue.zNq9Glab.js";import{_ as le}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./authFunction.BcROZVTX.js";const te=H({name:"PlantEncyclopedia",components:{importExcel:G,View:j},setup(){const a=J(),o={componentName:a==null?void 0:a.type.name},{crudBinding:M,crudRef:$,crudExpose:d,resetCrudOptions:N}=I({createCrudOptions:P,context:o}),C=f([]),m=f(!0),_=f(!1),b=f(""),S=f(""),F=f(""),w=f(!1),c=W({q:"",category:"",status:"",ordering:"-create_datetime"}),R=[{value:"草本植物",label:"草本植物"},{value:"木本植物",label:"木本植物"},{value:"藤本植物",label:"藤本植物"},{value:"兰科植物",label:"兰科植物"},{value:"水生植物",label:"水生植物"},{value:"球根植物",label:"球根植物"},{value:"宿根植物",label:"宿根植物"}],O=[{value:0,label:"草稿"},{value:1,label:"已发布"},{value:2,label:"已下架"}],r=async()=>{try{_.value=!0;const u=await L();u.code===2e3&&(C.value=u.data)}catch(u){console.error("获取统计数据失败:",u)}finally{_.value=!1}},v=()=>{r()},U=()=>{m.value=!m.value},g=()=>{b.value.trim()?(d.setSearchFormData({name:b.value.trim()}),d.doRefresh()):(d.setSearchFormData({name:void 0}),d.doRefresh())},q=()=>{d.setSearchFormData({category:S.value}),d.doRefresh()},A=()=>{d.setSearchFormData({status:F.value}),d.doRefresh()},T=()=>{w.value=!0},B=()=>{const u={...c};Object.keys(u).forEach(D=>{u[D]||delete u[D]}),d.setSearchFormData(u),d.doRefresh(),w.value=!1,K.success("搜索完成")},e=()=>{Object.assign(c,{q:"",category:"",status:"",ordering:"-create_datetime"})},z=u=>({0:"info",1:"success",2:"warning"})[u]||"info",Q=u=>({0:"草稿",1:"已发布",2:"已下架"})[u]||"未知";return X(()=>{d.doRefresh(),r()}),{crudBinding:M,crudRef:$,stats:C,showStats:m,statsLoading:_,quickSearch:b,categoryFilter:S,statusFilter:F,advancedSearchVisible:w,advancedSearchForm:c,categoryOptions:R,statusOptions:O,refreshStats:v,showStatsToggle:U,handleQuickSearch:g,handleCategoryFilter:q,handleStatusFilter:A,handleAdvancedSearch:T,executeAdvancedSearch:B,resetAdvancedSearch:e,getStatusType:z,getStatusText:Q}}}),oe={class:"stat-content"},se={class:"stat-number"},ne={class:"stat-label"},re={key:1,class:"no-image"},ue={class:"view-count"};function de(a,o,M,$,d,N){const C=s("el-card"),m=s("el-col"),_=s("el-row"),b=s("el-image"),S=s("el-tag"),F=s("View"),w=s("el-icon"),c=s("el-button"),R=s("importExcel"),O=s("el-input"),r=s("el-option"),v=s("el-select"),U=s("fs-crud"),g=s("el-form-item"),q=s("el-form"),A=s("el-dialog"),T=s("fs-page"),B=Y("auth");return n(),p(T,{class:"plant-encyclopedia-page"},{default:l(()=>[a.showStats?(n(),p(_,{key:0,gutter:20,class:"stats-row"},{default:l(()=>[(n(!0),h(V,null,k(a.stats,e=>(n(),p(m,{span:6,key:e.category},{default:l(()=>[t(C,{class:"stat-card"},{default:l(()=>[E("div",oe,[E("div",se,y(e.count),1),E("div",ne,y(e.category),1)])]),_:2},1024)]),_:2},1024))),128))]),_:1})):Z("",!0),t(U,x({ref:"crudRef"},a.crudBinding),{cell_main_image_url:l(e=>[e.row.main_image_url?(n(),p(b,{key:0,src:e.row.main_image_url,"preview-src-list":[e.row.main_image_url],style:{width:"60px",height:"60px"},fit:"cover",class:"plant-image"},null,8,["src","preview-src-list"])):(n(),h("span",re,"暂无图片"))]),cell_status:l(e=>[t(S,{type:a.getStatusType(e.row.status),size:"small"},{default:l(()=>[i(y(a.getStatusText(e.row.status)),1)]),_:2},1032,["type"])]),cell_category:l(e=>[t(S,{type:"info",size:"small",effect:"plain"},{default:l(()=>[i(y(e.row.category),1)]),_:2},1024)]),cell_view_count:l(e=>[E("span",ue,[t(w,null,{default:l(()=>[t(F)]),_:1}),i(" "+y(e.row.view_count||0),1)])]),"actionbar-right":l(()=>[t(c,{type:"primary",onClick:a.showStatsToggle,size:"small"},{default:l(()=>[i(y(a.showStats?"隐藏统计":"显示统计"),1)]),_:1},8,["onClick"]),t(c,{type:"success",onClick:a.refreshStats,size:"small",loading:a.statsLoading},{default:l(()=>o[9]||(o[9]=[i(" 刷新统计 ")])),_:1,__:[9]},8,["onClick","loading"]),ae((n(),p(R,{api:"api/plant-encyclopedia/plants/"},{default:l(()=>o[10]||(o[10]=[i(" 导入植物数据 ")])),_:1,__:[10]})),[[B,"PlantEncyclopedia:Import"]])]),"search-form-top":l(()=>[t(_,{gutter:20,class:"search-extra"},{default:l(()=>[t(m,{span:8},{default:l(()=>[t(O,{modelValue:a.quickSearch,"onUpdate:modelValue":o[0]||(o[0]=e=>a.quickSearch=e),placeholder:"快速搜索植物名称或学名",onKeyup:ee(a.handleQuickSearch,["enter"]),clearable:""},{append:l(()=>[t(c,{onClick:a.handleQuickSearch,icon:"Search"},null,8,["onClick"])]),_:1},8,["modelValue","onKeyup"])]),_:1}),t(m,{span:6},{default:l(()=>[t(v,{modelValue:a.categoryFilter,"onUpdate:modelValue":o[1]||(o[1]=e=>a.categoryFilter=e),placeholder:"选择分类",clearable:"",onChange:a.handleCategoryFilter},{default:l(()=>[(n(!0),h(V,null,k(a.categoryOptions,e=>(n(),p(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),t(m,{span:6},{default:l(()=>[t(v,{modelValue:a.statusFilter,"onUpdate:modelValue":o[2]||(o[2]=e=>a.statusFilter=e),placeholder:"选择状态",clearable:"",onChange:a.handleStatusFilter},{default:l(()=>[(n(!0),h(V,null,k(a.statusOptions,e=>(n(),p(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),t(m,{span:4},{default:l(()=>[t(c,{type:"primary",onClick:a.handleAdvancedSearch},{default:l(()=>o[11]||(o[11]=[i(" 高级搜索 ")])),_:1,__:[11]},8,["onClick"])]),_:1})]),_:1})]),_:1},16),t(A,{modelValue:a.advancedSearchVisible,"onUpdate:modelValue":o[8]||(o[8]=e=>a.advancedSearchVisible=e),title:"高级搜索",width:"600px"},{footer:l(()=>[t(c,{onClick:o[7]||(o[7]=e=>a.advancedSearchVisible=!1)},{default:l(()=>o[12]||(o[12]=[i("取消")])),_:1,__:[12]}),t(c,{onClick:a.resetAdvancedSearch},{default:l(()=>o[13]||(o[13]=[i("重置")])),_:1,__:[13]},8,["onClick"]),t(c,{type:"primary",onClick:a.executeAdvancedSearch},{default:l(()=>o[14]||(o[14]=[i("搜索")])),_:1,__:[14]},8,["onClick"])]),default:l(()=>[t(q,{model:a.advancedSearchForm,"label-width":"100px"},{default:l(()=>[t(g,{label:"关键词"},{default:l(()=>[t(O,{modelValue:a.advancedSearchForm.q,"onUpdate:modelValue":o[3]||(o[3]=e=>a.advancedSearchForm.q=e),placeholder:"搜索植物名称、学名、描述等"},null,8,["modelValue"])]),_:1}),t(g,{label:"分类"},{default:l(()=>[t(v,{modelValue:a.advancedSearchForm.category,"onUpdate:modelValue":o[4]||(o[4]=e=>a.advancedSearchForm.category=e),placeholder:"选择植物分类",clearable:""},{default:l(()=>[(n(!0),h(V,null,k(a.categoryOptions,e=>(n(),p(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"状态"},{default:l(()=>[t(v,{modelValue:a.advancedSearchForm.status,"onUpdate:modelValue":o[5]||(o[5]=e=>a.advancedSearchForm.status=e),placeholder:"选择状态",clearable:""},{default:l(()=>[(n(!0),h(V,null,k(a.statusOptions,e=>(n(),p(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"排序方式"},{default:l(()=>[t(v,{modelValue:a.advancedSearchForm.ordering,"onUpdate:modelValue":o[6]||(o[6]=e=>a.advancedSearchForm.ordering=e),placeholder:"选择排序方式"},{default:l(()=>[t(r,{label:"创建时间降序",value:"-create_datetime"}),t(r,{label:"创建时间升序",value:"create_datetime"}),t(r,{label:"浏览量降序",value:"-view_count"}),t(r,{label:"浏览量升序",value:"view_count"}),t(r,{label:"名称升序",value:"name"}),t(r,{label:"名称降序",value:"-name"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]),_:1})}const _e=le(te,[["render",de],["__scopeId","data-v-3397a526"]]);export{_e as default};
