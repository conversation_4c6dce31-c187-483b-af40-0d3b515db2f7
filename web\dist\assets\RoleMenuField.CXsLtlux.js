import{R as x,h as C}from"./api.Bbrb5cXl.js";import{R as M,a as $}from"./RoleMenuFieldStores.BbCdho5b.js";import{e as y}from"./index.GuQX7xXE.js";import{d as S,b as f,c as l,h as N,u as c,e as a,l as t,f as m,k,w as v,F as _,j as p,t as V}from"./vue.zNq9Glab.js";import{_ as U}from"./_plugin-vue_export-helper.DlAUqK2U.js";const B={key:0,class:"pccm-item"},D={class:"menu-form-alert"},E={class:"columns-list"},H={class:"columns-head"},I={class:"columns-content"},j={class:"width-txt"},z=S({__name:"RoleMenuField",setup(L){const d=M(),h=$(),R=x(),F=(r,s,u)=>{for(const n of d.$state)n[s]=n[u]?n[s]:r},b=async()=>{const r=await C(R.$state.roleId,d.$state);y({message:r.msg,type:"success"})};return(r,s)=>{const u=f("el-button"),n=f("el-checkbox");return c(d).$state.length>0?(a(),l("div",B,[t("div",D,[m(u,{size:"small",onClick:b},{default:v(()=>s[0]||(s[0]=[k("保存 ")])),_:1,__:[0]}),s[1]||(s[1]=k(" 配置数据列字段权限 "))]),t("ul",E,[t("li",H,[s[2]||(s[2]=t("div",{class:"width-txt"},[t("span",null,"字段")],-1)),(a(!0),l(_,null,p(c(h).$state,(e,i)=>(a(),l("div",{key:i,class:"width-check"},[m(n,{modelValue:e.checked,"onUpdate:modelValue":o=>e.checked=o,onChange:o=>F(o,e.value,e.disabled)},{default:v(()=>[t("span",null,V(e.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]))),128))]),t("div",I,[(a(!0),l(_,null,p(c(d).$state,(e,i)=>(a(),l("li",{key:i,class:"columns-item"},[t("div",j,V(e.title),1),(a(!0),l(_,null,p(c(h).$state,(o,g)=>(a(),l("div",{key:g,class:"width-check"},[m(n,{modelValue:e[o.value],"onUpdate:modelValue":w=>e[o.value]=w,class:"ci-checkout",disabled:e[o.disabled]},null,8,["modelValue","onUpdate:modelValue","disabled"])]))),128))]))),128))])])])):N("",!0)}}}),O=U(z,[["__scopeId","data-v-088391a4"]]);export{O as default};
