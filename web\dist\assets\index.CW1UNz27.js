const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.CtPCylKS.js","assets/index.GuQX7xXE.js","assets/vue.zNq9Glab.js","assets/index.WGbL7eFL.css","assets/crud.3b27k8HF.js"])))=>i.map(i=>d[i]);
import{a as P,b as T,c as F,_ as M,X as f,z as O,E as X,y as $}from"./index.GuQX7xXE.js";import{d as q,a as b,p as I,b as r,g as L,e as j,w as e,f as t,n as G,u as a,A as H,k as i,D as J,t as h,P as K,l as Q}from"./vue.zNq9Glab.js";import{a as W}from"./authFunction.BcROZVTX.js";import{r as Y,c as Z}from"./crud.DkvPVLeS.js";import{R as ee}from"./RoleUserStores.qMOBT--c.js";const ce=q({__name:"index",setup(te,{expose:w}){const d=ee(),R=K(()=>M(()=>import("./index.CtPCylKS.js"),__vite__mapDeps([0,1,2,3,4]))),m=b(),x=()=>{n.doRefresh()},y=b(!1),k=l=>{o.value=[],l()},u=I(()=>o.value.length),D=l=>{const s=n.getBaseTableRef(),_=n.getTableData();f.pluck(_,"id").includes(l.id)?s.toggleRowSelection(l,!1):o.value=f.remove(o.value,c=>c.id!==l.id)},C=async()=>{if(o.value.length<1){O("请先勾选用户");return}await X.confirm(`确定要删除这 “${o.value.length}” 位用户的权限吗`,"确认");const l=await Y(g.value.getSearchFormData().role_id,f.pluck(o.value,"id"));o.value=[],$(l.msg),n.doRefresh()},{crudBinding:V,crudRef:g,crudExpose:n,selectedRows:o}=P({createCrudOptions:Z,context:{subUserRef:m}}),{setSearchFormData:U,doRefresh:S}=n;return w({drawer:y,setSearchFormData:U,doRefresh:S}),(l,s)=>{const _=r("el-tag"),c=r("el-button"),v=r("el-table-column"),B=r("el-table"),E=r("el-popover"),z=r("el-tooltip"),A=r("fs-crud"),N=r("el-drawer");return j(),L(N,{size:"70%",modelValue:a(d).drawerVisible,"onUpdate:modelValue":s[0]||(s[0]=p=>a(d).drawerVisible=p),direction:"rtl","destroy-on-close":"","before-close":k},{header:e(()=>[Q("div",null,[s[1]||(s[1]=i(" 当前授权角色： ")),t(_,null,{default:e(()=>[i(h(a(d).role_name),1)]),_:1})])]),default:e(()=>[t(A,G({ref_key:"crudRef",ref:g},a(V)),{"pagination-right":e(()=>[t(E,{placement:"top",width:200,trigger:"click"},{reference:e(()=>[t(c,{text:"",type:u.value>0?"primary":""},{default:e(()=>[i("已选中"+h(u.value)+"条数据",1)]),_:1},8,["type"])]),default:e(()=>[t(B,{data:a(o),size:"small","max-height":500},{default:e(()=>[t(v,{width:"100",property:"name",label:"用户名"}),t(v,{fixed:"right",label:"操作","min-width":"60"},{default:e(p=>[t(c,{text:"",type:"info",icon:a(F),onClick:oe=>D(p.row),circle:""},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),"pagination-left":e(()=>[t(z,{content:"批量删除所选择的用户权限"},{default:e(()=>[H(t(c,{type:"danger",onClick:C,icon:a(T)},{default:e(()=>s[2]||(s[2]=[i("批量删除")])),_:1,__:[2]},8,["icon"]),[[J,u.value>0&&a(W)("role:AuthorizedDel")]])]),_:1})]),_:1},16),t(a(R),{ref_key:"subUserRef",ref:m,refreshCallback:x},null,512)]),_:1},8,["modelValue"])}}});export{ce as default};
