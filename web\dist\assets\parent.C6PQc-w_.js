const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/iframes.Dfj-JuBH.js","assets/vue.zNq9Glab.js","assets/index.GuQX7xXE.js","assets/index.WGbL7eFL.css"])))=>i.map(i=>d[i]);
import{n as I,u as P,l as w,S as x,_ as B}from"./index.GuQX7xXE.js";import{d as g,Q as D,R as E,M as d,r as S,a as M,p as f,N as O,I as c,o as U,O as b,m as Q,b as j,c as q,e as v,f as o,w as h,S as _,g as V,h as z,U as F,A as R,s as G,D as y,u as H,P as J,V as W}from"./vue.zNq9Glab.js";const X={class:"layout-parent"},Y=g({name:"layoutParentView"}),ae=g({...Y,setup(Z){const K=J(()=>B(()=>import("./iframes.Dfj-JuBH.js"),__vite__mapDeps([0,1,2,3]))),t=D(),A=E(),N=I(),k=P(),{keepAliveNames:i,cachedViews:n}=d(N),{themeConfig:u}=d(k),e=S({refreshRouterViewKey:"",iframeRefreshKey:"",keepAliveNameList:[],iframeList:[]}),m=M(!0);W("refreshView",function(){m.value=!1,c(()=>{m.value=!0})});const l=f(()=>u.value.animation),L=f(()=>(console.log(n.value),u.value.isTagsview?n.value:e.keepAliveNameList)),p=f(()=>t.meta.isIframe),T=async()=>{A.getRoutes().forEach(s=>{s.meta.isIframe&&(s.meta.isIframeOpen=!1,s.meta.loading=!0,e.iframeList.push({...s}))})};return O(()=>{e.keepAliveNameList=i.value,w.on("onTagsViewRefreshRouterView",s=>{e.keepAliveNameList=i.value.filter(a=>t.name!==a),e.refreshRouterViewKey="",e.iframeRefreshKey="",c(()=>{e.refreshRouterViewKey=s,e.iframeRefreshKey=s,e.keepAliveNameList=i.value})})}),U(()=>{T(),c(()=>{setTimeout(()=>{if(u.value.isCacheTagsView){let s=x.get("tagsViewList")||[];n.value=s.filter(a=>{var r;return(r=a.meta)==null?void 0:r.isKeepAlive}).map(a=>a.name)}},0)})}),b(()=>{w.off("onTagsViewRefreshRouterView",()=>{})}),Q(()=>t.fullPath,()=>{e.refreshRouterViewKey=decodeURI(t.fullPath)},{immediate:!0}),(s,a)=>{const r=j("router-view");return v(),q("div",X,[o(r,null,{default:h(({Component:C})=>[o(_,{name:l.value,mode:"out-in"},{default:h(()=>[m.value?(v(),V(F,{key:0,include:L.value},[R((v(),V(G(C),{key:e.refreshRouterViewKey,class:"w100"})),[[y,!p.value]])],1032,["include"])):z("",!0)]),_:2},1032,["name"])]),_:1}),o(_,{name:l.value,mode:"out-in"},{default:h(()=>[R(o(H(K),{class:"w100",refreshKey:e.iframeRefreshKey,name:l.value,list:e.iframeList},null,8,["refreshKey","name","list"]),[[y,p.value]])]),_:1},8,["name"])])}}});export{ae as default};
