import{R as h,a as p,s as R,b as k}from"./api.Bbrb5cXl.js";import{R as M}from"./RoleMenuTreeStores.BC0MttXT.js";import{R as _}from"./RoleMenuBtnStores.Dix3hi05.js";import{R as g,a as C}from"./RoleMenuFieldStores.BbCdho5b.js";import{X as y,e as S}from"./index.GuQX7xXE.js";import{d as I,a as c,o as v,b as T,g as b,e as w}from"./vue.zNq9Glab.js";const A=I({__name:"RoleMenuTree",setup(B){const o=h(),d=M(),s=_(),l=g(),u=C(),a=c([]),r=c([]),i={children:"children",label:"name",value:"id"},m=(e,t)=>{R({roleId:o.roleId,menuId:e.id,isCheck:t}).then(n=>{S({message:n.msg,type:"success"})})},f=async e=>{if(e.is_catalog)s.setState([]),l.setState([]);else{d.setRoleMenuTree(e);const{data:t}=await k({roleId:o.roleId,menuId:e.id});s.setState(t.menu_btn),l.setState(t.menu_field)}u.$reset()};return v(async()=>{a.value=await p({roleId:o.roleId}),r.value=y.toTreeArray(a.value).filter(e=>e.isCheck).map(e=>e.id)}),(e,t)=>{const n=T("el-tree");return w(),b(n,{ref:"treeRef",data:a.value,props:i,"default-checked-keys":r.value,onCheckChange:m,onNodeClick:f,"node-key":"id","check-strictly":"","highlight-current":"","show-checkbox":"","default-expand-all":"","check-on-click-leaf":!1},null,8,["data","default-checked-keys"])}}});export{A as default};
