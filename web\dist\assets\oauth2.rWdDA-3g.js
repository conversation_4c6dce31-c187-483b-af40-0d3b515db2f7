import{b as i}from"./api.DhPSFOaZ.js";import{d as r,r as d,o as p,a1 as u,c as s,h,e as c,l as t,F as m,j as _,t as k}from"./vue.zNq9Glab.js";import{_ as f}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./index.GuQX7xXE.js";const g=r({name:"loginOAuth2",setup(){const e=d({handleOAuth2LoginClick:o=>{history.replaceState(null,"",location.pathname+location.search),window.location.href=o.authentication_url+"?next="+window.location.href},backends:[]}),l=async()=>{i().then(o=>{e.backends=o.data})};return p(()=>{l()}),{...u(e)}}}),y={key:0,class:"other-fast-way"},C={class:"fast-list"},b=["onClickOnce"],w=["src","alt"];function O(a,e,l,o,$,B){return a.backends.length?(c(),s("div",y,[e[0]||(e[0]=t("div",{class:"fast-title"},[t("span",null,"其他快速方式登录")],-1)),t("ul",C,[(c(!0),s(m,null,_(a.backends,(n,A)=>(c(),s("li",{key:n},[t("a",{onClickOnce:L=>a.handleOAuth2LoginClick(n),style:{width:"50px",color:"#18bc9c"}},[t("img",{src:n.icon,alt:n.app_name},null,8,w),t("p",null,k(n.app_name),1)],40,b)]))),128))])])):h("",!0)}const V=f(g,[["render",O],["__scopeId","data-v-70c9ae26"]]);export{V as default};
