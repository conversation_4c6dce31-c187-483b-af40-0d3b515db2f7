import{j as F,k as V}from"./index.GuQX7xXE.js";import{d as w,c as s,e as t,F as d,j as p,l as g,t as y,n,g as x,w as B,s as P,p as v,a as U,m as _}from"./vue.zNq9Glab.js";const C=w({name:"FsFilesFormat",props:{modelValue:{},color:{default:""},type:{default:"tag"},a:{},tag:{},buildUrl:{},buildUrls:{},getFileName:{}},setup(a,b){const{ui:i}=V(),f=v(()=>a.getFileName||function(e){return typeof e!="string"?(console.warn("获取文件名失败，请配置getFileName"),e):(e==null?void 0:e.lastIndexOf("/"))>=0?e.substring(e.lastIndexOf("/")+1):e});function u(e){return{url:void 0,value:e,name:f.value(e),color:a.color}}async function m(e){if(a.buildUrls){const r=e.map(o=>o.value),k=await a.buildUrls(r);for(let o=0;o<e.length;o++)e[o].url=k[o]}else if(a.buildUrl)for(let r of e)r.url=await a.buildUrl(r.value);else for(let r=0;r<e.length;r++)e[r].url=e[r].value}async function l(){if(a.modelValue==null||a.modelValue==="")return[];let e=[];if(typeof a.modelValue=="string")e=[u(a.modelValue)];else if(a.modelValue instanceof Array){e=[];for(const r of a.modelValue)e.push(u(r))}return await m(e),e}const c=U([]);_(()=>a.modelValue,async()=>{c.value=await l()},{immediate:!0});const h=v(()=>({...a}));return{ui:i,itemsRef:c,computedProps:h}}}),N={class:"fs-files-format"},R=["href"],O=["href"];function j(a,b,i,f,u,m){return t(),s("div",N,[a.computedProps.type==="text"?(t(!0),s(d,{key:0},p(a.itemsRef,l=>(t(),s("span",{key:l.url,class:"fs-files-item"},[g("a",n({href:l.url,target:"_blank",ref_for:!0},a.computedProps.a),y(l.name),17,R)]))),128)):(t(!0),s(d,{key:1},p(a.itemsRef,l=>(t(),x(P(a.ui.tag.name),n({key:l.url,class:"fs-tag-item",color:l.color,ref_for:!0},a.computedProps.tag),{default:B(()=>[g("a",n({href:l.url,target:"_blank",ref_for:!0},a.computedProps.a),y(l.name),17,O)]),_:2},1040,["color"]))),128))])}const $=F(C,[["render",j]]);export{$ as default};
