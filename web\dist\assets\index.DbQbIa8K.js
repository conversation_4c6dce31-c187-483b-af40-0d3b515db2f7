import{t as p,w as m}from"./index.GuQX7xXE.js";import{createCrudOptions as i}from"./crud.DdAOUNbp.js";import{d as c,a as r,o as g,b as o,g as x,e as w,w as t,f as a,n as C,k,t as h}from"./vue.zNq9Glab.js";const y=c({name:"downloadCenter"}),b=c({...y,setup(B){const s=r(),n=r(),{crudExpose:e}=p({crudRef:s,crudBinding:n}),{crudOptions:_}=i({crudExpose:e});return m({crudExpose:e,crudOptions:_,context:{}}),g(async()=>{e.doRefresh()}),(R,v)=>{const d=o("el-tag"),u=o("fs-crud"),f=o("fs-page");return w(),x(f,null,{default:t(()=>[a(u,C({ref_key:"crudRef",ref:s},n.value),{cell_url:t(l=>[a(d,{size:"small"},{default:t(()=>[k(h(l.row.url),1)]),_:2},1024)]),_:1},16)]),_:1})}}});export{b as default};
