import{r as o,d as y,A as b,v as a,p as x,E as v,s as u}from"./index.GuQX7xXE.js";import{d as i}from"./dictionary.DBJS--kg.js";import{a as n}from"./authFunction.BcROZVTX.js";import{M as _,p as c}from"./vue.zNq9Glab.js";import{M as q}from"./md5.DLPczxzP.js";import{c as R}from"./commonCrud.DFvADd-j.js";const s="/api/system/user/";function G(t){return o({url:"/api/system/dept/all_dept/",method:"get",params:t})}function T(t){return o({url:s,method:"get",params:t})}function C(t){return o({url:s,method:"post",data:t})}function d(t){return o({url:s+t.id+"/",method:"put",data:t})}function D(t){return o({url:s+t+"/",method:"delete",data:{id:t}})}function S(t){return y({url:s+"export_data/",params:t,method:"get"})}function M(t){return o({url:s+t+"/reset_to_default_password/",method:"put"})}const O=function({crudExpose:t}){const m=async e=>await T(e),p=async({form:e,row:r})=>(e.id=r.id,await d(e)),h=async({row:e})=>await D(e.id),f=async({form:e})=>await C(e),w=async e=>{await M(e.id),u("重置密码成功")},g=b(),{systemConfig:l}=_(g);return c(()=>l.value),{crudOptions:{table:{remove:{confirmMessage:"是否删除该用户？"}},request:{pageRequest:m,addRequest:f,editRequest:p,delRequest:h},form:{initialForm:{password:c(()=>l.value["base.default_password"])}},actionbar:{buttons:{add:{show:n("user:Create")},export:{text:"导出",title:"导出",show:n("user:Export"),click:e=>v.confirm("确定导出数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>S(e.row))}}},rowHandle:{fixed:"right",width:200,buttons:{view:{show:!1},edit:{iconRight:"Edit",type:"text",show:n("user:Update")},remove:{iconRight:"Delete",type:"text",show:n("user:Delete")},custom:{text:"重设密码",type:"text",show:n("user:ResetPassword"),tooltip:{placement:"top",content:"重设密码"},click:e=>{const{row:r}=e;w(r)}}}},columns:{_index:{title:"序号",form:{show:!1},column:{type:"index",align:"center",width:"70px",columnSetDisabled:!0}},username:{title:"账号",search:{show:!0},type:"input",column:{minWidth:100},form:{rules:[{required:!0,message:"账号必填项"}],component:{placeholder:"请输入账号"}}},password:{title:"密码",type:"password",column:{show:!1},editForm:{show:!1},form:{rules:[{required:!0,message:"密码必填项"}],component:{span:12,showPassword:!0,placeholder:"请输入密码"}},valueResolve({form:e}){e.password&&(e.password=q.hashStr(e.password))}},name:{title:"姓名",search:{show:!0},type:"input",column:{minWidth:100},form:{rules:[{required:!0,message:"姓名必填项"}],component:{span:12,placeholder:"请输入姓名"}}},dept:{title:"部门",search:{disabled:!0},type:"dict-tree",dict:a({isTree:!0,url:"/api/system/dept/all_dept/",value:"id",label:"name"}),column:{minWidth:200,formatter({value:e,row:r,index:W}){return r.dept_name_all}},form:{rules:[{required:!0,message:"必填项"}],component:{filterable:!0,placeholder:"请选择",props:{checkStrictly:!0,props:{value:"id",label:"name"}}}}},role:{title:"角色",search:{disabled:!0},type:"dict-select",dict:a({url:"/api/system/role/",value:"id",label:"name"}),column:{minWidth:200},form:{rules:[{required:!0,message:"必填项"}],component:{multiple:!0,filterable:!0,placeholder:"请选择角色"}}},mobile:{title:"手机号码",search:{show:!0},type:"input",column:{minWidth:120},form:{rules:[{max:20,message:"请输入正确的手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码"}],component:{placeholder:"请输入手机号码"}}},email:{title:"邮箱",column:{width:260},form:{rules:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],component:{placeholder:"请输入邮箱"}}},gender:{title:"性别",type:"dict-select",dict:a({data:i("gender")}),form:{value:1,component:{span:12}},component:{props:{color:"auto"}}},user_type:{title:"用户类型",search:{show:!0},type:"dict-select",dict:a({data:i("user_type")}),column:{minWidth:100},form:{show:!1,value:0,component:{span:12}}},is_active:{title:"状态",search:{show:!0},type:"dict-radio",column:{component:{name:"fs-dict-switch",activeText:"",inactiveText:"",style:"--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6",onChange:x(e=>()=>{d(e.row).then(r=>{u(r.msg)})})}},dict:a({data:i("button_status_bool")})},avatar:{title:"头像",type:"avatar-uploader",align:"center",form:{show:!1},column:{minWidth:100}},...R({dept_belong_id:{form:!0,table:!0}})}}}},A=Object.freeze(Object.defineProperty({__proto__:null,createCrudOptions:O},Symbol.toStringTag,{value:"Module"}));export{G,A as a,O as c};
