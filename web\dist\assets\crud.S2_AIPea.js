import{r as u,v as y,X as r}from"./index.GuQX7xXE.js";import{a as i}from"./authFunction.BcROZVTX.js";import{a as v,I as x}from"./vue.zNq9Glab.js";const s="/api/system/column/";function D(t){return u({url:s,method:"get",params:t})}function q(t){return u({url:s,method:"post",data:t})}function R(t){return u({url:s+t.id+"/",method:"put",data:t})}function S(t){return u({url:s+t+"/",method:"delete",data:{id:t}})}function C(t){return u({url:s+"multiple_delete/",method:"delete",data:{keys:t}})}function M(){return u({url:"/api/system/column/get_models/",method:"get"})}const _=function({crudExpose:t,props:O,modelDialog:m,selectOptions:c,allModelData:k}){const p=async e=>{if(c.value.id)return await D({menu:c.value.id})},f=async({form:e,row:l})=>(e.id=l.id,await R(e)),h=async({row:e})=>await S(e.id),g=async({form:e})=>(e.menu=c.value.id,await q(e)),n=v([]),b=e=>{const o=t.getTableData().filter(a=>!e.includes(a));r.arrayEach(e,a=>{r.pluck(n.value,"id").includes(a.id)||(n.value=r.union(n.value,[a]))}),r.arrayEach(o,a=>{n.value=r.remove(n.value,d=>d.id!==a.id)})},w=()=>{const e=t.getBaseTableRef(),l=t.getTableData(),o=r.filter(l,a=>r.pluck(n.value,"id").includes(a.id));x(()=>{r.arrayEach(o,a=>{e.toggleRowSelection(a,!0)})})};return{selectedRows:n,crudOptions:{request:{pageRequest:p,addRequest:g,editRequest:f,delRequest:h},pagination:{show:!1},actionbar:{buttons:{add:{show:i("column:Create")},auto:{text:"自动匹配",type:"success",show:i("column:Match"),click:()=>m.value=!0}}},rowHandle:{fixed:"right",buttons:{view:{show:!1},edit:{show:i("column:Update")},remove:{show:i("column:Delete")}}},form:{col:{span:24},labelWidth:"110px",wrapper:{is:"el-dialog",width:"600px"}},table:{rowKey:"id",onSelectionChange:b,onRefreshed:()=>w()},columns:{$checked:{title:"选择",form:{show:!1},column:{type:"selection",align:"center",width:"70px",columnSetDisabled:!0}},_index:{title:"序号",form:{show:!1},column:{align:"center",width:"70px",columnSetDisabled:!0,formatter:e=>{let l=e.index??1,o=t.crudBinding.value.pagination;return((o.currentPage??1)-1)*o.pageSize+l+1}}},model:{title:"model",type:"dict-select",dict:y({url:"/api/system/column/get_models/",label:"title",value:"key"}),column:{sortable:!0},form:{rules:[{required:!0,message:"必填项"}],component:{span:12,showSearch:!0,filterable:!0,filterOption(e,l){return l.label.indexOf(e)>=0||l.value.indexOf(e)>=0}}}},title:{title:"中文名",sortable:"custom",search:{show:!0},type:"text",form:{rules:[{required:!0,message:"必填项"}],component:{span:12,placeholder:"请输入中文名"}}},field_name:{title:"字段名",type:"text",search:{show:!0},column:{sortable:!0},form:{rules:[{required:!0,message:"必填项"}],component:{span:12,placeholder:"请输入字段名"}}}}}}},P=Object.freeze(Object.defineProperty({__proto__:null,createCrudOptions:_},Symbol.toStringTag,{value:"Module"}));export{C as B,P as a,_ as c,M as g};
