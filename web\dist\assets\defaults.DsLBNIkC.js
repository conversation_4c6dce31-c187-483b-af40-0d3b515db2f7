const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/aside.D_PtffXs.js","assets/index.GuQX7xXE.js","assets/vue.zNq9Glab.js","assets/index.WGbL7eFL.css","assets/header.BPbW56P8.js","assets/main.Mab_8bra.js"])))=>i.map(i=>d[i]);
import{u as h,N as C,_ as a}from"./index.GuQX7xXE.js";import{d as p,a as _,Q as L,M,o as g,m as i,b as f,g as k,e as w,w as n,f as e,u as s,P as r,I as E}from"./vue.zNq9Glab.js";const S=p({name:"layoutDefaults"}),V=p({...S,setup(x){const d=r(()=>a(()=>import("./aside.D_PtffXs.js"),__vite__mapDeps([0,1,2,3]))),m=r(()=>a(()=>import("./header.BPbW56P8.js"),__vite__mapDeps([4,1,2,3]))),y=r(()=>a(()=>import("./main.Mab_8bra.js"),__vite__mapDeps([5,1,2,3]))),o=_(""),t=_(),R=L(),v=h(),{themeConfig:T}=M(v),l=()=>{o.value.update(),t.value.layoutMainScrollbarRef.update()},c=()=>{E(()=>{setTimeout(()=>{l(),o.value.wrapRef.scrollTop=0,t.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return g(()=>{c(),C.done(600)}),i(()=>R.path,()=>{c()}),i(T,()=>{l()},{deep:!0}),(A,P)=>{const b=f("el-scrollbar"),u=f("el-container");return w(),k(u,{class:"layout-container"},{default:n(()=>[e(s(d)),e(u,{class:"layout-container-view h100"},{default:n(()=>[e(b,{ref_key:"layoutScrollbarRef",ref:o,class:"layout-backtop"},{default:n(()=>[e(s(m)),e(s(y),{ref_key:"layoutMainRef",ref:t},null,512)]),_:1},512)]),_:1})]),_:1})}}});export{V as default};
