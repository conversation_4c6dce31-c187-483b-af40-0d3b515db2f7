import {
  get_default,
  merge_default,
  set_default
} from "./chunk-LK7GAOJV.js";
import {
  computed,
  createVNode,
  defineComponent,
  ref,
  resolveComponent
} from "./chunk-WEJJSMSC.js";

// node_modules/@fast-crud/ui-interface/dist/ui-interface.mjs
var F = class {
  constructor() {
    this.ref = ref(null);
  }
  set(o) {
    this.ref.value = o;
  }
  get() {
    if (this.ref.value == null)
      throw new Error("您还未设置ui,第一步：先安装依赖@fast-crud/ui-interface,然后在use(FastCrud)前安装ui，app.use(UiXxx)；第二步：如果是NaiveUI，还需要用fs-ui-context包裹router-view，请参考http://fast-crud.docmirror.cn/guide/start/integration.html#_5-naiveui%E7%9A%84%E9%A2%9D%E5%A4%96%E6%93%8D%E4%BD%9C");
    return this.ref.value;
  }
};
var i = new F();
function B() {
  return {
    uiContext: i,
    ui: i.get(),
    set: (r) => {
      i.set(r);
    }
  };
}
var d = (r) => {
  const o = typeof r.is == "string" ? resolveComponent(r.is) : r.is;
  return createVNode(o, r.props, r.slots);
};
var l = (r, o) => d(r.builder(o));
var c = (r, o, t) => {
  const n = {}, s = r.modelValue, e = o.vModel;
  s != null && e && (e != null && e.get && (e != null && e.set) ? (n[s] = e.get(), n[`onUpdate:${s}`] = (u) => {
    e.set(u), e.onChange && e.onChange(u);
  }) : e.ref && e.key ? (n[s] = get_default(e.ref, e.key), n[`onUpdate:${s}`] = (u) => {
    set_default(e.ref, e.key, u), e.onChange && e.onChange(u);
  }) : console.warn("vModel配置错误:", r, e));
  const a = {
    is: o.is || r.name,
    props: o.props,
    slots: o.slots
  }, p = t instanceof Function ? t() : t;
  return merge_default({
    props: n
  }, p, a);
};
var v = (r, o = {}) => {
  const t = {
    ...r
  };
  return t.render = (n) => l(t, n), t.builder || (t.builder = (n) => c(t, n, o)), t.buildProps = (n) => t.builder(n).props, t.builderComputed = (n) => computed(() => t.builder(n)), t;
};
function R() {
  return {
    creator: v,
    doRenderComponent: d,
    renderComponent: l,
    buildBinding: c
  };
}
var k = defineComponent({
  name: "FsUiRender",
  props: {
    renderFn: {
      type: Function,
      default() {
        return () => null;
      }
    }
  },
  setup(r) {
    return () => r.renderFn();
  }
});

export {
  F,
  i,
  B,
  d,
  l,
  c,
  v,
  R,
  k
};
//# sourceMappingURL=chunk-TGOZU523.js.map
