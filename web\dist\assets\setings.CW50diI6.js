import{e as O,a6 as he,a7 as Ce,Z as ce,u as xe,L as z,l as q,O as _e,ah as we}from"./index.GuQX7xXE.js";import{d as me,M as Ve,r as Te,p as Se,o as $e,I as Be,O as Ee,b as M,c as Me,e as ke,f as u,w,l as a,k as L,t as c,q as A,v as D}from"./vue.zNq9Glab.js";import{f as ne}from"./formatTime.in1fXasu.js";import{_ as Ie}from"./_plugin-vue_export-helper.DlAUqK2U.js";function J(){return{hexToRgb:v=>{let f="";if(!/^\#?[0-9A-Fa-f]{6}$/.test(v))return O.warning("输入错误的hex"),"";v=v.replace("#",""),f=v.match(/../g);for(let o=0;o<3;o++)f[o]=parseInt(f[o],16);return f},rgbToHex:(v,f,b)=>{let o=/^\d{1,3}$/;if(!o.test(v)||!o.test(f)||!o.test(b))return O.warning("输入错误的rgb颜色值"),"";let d=[v.toString(16),f.toString(16),b.toString(16)];for(let e=0;e<3;e++)d[e].length==1&&(d[e]=`0${d[e]}`);return`#${d.join("")}`},getDarkColor:(v,f)=>{if(!/^\#?[0-9A-Fa-f]{6}$/.test(v))return O.warning("输入错误的hex颜色值"),"";let o=J().hexToRgb(v);for(let d=0;d<3;d++)o[d]=Math.floor(o[d]*(1-f));return J().rgbToHex(o[0],o[1],o[2])},getLightColor:(v,f)=>{if(!/^\#?[0-9A-Fa-f]{6}$/.test(v))return O.warning("输入错误的hex颜色值"),"";let o=J().hexToRgb(v);for(let d=0;d<3;d++)o[d]=Math.floor((255-o[d])*f+o[d]);return J().rgbToHex(o[0],o[1],o[2])}}}const de=$=>{const B="1.23452384164.123412416";document.getElementById(B)!==null&&document.body.removeChild(document.getElementById(B));const V=document.createElement("canvas");V.width=200,V.height=130;const T=V.getContext("2d");T.rotate(-20*Math.PI/180),T.font="12px Vedana",T.fillStyle="rgba(200, 200, 200, 0.30)",T.textBaseline="middle",T.fillText($,V.width/10,V.height/2);const v=document.createElement("div");return v.id=B,v.style.pointerEvents="none",v.style.top="0px",v.style.left="0px",v.style.position="fixed",v.style.zIndex="10000000",v.style.width=`${document.documentElement.clientWidth}px`,v.style.height=`${document.documentElement.clientHeight}px`,v.style.background=`url(${V.toDataURL("image/png")}) left top repeat`,document.body.appendChild(v),B},re={set:$=>{let B=de($);document.getElementById(B)===null&&(B=de($))},del:()=>{let $="1.23452384164.123412416";document.getElementById($)!==null&&document.body.removeChild(document.getElementById($))}};var fe={exports:{}};/*!
* clipboard.js v2.0.11
* https://clipboardjs.com/
*
* Licensed MIT © Zeno Rocha
*/(function($,B){(function(T,v){$.exports=v()})(Ce,function(){return function(){var V={686:function(f,b,o){o.d(b,{default:function(){return se}});var d=o(279),e=o.n(d),y=o(370),m=o.n(y),p=o(817),S=o.n(p);function h(l){try{return document.execCommand(l)}catch{return!1}}var _=function(t){var n=S()(t);return h("cut"),n},C=_;function I(l){var t=document.documentElement.getAttribute("dir")==="rtl",n=document.createElement("textarea");n.style.fontSize="12pt",n.style.border="0",n.style.padding="0",n.style.margin="0",n.style.position="absolute",n.style[t?"right":"left"]="-9999px";var i=window.pageYOffset||document.documentElement.scrollTop;return n.style.top="".concat(i,"px"),n.setAttribute("readonly",""),n.value=l,n}var X=function(t,n){var i=I(t);n.container.appendChild(i);var r=S()(i);return h("copy"),i.remove(),r},K=function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body},i="";return typeof t=="string"?i=X(t,n):t instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(t==null?void 0:t.type)?i=X(t.value,n):(i=S()(t),h("copy")),i},G=K;function P(l){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?P=function(n){return typeof n}:P=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},P(l)}var Q=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=t.action,i=n===void 0?"copy":n,r=t.container,g=t.target,k=t.text;if(i!=="copy"&&i!=="cut")throw new Error('Invalid "action" value, use either "copy" or "cut"');if(g!==void 0)if(g&&P(g)==="object"&&g.nodeType===1){if(i==="copy"&&g.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if(i==="cut"&&(g.hasAttribute("readonly")||g.hasAttribute("disabled")))throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`)}else throw new Error('Invalid "target" value, use a valid Element');if(k)return G(k,{container:r});if(g)return i==="cut"?C(g):G(g,{container:r})},ee=Q;function U(l){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?U=function(n){return typeof n}:U=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},U(l)}function Z(l,t){if(!(l instanceof t))throw new TypeError("Cannot call a class as a function")}function N(l,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}function te(l,t,n){return t&&N(l.prototype,t),n&&N(l,n),l}function R(l,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");l.prototype=Object.create(t&&t.prototype,{constructor:{value:l,writable:!0,configurable:!0}}),t&&F(l,t)}function F(l,t){return F=Object.setPrototypeOf||function(i,r){return i.__proto__=r,i},F(l,t)}function ae(l){var t=x();return function(){var i=H(l),r;if(t){var g=H(this).constructor;r=Reflect.construct(i,arguments,g)}else r=i.apply(this,arguments);return le(this,r)}}function le(l,t){return t&&(U(t)==="object"||typeof t=="function")?t:Y(l)}function Y(l){if(l===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l}function x(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function H(l){return H=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},H(l)}function j(l,t){var n="data-clipboard-".concat(l);if(t.hasAttribute(n))return t.getAttribute(n)}var oe=function(l){R(n,l);var t=ae(n);function n(i,r){var g;return Z(this,n),g=t.call(this),g.resolveOptions(r),g.listenClick(i),g}return te(n,[{key:"resolveOptions",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.action=typeof r.action=="function"?r.action:this.defaultAction,this.target=typeof r.target=="function"?r.target:this.defaultTarget,this.text=typeof r.text=="function"?r.text:this.defaultText,this.container=U(r.container)==="object"?r.container:document.body}},{key:"listenClick",value:function(r){var g=this;this.listener=m()(r,"click",function(k){return g.onClick(k)})}},{key:"onClick",value:function(r){var g=r.delegateTarget||r.currentTarget,k=this.action(g)||"copy",E=ee({action:k,container:this.container,target:this.target(g),text:this.text(g)});this.emit(E?"success":"error",{action:k,text:E,trigger:g,clearSelection:function(){g&&g.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(r){return j("action",r)}},{key:"defaultTarget",value:function(r){var g=j("target",r);if(g)return document.querySelector(g)}},{key:"defaultText",value:function(r){return j("text",r)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(r){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body};return G(r,g)}},{key:"cut",value:function(r){return C(r)}},{key:"isSupported",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["copy","cut"],g=typeof r=="string"?[r]:r,k=!!document.queryCommandSupported;return g.forEach(function(E){k=k&&!!document.queryCommandSupported(E)}),k}}]),n}(e()),se=oe},828:function(f){var b=9;if(typeof Element<"u"&&!Element.prototype.matches){var o=Element.prototype;o.matches=o.matchesSelector||o.mozMatchesSelector||o.msMatchesSelector||o.oMatchesSelector||o.webkitMatchesSelector}function d(e,y){for(;e&&e.nodeType!==b;){if(typeof e.matches=="function"&&e.matches(y))return e;e=e.parentNode}}f.exports=d},438:function(f,b,o){var d=o(828);function e(p,S,h,_,C){var I=m.apply(this,arguments);return p.addEventListener(h,I,C),{destroy:function(){p.removeEventListener(h,I,C)}}}function y(p,S,h,_,C){return typeof p.addEventListener=="function"?e.apply(null,arguments):typeof h=="function"?e.bind(null,document).apply(null,arguments):(typeof p=="string"&&(p=document.querySelectorAll(p)),Array.prototype.map.call(p,function(I){return e(I,S,h,_,C)}))}function m(p,S,h,_){return function(C){C.delegateTarget=d(C.target,S),C.delegateTarget&&_.call(p,C)}}f.exports=y},879:function(f,b){b.node=function(o){return o!==void 0&&o instanceof HTMLElement&&o.nodeType===1},b.nodeList=function(o){var d=Object.prototype.toString.call(o);return o!==void 0&&(d==="[object NodeList]"||d==="[object HTMLCollection]")&&"length"in o&&(o.length===0||b.node(o[0]))},b.string=function(o){return typeof o=="string"||o instanceof String},b.fn=function(o){var d=Object.prototype.toString.call(o);return d==="[object Function]"}},370:function(f,b,o){var d=o(879),e=o(438);function y(h,_,C){if(!h&&!_&&!C)throw new Error("Missing required arguments");if(!d.string(_))throw new TypeError("Second argument must be a String");if(!d.fn(C))throw new TypeError("Third argument must be a Function");if(d.node(h))return m(h,_,C);if(d.nodeList(h))return p(h,_,C);if(d.string(h))return S(h,_,C);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function m(h,_,C){return h.addEventListener(_,C),{destroy:function(){h.removeEventListener(_,C)}}}function p(h,_,C){return Array.prototype.forEach.call(h,function(I){I.addEventListener(_,C)}),{destroy:function(){Array.prototype.forEach.call(h,function(I){I.removeEventListener(_,C)})}}}function S(h,_,C){return e(document.body,h,_,C)}f.exports=y},817:function(f){function b(o){var d;if(o.nodeName==="SELECT")o.focus(),d=o.value;else if(o.nodeName==="INPUT"||o.nodeName==="TEXTAREA"){var e=o.hasAttribute("readonly");e||o.setAttribute("readonly",""),o.select(),o.setSelectionRange(0,o.value.length),e||o.removeAttribute("readonly"),d=o.value}else{o.hasAttribute("contenteditable")&&o.focus();var y=window.getSelection(),m=document.createRange();m.selectNodeContents(o),y.removeAllRanges(),y.addRange(m),d=y.toString()}return d}f.exports=b},279:function(f){function b(){}b.prototype={on:function(o,d,e){var y=this.e||(this.e={});return(y[o]||(y[o]=[])).push({fn:d,ctx:e}),this},once:function(o,d,e){var y=this;function m(){y.off(o,m),d.apply(e,arguments)}return m._=d,this.on(o,m,e)},emit:function(o){var d=[].slice.call(arguments,1),e=((this.e||(this.e={}))[o]||[]).slice(),y=0,m=e.length;for(y;y<m;y++)e[y].fn.apply(e[y].ctx,d);return this},off:function(o,d){var e=this.e||(this.e={}),y=e[o],m=[];if(y&&d)for(var p=0,S=y.length;p<S;p++)y[p].fn!==d&&y[p].fn._!==d&&m.push(y[p]);return m.length?e[o]=m:delete e[o],this}},f.exports=b,f.exports.TinyEmitter=b}},T={};function v(f){if(T[f])return T[f].exports;var b=T[f]={exports:{}};return V[f](b,b.exports,v),b.exports}return function(){v.n=function(f){var b=f&&f.__esModule?function(){return f.default}:function(){return f};return v.d(b,{a:b}),b}}(),function(){v.d=function(f,b){for(var o in b)v.o(b,o)&&!v.o(f,o)&&Object.defineProperty(f,o,{enumerable:!0,get:b[o]})}}(),function(){v.o=function(f,b){return Object.prototype.hasOwnProperty.call(f,b)}}(),v(686)}().default})})(fe);var Ae=fe.exports;const ze=he(Ae),Ue=$=>({toClipboard(B,V){return new Promise((T,v)=>{const f=document.createElement("button"),b=new ze(f,{text:()=>B,action:()=>"copy",container:V!==void 0?V:document.body});b.on("success",o=>{b.destroy(),T(o)}),b.on("error",o=>{b.destroy(),v(o)}),document.body.appendChild(f),f.click(),document.body.removeChild(f)})}});function Le(){const{t:$}=ce.useI18n(),{toClipboard:B}=Ue();return{percentFormat:(e,y,m)=>m?`${m}%`:"-",dateFormatYMD:(e,y,m)=>m?ne(new Date(m),"YYYY-mm-dd"):"-",dateFormatYMDHMS:(e,y,m)=>m?ne(new Date(m),"YYYY-mm-dd HH:MM:SS"):"-",dateFormatHMS:(e,y,m)=>{if(!m)return"-";let p=0;return typeof e=="number"&&(p=e),typeof m=="number"&&(p=m),ne(new Date(p*1e3),"HH:MM:SS")},scaleFormat:(e="0",y=4)=>Number.parseFloat(e).toFixed(y),scale2Format:(e="0")=>Number.parseFloat(e).toFixed(2),copyText:e=>new Promise((y,m)=>{try{B(e),O.success($("message.layout.copyTextSuccess")),y(e)}catch(p){O.error($("message.layout.copyTextError")),m(p)}})}}const De={class:"layout-breadcrumb-seting"},Fe={class:"layout-breadcrumb-seting-bar-flex"},Oe={class:"layout-breadcrumb-seting-bar-flex-value"},Pe={class:"layout-breadcrumb-seting-bar-flex mt15"},Re={class:"layout-breadcrumb-seting-bar-flex-label"},He={class:"layout-breadcrumb-seting-bar-flex-value"},Ge={class:"layout-breadcrumb-seting-bar-flex"},Ne={class:"layout-breadcrumb-seting-bar-flex-label"},Ye={class:"layout-breadcrumb-seting-bar-flex-value"},je={class:"layout-breadcrumb-seting-bar-flex"},We={class:"layout-breadcrumb-seting-bar-flex-label"},qe={class:"layout-breadcrumb-seting-bar-flex-value"},Je={class:"layout-breadcrumb-seting-bar-flex mt10"},Xe={class:"layout-breadcrumb-seting-bar-flex-label"},Ze={class:"layout-breadcrumb-seting-bar-flex-value"},Ke={class:"layout-breadcrumb-seting-bar-flex"},Qe={class:"layout-breadcrumb-seting-bar-flex-label"},et={class:"layout-breadcrumb-seting-bar-flex-value"},tt={class:"layout-breadcrumb-seting-bar-flex"},at={class:"layout-breadcrumb-seting-bar-flex-label"},lt={class:"layout-breadcrumb-seting-bar-flex-value"},ot={class:"layout-breadcrumb-seting-bar-flex"},st={class:"layout-breadcrumb-seting-bar-flex-label"},nt={class:"layout-breadcrumb-seting-bar-flex-value"},rt={class:"layout-breadcrumb-seting-bar-flex mt14"},ut={class:"layout-breadcrumb-seting-bar-flex-label"},it={class:"layout-breadcrumb-seting-bar-flex-value"},dt={class:"layout-breadcrumb-seting-bar-flex-label"},ct={class:"layout-breadcrumb-seting-bar-flex-value"},mt={class:"layout-breadcrumb-seting-bar-flex-label"},ft={class:"layout-breadcrumb-seting-bar-flex-value"},bt={class:"layout-breadcrumb-seting-bar-flex-label"},vt={class:"layout-breadcrumb-seting-bar-flex-value"},yt={class:"layout-breadcrumb-seting-bar-flex-label"},gt={class:"layout-breadcrumb-seting-bar-flex-value"},pt={class:"layout-breadcrumb-seting-bar-flex-label"},ht={class:"layout-breadcrumb-seting-bar-flex-value"},Ct={class:"layout-breadcrumb-seting-bar-flex-label"},xt={class:"layout-breadcrumb-seting-bar-flex-value"},_t={class:"layout-breadcrumb-seting-bar-flex mt15"},wt={class:"layout-breadcrumb-seting-bar-flex-label"},Vt={class:"layout-breadcrumb-seting-bar-flex-value"},Tt={class:"layout-breadcrumb-seting-bar-flex-label"},St={class:"layout-breadcrumb-seting-bar-flex-value"},$t={class:"layout-breadcrumb-seting-bar-flex mt15"},Bt={class:"layout-breadcrumb-seting-bar-flex-label"},Et={class:"layout-breadcrumb-seting-bar-flex-value"},Mt={class:"layout-breadcrumb-seting-bar-flex mt11"},kt={class:"layout-breadcrumb-seting-bar-flex-label"},It={class:"layout-breadcrumb-seting-bar-flex-value"},At={class:"layout-breadcrumb-seting-bar-flex mt15"},zt={class:"layout-breadcrumb-seting-bar-flex-label"},Ut={class:"layout-breadcrumb-seting-bar-flex-value"},Lt={class:"layout-breadcrumb-seting-bar-flex-label"},Dt={class:"layout-breadcrumb-seting-bar-flex-value"},Ft={class:"layout-breadcrumb-seting-bar-flex mt15"},Ot={class:"layout-breadcrumb-seting-bar-flex-label"},Pt={class:"layout-breadcrumb-seting-bar-flex-value"},Rt={class:"layout-breadcrumb-seting-bar-flex mt15"},Ht={class:"layout-breadcrumb-seting-bar-flex-label"},Gt={class:"layout-breadcrumb-seting-bar-flex-value"},Nt={class:"layout-breadcrumb-seting-bar-flex mt15"},Yt={class:"layout-breadcrumb-seting-bar-flex-label"},jt={class:"layout-breadcrumb-seting-bar-flex-value"},Wt={class:"layout-breadcrumb-seting-bar-flex mt15"},qt={class:"layout-breadcrumb-seting-bar-flex-label"},Jt={class:"layout-breadcrumb-seting-bar-flex-value"},Xt={class:"layout-breadcrumb-seting-bar-flex-label"},Zt={class:"layout-breadcrumb-seting-bar-flex-value"},Kt={class:"layout-breadcrumb-seting-bar-flex mt15"},Qt={class:"layout-breadcrumb-seting-bar-flex-label"},ea={class:"layout-breadcrumb-seting-bar-flex-value"},ta={class:"layout-breadcrumb-seting-bar-flex mt15"},aa={class:"layout-breadcrumb-seting-bar-flex-label"},la={class:"layout-breadcrumb-seting-bar-flex-value"},oa={class:"layout-breadcrumb-seting-bar-flex mt15"},sa={class:"layout-breadcrumb-seting-bar-flex-label"},na={class:"layout-breadcrumb-seting-bar-flex-value"},ra={class:"layout-breadcrumb-seting-bar-flex mt15"},ua={class:"layout-breadcrumb-seting-bar-flex-label"},ia={class:"layout-breadcrumb-seting-bar-flex-value"},da={class:"layout-breadcrumb-seting-bar-flex mt15"},ca={class:"layout-breadcrumb-seting-bar-flex-label"},ma={class:"layout-breadcrumb-seting-bar-flex-value"},fa={class:"layout-breadcrumb-seting-bar-flex mt14"},ba={class:"layout-breadcrumb-seting-bar-flex-label"},va={class:"layout-breadcrumb-seting-bar-flex-value"},ya={class:"layout-breadcrumb-seting-bar-flex mt15"},ga={class:"layout-breadcrumb-seting-bar-flex-label"},pa={class:"layout-breadcrumb-seting-bar-flex-value"},ha={class:"layout-breadcrumb-seting-bar-flex mt15"},Ca={class:"layout-breadcrumb-seting-bar-flex-label"},xa={class:"layout-breadcrumb-seting-bar-flex-value"},_a={class:"layout-breadcrumb-seting-bar-flex-label"},wa={class:"layout-breadcrumb-seting-bar-flex-value"},Va={class:"layout-breadcrumb-seting-bar-flex-label"},Ta={class:"layout-breadcrumb-seting-bar-flex-value"},Sa={class:"layout-drawer-content-flex"},$a={class:"layout-tips-box"},Ba={class:"layout-tips-txt"},Ea={class:"layout-tips-box"},Ma={class:"layout-tips-txt"},ka={class:"layout-tips-box"},Ia={class:"layout-tips-txt"},Aa={class:"layout-tips-box"},za={class:"layout-tips-txt"},Ua={class:"copy-config"},La=me({name:"layoutBreadcrumbSeting"}),Da=me({...La,setup($,{expose:B}){const{locale:V}=ce.useI18n(),T=xe(),{themeConfig:v}=Ve(T),{copyText:f}=Le(),{getLightColor:b,getDarkColor:o}=J(),d=Te({isMobile:!1}),e=Se(()=>v.value),y=()=>{if(!e.value.primary)return O.warning("全局主题 primary 颜色值不能为空");document.documentElement.style.setProperty("--el-color-primary-dark-2",`${o(e.value.primary,.1)}`),document.documentElement.style.setProperty("--el-color-primary",e.value.primary);for(let l=1;l<=9;l++)document.documentElement.style.setProperty(`--el-color-primary-light-${l}`,`${b(e.value.primary,l/10)}`);Y()},m=l=>{document.documentElement.style.setProperty(`--next-bg-${l}`,v.value[l]),l==="menuBar"&&document.documentElement.style.setProperty("--next-bg-menuBar-light-1",b(e.value.menuBar,.05)),p(),S(),h(),Y()},p=()=>{_(".layout-navbars-breadcrumb-index",e.value.isTopBarColorGradual,e.value.topBar)},S=()=>{_(".layout-container .el-aside",e.value.isMenuBarColorGradual,e.value.menuBar)},h=()=>{_(".layout-container .layout-columns-aside",e.value.isColumnsMenuBarColorGradual,e.value.columnsMenuBar)},_=(l,t,n)=>{setTimeout(()=>{let i=document.querySelector(l);if(!i)return!1;document.documentElement.style.setProperty("--el-menu-bg-color",document.documentElement.style.getPropertyValue("--next-bg-menuBar")),t?i.setAttribute("style",`background:linear-gradient(to bottom left , ${n}, ${b(n,.6)}) !important;`):i.setAttribute("style",""),x()},200)},C=()=>{x()},I=()=>{Y()},X=()=>{e.value.isFixedHeaderChange=!e.value.isFixedHeader,x()},K=()=>{e.value.isBreadcrumb=!1,x(),q.emit("getBreadcrumbIndexSetFilterRoutes")},G=()=>{e.value.isShowLogoChange=!e.value.isShowLogo,x()},P=()=>{e.value.layout==="classic"&&(e.value.isClassicSplitMenu=!1),x()},Q=()=>{q.emit("openOrCloseSortable"),x()},ee=()=>{q.emit("openShareTagsView"),x()},U=l=>{l==="grayscale"?e.value.isGrayscale&&(e.value.isInvert=!1):e.value.isInvert&&(e.value.isGrayscale=!1);const t=l==="grayscale"?`grayscale(${e.value.isGrayscale?1:0})`:`invert(${e.value.isInvert?"80%":"0%"})`;document.body.setAttribute("style",`filter: ${t}`),x()},Z=()=>{const l=document.documentElement;e.value.isIsDark?l.setAttribute("data-theme","dark"):l.setAttribute("data-theme","")},N=()=>{e.value.isWartermark?re.set(e.value.wartermarkText):re.del(),x()},te=l=>{if(e.value.wartermarkText=we(l),e.value.wartermarkText==="")return!1;e.value.isWartermark&&re.set(e.value.wartermarkText),x()},R=l=>{if(z.set("oldLayout",l),e.value.layout===l)return!1;l==="transverse"&&(e.value.isCollapse=!1),e.value.layout=l,e.value.isDrawer=!1,F()},F=()=>{m("menuBar"),m("menuBarColor"),m("menuBarActiveColor"),m("topBar"),m("topBarColor"),m("columnsMenuBar"),m("columnsMenuBarColor")},ae=()=>{e.value.isFixedHeaderChange=!1,e.value.isShowLogoChange=!1,e.value.isDrawer=!1,x()},le=()=>{e.value.isDrawer=!0},Y=()=>{x(),H()},x=()=>{z.remove("themeConfig"),z.set("themeConfig",e.value)},H=()=>{z.set("themeConfigStyle",document.documentElement.style.cssText)},j=()=>{let l=z.get("themeConfig");l.isDrawer=!1,f(JSON.stringify(l)).then(()=>{e.value.isDrawer=!1})},oe=()=>{z.clear(),window.location.reload(),z.set("version","3.2.0")},se=()=>{p(),S(),h()};return $e(()=>{Be(()=>{z.get("frequency")||F(),z.set("frequency",1),q.on("layoutMobileResize",l=>{e.value.layout=l.layout,e.value.isDrawer=!1,F(),d.isMobile=_e.isMobile()}),setTimeout(()=>{y(),e.value.isGrayscale&&U("grayscale"),e.value.isInvert&&U("invert"),e.value.isIsDark&&Z(),N(),z.get("themeConfig")&&(V.value=z.get("themeConfig").globalI18n),se()},100)})}),Ee(()=>{q.off("layoutMobileResize",()=>{})}),B({openDrawer:le}),(l,t)=>{const n=M("el-divider"),i=M("el-color-picker"),r=M("el-switch"),g=M("el-input-number"),k=M("el-input"),E=M("el-option"),W=M("el-select"),be=M("el-alert"),ve=M("ele-CopyDocument"),ue=M("el-icon"),ie=M("el-button"),ye=M("ele-RefreshRight"),ge=M("el-scrollbar"),pe=M("el-drawer");return ke(),Me("div",De,[u(pe,{title:l.$t("message.layout.configTitle"),modelValue:e.value.isDrawer,"onUpdate:modelValue":t[49]||(t[49]=s=>e.value.isDrawer=s),direction:"rtl","destroy-on-close":"",size:"260px",onClose:ae},{default:w(()=>[u(ge,{class:"layout-breadcrumb-seting-bar"},{default:w(()=>[u(n,{"content-position":"left"},{default:w(()=>[L(c(l.$t("message.layout.oneTitle")),1)]),_:1}),a("div",Fe,[t[50]||(t[50]=a("div",{class:"layout-breadcrumb-seting-bar-flex-label"},"primary",-1)),a("div",Oe,[u(i,{modelValue:e.value.primary,"onUpdate:modelValue":t[0]||(t[0]=s=>e.value.primary=s),size:"default",onChange:y},null,8,["modelValue"])])]),a("div",Pe,[a("div",Re,c(l.$t("message.layout.fourIsDark")),1),a("div",He,[u(r,{modelValue:e.value.isIsDark,"onUpdate:modelValue":t[1]||(t[1]=s=>e.value.isIsDark=s),size:"small",onChange:Z},null,8,["modelValue"])])]),u(n,{"content-position":"left"},{default:w(()=>[L(c(l.$t("message.layout.twoTopTitle")),1)]),_:1}),a("div",Ge,[a("div",Ne,c(l.$t("message.layout.twoTopBar")),1),a("div",Ye,[u(i,{modelValue:e.value.topBar,"onUpdate:modelValue":t[2]||(t[2]=s=>e.value.topBar=s),size:"default",onChange:t[3]||(t[3]=s=>m("topBar"))},null,8,["modelValue"])])]),a("div",je,[a("div",We,c(l.$t("message.layout.twoTopBarColor")),1),a("div",qe,[u(i,{modelValue:e.value.topBarColor,"onUpdate:modelValue":t[4]||(t[4]=s=>e.value.topBarColor=s),size:"default",onChange:t[5]||(t[5]=s=>m("topBarColor"))},null,8,["modelValue"])])]),a("div",Je,[a("div",Xe,c(l.$t("message.layout.twoIsTopBarColorGradual")),1),a("div",Ze,[u(r,{modelValue:e.value.isTopBarColorGradual,"onUpdate:modelValue":t[6]||(t[6]=s=>e.value.isTopBarColorGradual=s),size:"small",onChange:p},null,8,["modelValue"])])]),u(n,{"content-position":"left"},{default:w(()=>[L(c(l.$t("message.layout.twoMenuTitle")),1)]),_:1}),a("div",Ke,[a("div",Qe,c(l.$t("message.layout.twoMenuBar")),1),a("div",et,[u(i,{modelValue:e.value.menuBar,"onUpdate:modelValue":t[7]||(t[7]=s=>e.value.menuBar=s),size:"default",onChange:t[8]||(t[8]=s=>m("menuBar"))},null,8,["modelValue"])])]),a("div",tt,[a("div",at,c(l.$t("message.layout.twoMenuBarColor")),1),a("div",lt,[u(i,{modelValue:e.value.menuBarColor,"onUpdate:modelValue":t[9]||(t[9]=s=>e.value.menuBarColor=s),size:"default",onChange:t[10]||(t[10]=s=>m("menuBarColor"))},null,8,["modelValue"])])]),a("div",ot,[a("div",st,c(l.$t("message.layout.twoMenuBarActiveColor")),1),a("div",nt,[u(i,{modelValue:e.value.menuBarActiveColor,"onUpdate:modelValue":t[11]||(t[11]=s=>e.value.menuBarActiveColor=s),size:"default","show-alpha":"",onChange:t[12]||(t[12]=s=>m("menuBarActiveColor"))},null,8,["modelValue"])])]),a("div",rt,[a("div",ut,c(l.$t("message.layout.twoIsMenuBarColorGradual")),1),a("div",it,[u(r,{modelValue:e.value.isMenuBarColorGradual,"onUpdate:modelValue":t[13]||(t[13]=s=>e.value.isMenuBarColorGradual=s),size:"small",onChange:S},null,8,["modelValue"])])]),u(n,{"content-position":"left",style:A({opacity:e.value.layout!=="columns"?.5:1})},{default:w(()=>[L(c(l.$t("message.layout.twoColumnsTitle")),1)]),_:1},8,["style"]),a("div",{class:"layout-breadcrumb-seting-bar-flex",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",dt,c(l.$t("message.layout.twoColumnsMenuBar")),1),a("div",ct,[u(i,{modelValue:e.value.columnsMenuBar,"onUpdate:modelValue":t[14]||(t[14]=s=>e.value.columnsMenuBar=s),size:"default",onChange:t[15]||(t[15]=s=>m("columnsMenuBar")),disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",mt,c(l.$t("message.layout.twoColumnsMenuBarColor")),1),a("div",ft,[u(i,{modelValue:e.value.columnsMenuBarColor,"onUpdate:modelValue":t[16]||(t[16]=s=>e.value.columnsMenuBarColor=s),size:"default",onChange:t[17]||(t[17]=s=>m("columnsMenuBarColor")),disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt14",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",bt,c(l.$t("message.layout.twoIsColumnsMenuBarColorGradual")),1),a("div",vt,[u(r,{modelValue:e.value.isColumnsMenuBarColorGradual,"onUpdate:modelValue":t[18]||(t[18]=s=>e.value.isColumnsMenuBarColorGradual=s),size:"small",onChange:h,disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt14",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",yt,c(l.$t("message.layout.twoIsColumnsMenuHoverPreload")),1),a("div",gt,[u(r,{modelValue:e.value.isColumnsMenuHoverPreload,"onUpdate:modelValue":t[19]||(t[19]=s=>e.value.isColumnsMenuHoverPreload=s),size:"small",onChange:C,disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),u(n,{"content-position":"left"},{default:w(()=>[L(c(l.$t("message.layout.threeTitle")),1)]),_:1}),a("div",{class:"layout-breadcrumb-seting-bar-flex",style:A({opacity:e.value.layout==="transverse"?.5:1})},[a("div",pt,c(l.$t("message.layout.threeIsCollapse")),1),a("div",ht,[u(r,{modelValue:e.value.isCollapse,"onUpdate:modelValue":t[20]||(t[20]=s=>e.value.isCollapse=s),disabled:e.value.layout==="transverse",size:"small",onChange:I},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:A({opacity:e.value.layout==="transverse"?.5:1})},[a("div",Ct,c(l.$t("message.layout.threeIsUniqueOpened")),1),a("div",xt,[u(r,{modelValue:e.value.isUniqueOpened,"onUpdate:modelValue":t[21]||(t[21]=s=>e.value.isUniqueOpened=s),disabled:e.value.layout==="transverse",size:"small",onChange:x},null,8,["modelValue","disabled"])])],4),a("div",_t,[a("div",wt,c(l.$t("message.layout.threeIsFixedHeader")),1),a("div",Vt,[u(r,{modelValue:e.value.isFixedHeader,"onUpdate:modelValue":t[22]||(t[22]=s=>e.value.isFixedHeader=s),size:"small",onChange:X},null,8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:A({opacity:e.value.layout!=="classic"?.5:1})},[a("div",Tt,c(l.$t("message.layout.threeIsClassicSplitMenu")),1),a("div",St,[u(r,{modelValue:e.value.isClassicSplitMenu,"onUpdate:modelValue":t[23]||(t[23]=s=>e.value.isClassicSplitMenu=s),disabled:e.value.layout!=="classic",size:"small",onChange:K},null,8,["modelValue","disabled"])])],4),a("div",$t,[a("div",Bt,c(l.$t("message.layout.threeIsLockScreen")),1),a("div",Et,[u(r,{modelValue:e.value.isLockScreen,"onUpdate:modelValue":t[24]||(t[24]=s=>e.value.isLockScreen=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",Mt,[a("div",kt,c(l.$t("message.layout.threeLockScreenTime")),1),a("div",It,[u(g,{modelValue:e.value.lockScreenTime,"onUpdate:modelValue":t[25]||(t[25]=s=>e.value.lockScreenTime=s),"controls-position":"right",min:1,max:9999,onChange:x,size:"default",style:{width:"90px"}},null,8,["modelValue"])])]),u(n,{"content-position":"left"},{default:w(()=>[L(c(l.$t("message.layout.fourTitle")),1)]),_:1}),a("div",At,[a("div",zt,c(l.$t("message.layout.fourIsShowLogo")),1),a("div",Ut,[u(r,{modelValue:e.value.isShowLogo,"onUpdate:modelValue":t[26]||(t[26]=s=>e.value.isShowLogo=s),size:"small",onChange:G},null,8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:A({opacity:e.value.layout==="classic"||e.value.layout==="transverse"?.5:1})},[a("div",Lt,c(l.$t("message.layout.fourIsBreadcrumb")),1),a("div",Dt,[u(r,{modelValue:e.value.isBreadcrumb,"onUpdate:modelValue":t[27]||(t[27]=s=>e.value.isBreadcrumb=s),disabled:e.value.layout==="classic"||e.value.layout==="transverse",size:"small",onChange:P},null,8,["modelValue","disabled"])])],4),a("div",Ft,[a("div",Ot,c(l.$t("message.layout.fourIsBreadcrumbIcon")),1),a("div",Pt,[u(r,{modelValue:e.value.isBreadcrumbIcon,"onUpdate:modelValue":t[28]||(t[28]=s=>e.value.isBreadcrumbIcon=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",Rt,[a("div",Ht,c(l.$t("message.layout.fourIsTagsview")),1),a("div",Gt,[u(r,{modelValue:e.value.isTagsview,"onUpdate:modelValue":t[29]||(t[29]=s=>e.value.isTagsview=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",Nt,[a("div",Yt,c(l.$t("message.layout.fourIsTagsviewIcon")),1),a("div",jt,[u(r,{modelValue:e.value.isTagsviewIcon,"onUpdate:modelValue":t[30]||(t[30]=s=>e.value.isTagsviewIcon=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",Wt,[a("div",qt,c(l.$t("message.layout.fourIsCacheTagsView")),1),a("div",Jt,[u(r,{modelValue:e.value.isCacheTagsView,"onUpdate:modelValue":t[31]||(t[31]=s=>e.value.isCacheTagsView=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:A({opacity:d.isMobile?.5:1})},[a("div",Xt,c(l.$t("message.layout.fourIsSortableTagsView")),1),a("div",Zt,[u(r,{modelValue:e.value.isSortableTagsView,"onUpdate:modelValue":t[32]||(t[32]=s=>e.value.isSortableTagsView=s),disabled:!!d.isMobile,size:"small",onChange:Q},null,8,["modelValue","disabled"])])],4),a("div",Kt,[a("div",Qt,c(l.$t("message.layout.fourIsShareTagsView")),1),a("div",ea,[u(r,{modelValue:e.value.isShareTagsView,"onUpdate:modelValue":t[33]||(t[33]=s=>e.value.isShareTagsView=s),size:"small",onChange:ee},null,8,["modelValue"])])]),a("div",ta,[a("div",aa,c(l.$t("message.layout.fourIsFooter")),1),a("div",la,[u(r,{modelValue:e.value.isFooter,"onUpdate:modelValue":t[34]||(t[34]=s=>e.value.isFooter=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",oa,[a("div",sa,c(l.$t("message.layout.fourIsGrayscale")),1),a("div",na,[u(r,{modelValue:e.value.isGrayscale,"onUpdate:modelValue":t[35]||(t[35]=s=>e.value.isGrayscale=s),size:"small",onChange:t[36]||(t[36]=s=>U("grayscale"))},null,8,["modelValue"])])]),a("div",ra,[a("div",ua,c(l.$t("message.layout.fourIsInvert")),1),a("div",ia,[u(r,{modelValue:e.value.isInvert,"onUpdate:modelValue":t[37]||(t[37]=s=>e.value.isInvert=s),size:"small",onChange:t[38]||(t[38]=s=>U("invert"))},null,8,["modelValue"])])]),a("div",da,[a("div",ca,c(l.$t("message.layout.fourIsWartermark")),1),a("div",ma,[u(r,{modelValue:e.value.isWartermark,"onUpdate:modelValue":t[39]||(t[39]=s=>e.value.isWartermark=s),size:"small",onChange:N},null,8,["modelValue"])])]),a("div",fa,[a("div",ba,c(l.$t("message.layout.fourWartermarkText")),1),a("div",va,[u(k,{modelValue:e.value.wartermarkText,"onUpdate:modelValue":t[40]||(t[40]=s=>e.value.wartermarkText=s),size:"default",style:{width:"90px"},onInput:te},null,8,["modelValue"])])]),u(n,{"content-position":"left"},{default:w(()=>[L(c(l.$t("message.layout.fiveTitle")),1)]),_:1}),a("div",ya,[a("div",ga,c(l.$t("message.layout.fiveTagsStyle")),1),a("div",pa,[u(W,{modelValue:e.value.tagsStyle,"onUpdate:modelValue":t[41]||(t[41]=s=>e.value.tagsStyle=s),placeholder:"请选择",size:"default",style:{width:"90px"},onChange:x},{default:w(()=>[u(E,{label:"风格1",value:"tags-style-one"}),u(E,{label:"风格4",value:"tags-style-four"}),u(E,{label:"风格5",value:"tags-style-five"})]),_:1},8,["modelValue"])])]),a("div",ha,[a("div",Ca,c(l.$t("message.layout.fiveAnimation")),1),a("div",xa,[u(W,{modelValue:e.value.animation,"onUpdate:modelValue":t[42]||(t[42]=s=>e.value.animation=s),placeholder:"请选择",size:"default",style:{width:"90px"},onChange:x},{default:w(()=>[u(E,{label:"slide-right",value:"slide-right"}),u(E,{label:"slide-left",value:"slide-left"}),u(E,{label:"opacitys",value:"opacitys"})]),_:1},8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",_a,c(l.$t("message.layout.fiveColumnsAsideStyle")),1),a("div",wa,[u(W,{modelValue:e.value.columnsAsideStyle,"onUpdate:modelValue":t[43]||(t[43]=s=>e.value.columnsAsideStyle=s),placeholder:"请选择",size:"default",style:{width:"90px"},disabled:e.value.layout!=="columns",onChange:x},{default:w(()=>[u(E,{label:"圆角",value:"columns-round"}),u(E,{label:"卡片",value:"columns-card"})]),_:1},8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15 mb27",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",Va,c(l.$t("message.layout.fiveColumnsAsideLayout")),1),a("div",Ta,[u(W,{modelValue:e.value.columnsAsideLayout,"onUpdate:modelValue":t[44]||(t[44]=s=>e.value.columnsAsideLayout=s),placeholder:"请选择",size:"default",style:{width:"90px"},disabled:e.value.layout!=="columns",onChange:x},{default:w(()=>[u(E,{label:"水平",value:"columns-horizontal"}),u(E,{label:"垂直",value:"columns-vertical"})]),_:1},8,["modelValue","disabled"])])],4),u(n,{"content-position":"left"},{default:w(()=>[L(c(l.$t("message.layout.sixTitle")),1)]),_:1}),a("div",Sa,[a("div",{class:"layout-drawer-content-item",onClick:t[45]||(t[45]=s=>R("defaults"))},[a("section",{class:D(["el-container el-circular",{"drawer-layout-active":e.value.layout==="defaults"}])},t[51]||(t[51]=[a("aside",{class:"el-aside",style:{width:"20px"}},null,-1),a("section",{class:"el-container is-vertical"},[a("header",{class:"el-header",style:{height:"10px"}}),a("main",{class:"el-main"})],-1)]),2),a("div",{class:D(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="defaults"}])},[a("div",$a,[a("p",Ba,c(l.$t("message.layout.sixDefaults")),1)])],2)]),a("div",{class:"layout-drawer-content-item",onClick:t[46]||(t[46]=s=>R("classic"))},[a("section",{class:D(["el-container is-vertical el-circular",{"drawer-layout-active":e.value.layout==="classic"}])},t[52]||(t[52]=[a("header",{class:"el-header",style:{height:"10px"}},null,-1),a("section",{class:"el-container"},[a("aside",{class:"el-aside",style:{width:"20px"}}),a("section",{class:"el-container is-vertical"},[a("main",{class:"el-main"})])],-1)]),2),a("div",{class:D(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="classic"}])},[a("div",Ea,[a("p",Ma,c(l.$t("message.layout.sixClassic")),1)])],2)]),a("div",{class:"layout-drawer-content-item",onClick:t[47]||(t[47]=s=>R("transverse"))},[a("section",{class:D(["el-container is-vertical el-circular",{"drawer-layout-active":e.value.layout==="transverse"}])},t[53]||(t[53]=[a("header",{class:"el-header",style:{height:"10px"}},null,-1),a("section",{class:"el-container"},[a("section",{class:"el-container is-vertical"},[a("main",{class:"el-main"})])],-1)]),2),a("div",{class:D(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="transverse"}])},[a("div",ka,[a("p",Ia,c(l.$t("message.layout.sixTransverse")),1)])],2)]),a("div",{class:"layout-drawer-content-item",onClick:t[48]||(t[48]=s=>R("columns"))},[a("section",{class:D(["el-container el-circular",{"drawer-layout-active":e.value.layout==="columns"}])},t[54]||(t[54]=[a("aside",{class:"el-aside-dark",style:{width:"10px"}},null,-1),a("aside",{class:"el-aside",style:{width:"20px"}},null,-1),a("section",{class:"el-container is-vertical"},[a("header",{class:"el-header",style:{height:"10px"}}),a("main",{class:"el-main"})],-1)]),2),a("div",{class:D(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="columns"}])},[a("div",Aa,[a("p",za,c(l.$t("message.layout.sixColumns")),1)])],2)])]),a("div",Ua,[u(be,{title:l.$t("message.layout.tipText"),type:"warning",closable:!1},null,8,["title"]),u(ie,{size:"default",class:"copy-config-btn",type:"primary",ref:"copyConfigBtnRef",onClick:j},{default:w(()=>[u(ue,{class:"mr5"},{default:w(()=>[u(ve)]),_:1}),L(" "+c(l.$t("message.layout.copyText")),1)]),_:1},512),u(ie,{size:"default",class:"copy-config-btn-reset",type:"info",onClick:oe},{default:w(()=>[u(ue,{class:"mr5"},{default:w(()=>[u(ye)]),_:1}),L(" "+c(l.$t("message.layout.resetText")),1)]),_:1})])]),_:1})]),_:1},8,["title","modelValue"])])}}}),Ha=Ie(Da,[["__scopeId","data-v-813cba48"]]);export{Ha as default};
