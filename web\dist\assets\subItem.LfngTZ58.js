import{d as h,p as $,b as c,c as n,e as t,j as w,F as a,g as l,w as r,f as o,l as u,t as m,Z as d,k}from"./vue.zNq9Glab.js";import{O as I}from"./index.GuQX7xXE.js";const B={key:0,href:"#/templateCenter",target:"_blank"},S=["onClick"],x=["onClick"],N=h({name:"navMenuSubItem"}),M=h({...N,props:{chil:{type:Array,default:()=>[]}},setup(f){const y=f,C=$(()=>y.chil),p=i=>{I.handleOpenLink(i)};return(i,V)=>{const s=c("SvgIcon"),b=c("sub-item",!0),g=c("el-sub-menu"),_=c("el-menu-item");return t(!0),n(a,null,w(C.value,e=>(t(),n(a,null,[e.children&&e.children.length>0?(t(),l(g,{index:e.path,key:e.path},{title:r(()=>[o(s,{name:e.meta.icon},null,8,["name"]),u("span",null,m(i.$t(e.meta.title)),1)]),default:r(()=>[o(b,{chil:e.children},null,8,["chil"])]),_:2},1032,["index"])):(t(),n(a,{key:1},[e.name==="templateCenter"?(t(),n("a",B,[(t(),l(_,{key:e.path},{default:r(()=>[!e.meta.isLink||e.meta.isLink&&e.meta.isIframe?(t(),n(a,{key:0},[o(s,{name:e.meta.icon},null,8,["name"]),u("span",null,m(i.$t(e.meta.title)),1)],64)):(t(),n("a",{key:1,class:"w100",onClick:d(L=>p(e),["prevent"])},[o(s,{name:e.meta.icon},null,8,["name"]),k(" "+m(i.$t(e.meta.title)),1)],8,S))]),_:2},1024))])):(t(),l(_,{index:e.path,key:e.path},{default:r(()=>[!e.meta.isLink||e.meta.isLink&&e.meta.isIframe?(t(),n(a,{key:0},[o(s,{name:e.meta.icon},null,8,["name"]),u("span",null,m(i.$t(e.meta.title)),1)],64)):(t(),n("a",{key:1,class:"w100",onClick:d(L=>p(e),["prevent"])},[o(s,{name:e.meta.icon},null,8,["name"]),k(" "+m(i.$t(e.meta.title)),1)],8,x))]),_:2},1032,["index"]))],64))],64))),256)}}});export{M as default};
