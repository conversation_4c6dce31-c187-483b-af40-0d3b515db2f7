const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.D23QjRGq.js","assets/index.GuQX7xXE.js","assets/vue.zNq9Glab.js","assets/index.WGbL7eFL.css","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/index.DPJ4CiP8.css"])))=>i.map(i=>d[i]);
import{J as n,_}from"./index.GuQX7xXE.js";import{d as s,M as c,b as i,A as u,D as d,u as e,g as l,e as p,w as f,f as m,P as h}from"./vue.zNq9Glab.js";const w=s({name:"layoutHeader"}),x=s({...w,setup(v){const o=h(()=>_(()=>import("./index.D23QjRGq.js"),__vite__mapDeps([0,1,2,3,4,5]))),a=n(),{isTagsViewCurrenFull:t}=c(a);return(C,T)=>{const r=i("el-header");return u((p(),l(r,{class:"layout-header"},{default:f(()=>[m(e(o))]),_:1},512)),[[d,!e(t)]])}}});export{x as default};
