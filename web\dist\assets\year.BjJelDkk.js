import{d as S,a as i,p as w,m as b,o as q,b as V,g as A,e as F,w as r,f as o,k as m,c as G,F as H,j as I}from"./vue.zNq9Glab.js";const K=S({__name:"year",props:{cron:{},check:{type:Function}},emits:["update"],setup(N,{expose:B,emit:T}){const v=T,C=N,a=i(1),n=i(0),t=i(0),s=i(0),d=i(0),p=i(1),k=i([]),x=C.check;B({cycle01:t,cycle02:s,average01:d,average02:p,checkboxList:k});const g=w(()=>(t.value=x(t.value,n.value,2098),s.value=x(s.value,t.value?t.value+1:n.value+1,2099),t.value+"-"+s.value)),_=w(()=>(d.value=x(d.value,n.value,2098),p.value=x(p.value,1,2099-d.value||n.value),d.value+"/"+p.value)),U=w(()=>{let u=k.value.join();return u==""?"*":u});b(a,(u,e)=>{D()}),b(g,(u,e)=>{L()}),b(_,(u,e)=>{O()}),b(U,(u,e)=>{Y()}),b(C,(u,e)=>{j(u.cron.year)});function j(u){u&&(u==""?a.value=1:u=="*"?a.value=2:typeof u=="string"&&u.indexOf("-")>-1?a.value=3:typeof u=="string"&&u.indexOf("/")>-1?a.value=4:a.value=5)}q(()=>{n.value=Number(new Date().getFullYear()),t.value=n.value,s.value=t.value+1,d.value=n.value});function D(){switch(a.value){case 1:v("update","year","","cronyear");break;case 2:v("update","year","*","cronyear");break;case 3:v("update","year",g.value,"cronyear");break;case 4:v("update","year",_.value,"cronyear");break;case 5:v("update","year",U.value,"cronyear");break}}function L(){a.value==3&&v("update","year",g.value,"cronyear")}function O(){a.value==4&&v("update","year",_.value,"cronyear")}function Y(){a.value==5&&v("update","year",U.value,"cronyear")}return(u,e)=>{const f=V("el-radio"),y=V("el-form-item"),c=V("el-input-number"),z=V("el-option"),E=V("el-select"),M=V("el-form");return F(),A(M,{size:"small"},{default:r(()=>[o(y,null,{default:r(()=>[o(f,{label:1,modelValue:a.value,"onUpdate:modelValue":e[0]||(e[0]=l=>a.value=l)},{default:r(()=>e[10]||(e[10]=[m(" 不填，允许的通配符[, - * /] ")])),_:1,__:[10]},8,["modelValue"])]),_:1}),o(y,null,{default:r(()=>[o(f,{label:2,modelValue:a.value,"onUpdate:modelValue":e[1]||(e[1]=l=>a.value=l)},{default:r(()=>e[11]||(e[11]=[m("每年")])),_:1,__:[11]},8,["modelValue"])]),_:1}),o(y,null,{default:r(()=>[o(f,{label:3,modelValue:a.value,"onUpdate:modelValue":e[4]||(e[4]=l=>a.value=l)},{default:r(()=>[e[12]||(e[12]=m(" 周期从 ")),o(c,{modelValue:t.value,"onUpdate:modelValue":e[2]||(e[2]=l=>t.value=l),min:n.value,max:2098},null,8,["modelValue","min"]),e[13]||(e[13]=m(" - ")),o(c,{modelValue:s.value,"onUpdate:modelValue":e[3]||(e[3]=l=>s.value=l),min:t.value?t.value+1:n.value+1,max:2099},null,8,["modelValue","min"])]),_:1,__:[12,13]},8,["modelValue"])]),_:1}),o(y,null,{default:r(()=>[o(f,{label:4,modelValue:a.value,"onUpdate:modelValue":e[7]||(e[7]=l=>a.value=l)},{default:r(()=>[e[14]||(e[14]=m(" 从 ")),o(c,{modelValue:d.value,"onUpdate:modelValue":e[5]||(e[5]=l=>d.value=l),min:n.value,max:2098},null,8,["modelValue","min"]),e[15]||(e[15]=m(" 年开始，每 ")),o(c,{modelValue:p.value,"onUpdate:modelValue":e[6]||(e[6]=l=>p.value=l),min:1,max:2099-d.value||n.value},null,8,["modelValue","max"]),e[16]||(e[16]=m(" 年执行一次 "))]),_:1,__:[14,15,16]},8,["modelValue"])]),_:1}),o(y,null,{default:r(()=>[o(f,{label:5,modelValue:a.value,"onUpdate:modelValue":e[9]||(e[9]=l=>a.value=l)},{default:r(()=>[e[17]||(e[17]=m(" 指定 ")),o(E,{clearable:"",modelValue:k.value,"onUpdate:modelValue":e[8]||(e[8]=l=>k.value=l),placeholder:"可多选",multiple:""},{default:r(()=>[(F(),G(H,null,I(9,l=>o(z,{key:l,value:l-1+n.value,label:l-1+n.value},null,8,["value","label"])),64))]),_:1},8,["modelValue"])]),_:1,__:[17]},8,["modelValue"])]),_:1})]),_:1})}}});export{K as default};
