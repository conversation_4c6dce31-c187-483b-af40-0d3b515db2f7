const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/userNews.BkO95KNa.js","assets/vue.zNq9Glab.js","assets/index.GuQX7xXE.js","assets/index.WGbL7eFL.css","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/userNews.44C92aOn.css","assets/search.Dc1pRj-_.js","assets/search.gOvPOjoo.css"])))=>i.map(i=>d[i]);
import{Z as te,$ as L,u as oe,L as v,S as P,g as le,ab as ae,_ as T,e as re,l as ie,E as ue,O as ce}from"./index.GuQX7xXE.js";import{B as de,d as D,R as me,M as B,a as fe,r as ge,p as be,o as _e,b,c as R,e as q,f as n,l as u,w as s,k as m,t as p,v as O,u as f,P as x,h as he,q as pe}from"./vue.zNq9Glab.js";import{_ as we}from"./_plugin-vue_export-helper.DlAUqK2U.js";const N=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],_=(()=>{if(typeof document>"u")return!1;const t=N[0],i={};for(const l of N)if((l==null?void 0:l[1])in document){for(const[d,w]of l.entries())i[t[d]]=w;return i}return!1})(),A={change:_.fullscreenchange,error:_.fullscreenerror};let r={request(t=document.documentElement,i){return new Promise((l,h)=>{const d=()=>{r.off("change",d),l()};r.on("change",d);const w=t[_.requestFullscreen](i);w instanceof Promise&&w.then(d).catch(h)})},exit(){return new Promise((t,i)=>{if(!r.isFullscreen){t();return}const l=()=>{r.off("change",l),t()};r.on("change",l);const h=document[_.exitFullscreen]();h instanceof Promise&&h.then(l).catch(i)})},toggle(t,i){return r.isFullscreen?r.exit():r.request(t,i)},onchange(t){r.on("change",t)},onerror(t){r.on("error",t)},on(t,i){const l=A[t];l&&document.addEventListener(l,i,!1)},off(t,i){const l=A[t];l&&document.removeEventListener(l,i,!1)},raw:_};Object.defineProperties(r,{isFullscreen:{get:()=>!!document[_.fullscreenElement]},element:{enumerable:!0,get:()=>document[_.fullscreenElement]??void 0},isEnabled:{enumerable:!0,get:()=>!!document[_.fullscreenEnabled]}});_||(r={isEnabled:!1});const ve=de("messageCenter",{state:()=>({unread:0}),actions:{async setUnread(t){this.unread=t}}}),Se={class:"layout-navbars-breadcrumb-user-icon"},Ce=["title"],Ee={class:"layout-navbars-breadcrumb-user-icon"},ke=["title"],Fe=["title"],ze={class:"layout-navbars-breadcrumb-user-icon"},ye=["title"],Ie={class:"layout-navbars-breadcrumb-user-link"},Be={key:0},Oe=["src"],$e=D({name:"layoutBreadcrumbUser"}),Me=D({...$e,setup(t){const i=x(()=>T(()=>import("./userNews.BkO95KNa.js"),__vite__mapDeps([0,1,2,3,4,5]))),l=x(()=>T(()=>import("./search.Dc1pRj-_.js"),__vite__mapDeps([6,1,2,3,4,7]))),{locale:h,t:d}=te.useI18n(),w=me(),U=L(),V=oe(),{userInfos:k}=B(U),{themeConfig:S}=B(V),$=fe(),c=ge({isScreenfull:!1,disabledI18n:"zh-cn",disabledSize:"large"}),j=be(()=>{let e="";const{layout:a,isClassicSplitMenu:o}=S.value;return["defaults","columns"].includes(a)||a==="classic"&&!o?e="1":e="",e}),{isSocketOpen:F}=B(L()),H=()=>{if(!r.isEnabled)return re.warning("暂不不支持全屏"),!1;r.toggle(),r.on("change",()=>{r.isFullscreen?c.isScreenfull=!0:c.isScreenfull=!1})},Z=()=>{ie.emit("openSetingsDrawer")},G=e=>{e==="logOut"?ue({closeOnClickModal:!1,closeOnPressEscape:!1,title:d("message.user.logOutTitle"),message:d("message.user.logOutMessage"),showCancelButton:!0,confirmButtonText:d("message.user.logOutConfirm"),cancelButtonText:d("message.user.logOutCancel"),buttonSize:"default",beforeClose:(a,o,g)=>{a==="confirm"?(o.confirmButtonLoading=!0,o.confirmButtonText=d("message.user.logOutExit"),setTimeout(()=>{g(),setTimeout(()=>{o.confirmButtonLoading=!1},300)},700)):g()}}).then(async()=>{P.clear(),window.location.reload()}).catch(()=>{}):e==="wareHouse"?window.open("https://gitee.com/huge-dream/django-vue3-admin"):w.push(e)},J=()=>{$.value.openSearch()},K=e=>{v.remove("themeConfig"),S.value.globalComponentSize=e,v.set("themeConfig",S.value),E("globalComponentSize","disabledSize"),window.location.reload()},Q=e=>{v.remove("themeConfig"),S.value.globalI18n=e,v.set("themeConfig",S.value),h.value=e,ce.useTitle(),E("globalI18n","disabledI18n")},E=(e,a)=>{const o=v.get("themeConfig"),g=o&&o[e]||"";c[a]=g};_e(()=>{v.get("themeConfig")&&(E("globalComponentSize","disabledSize"),E("globalI18n","disabledI18n")),X()});const z=ve();let C=null;const W=P.get("token"),X=()=>{C=new EventSource(`${le()}/sse/?token=${W}`),C.onmessage=function(e){z.setUnread(+e.data)},C.onerror=function(e){console.error("SSE 错误:",e),C!==null&&C.readyState===EventSource.CLOSED&&console.log("连接已关闭")}};return(e,a)=>{const o=b("el-dropdown-item"),g=b("el-dropdown-menu"),y=b("el-dropdown"),Y=b("ele-Search"),I=b("el-icon"),ee=b("ele-Bell"),M=b("el-badge"),ne=b("el-popover"),se=b("ele-ArrowDown");return q(),R("div",{class:"layout-navbars-breadcrumb-user pr15",style:pe({flex:j.value})},[n(y,{"show-timeout":70,"hide-timeout":50,trigger:"click",onCommand:K},{dropdown:s(()=>[n(g,null,{default:s(()=>[n(o,{command:"large",disabled:c.disabledSize==="large"},{default:s(()=>[m(p(e.$t("message.user.dropdownLarge")),1)]),_:1},8,["disabled"]),n(o,{command:"default",disabled:c.disabledSize==="default"},{default:s(()=>[m(p(e.$t("message.user.dropdownDefault")),1)]),_:1},8,["disabled"]),n(o,{command:"small",disabled:c.disabledSize==="small"},{default:s(()=>[m(p(e.$t("message.user.dropdownSmall")),1)]),_:1},8,["disabled"])]),_:1})]),default:s(()=>[u("div",Se,[u("i",{class:"iconfont icon-ziti",title:e.$t("message.user.title0")},null,8,Ce)])]),_:1}),n(y,{"show-timeout":70,"hide-timeout":50,trigger:"click",onCommand:Q},{dropdown:s(()=>[n(g,null,{default:s(()=>[n(o,{command:"zh-cn",disabled:c.disabledI18n==="zh-cn"},{default:s(()=>a[0]||(a[0]=[m("简体中文")])),_:1,__:[0]},8,["disabled"]),n(o,{command:"en",disabled:c.disabledI18n==="en"},{default:s(()=>a[1]||(a[1]=[m("English")])),_:1,__:[1]},8,["disabled"]),n(o,{command:"zh-tw",disabled:c.disabledI18n==="zh-tw"},{default:s(()=>a[2]||(a[2]=[m("繁體中文")])),_:1,__:[2]},8,["disabled"])]),_:1})]),default:s(()=>[u("div",Ee,[u("i",{class:O(["iconfont",c.disabledI18n==="en"?"icon-fuhao-yingwen":"icon-fuhao-zhongwen"]),title:e.$t("message.user.title1")},null,10,ke)])]),_:1}),u("div",{class:"layout-navbars-breadcrumb-user-icon",onClick:J},[n(I,{title:e.$t("message.user.title2")},{default:s(()=>[n(Y)]),_:1},8,["title"])]),u("div",{class:"layout-navbars-breadcrumb-user-icon",onClick:Z},[u("i",{class:"icon-skin iconfont",title:e.$t("message.user.title3")},null,8,Fe)]),u("div",ze,[n(ne,{placement:"bottom",trigger:"hover",transition:"el-zoom-in-top",width:300,persistent:!1},{reference:s(()=>[n(M,{value:f(z).unread,hidden:f(z).unread===0},{default:s(()=>[n(I,{title:e.$t("message.user.title4")},{default:s(()=>[n(ee)]),_:1},8,["title"])]),_:1},8,["value","hidden"])]),default:s(()=>[n(f(i))]),_:1})]),u("div",{class:"layout-navbars-breadcrumb-user-icon mr10",onClick:H},[u("i",{class:O(["iconfont",c.isScreenfull?"icon-tuichuquanping":"icon-fullscreen"]),title:c.isScreenfull?e.$t("message.user.title6"):e.$t("message.user.title5")},null,10,ye)]),a[4]||(a[4]=u("div",null,null,-1)),n(y,{"show-timeout":70,"hide-timeout":50,onCommand:G},{dropdown:s(()=>[n(g,null,{default:s(()=>[n(o,{command:"/home"},{default:s(()=>[m(p(e.$t("message.user.dropdown1")),1)]),_:1}),n(o,{command:"/personal"},{default:s(()=>[m(p(e.$t("message.user.dropdown2")),1)]),_:1}),n(o,{command:"/versionUpgradeLog"},{default:s(()=>a[3]||(a[3]=[m("更新日志")])),_:1,__:[3]}),n(o,{divided:"",command:"logOut"},{default:s(()=>[m(p(e.$t("message.user.dropdown5")),1)]),_:1})]),_:1})]),default:s(()=>[u("span",Ie,[f(F)?(q(),R("span",Be,[n(M,{"is-dot":"",class:O(["item",{"online-status":f(F),"online-down":!f(F)}])},{default:s(()=>[u("img",{src:f(k).avatar||f(ae),class:"layout-navbars-breadcrumb-user-link-photo mr5"},null,8,Oe)]),_:1},8,["class"])])):he("",!0),m(" "+p(f(k).username===""?"common":f(k).username)+" ",1),n(I,{class:"el-icon--right"},{default:s(()=>[n(se)]),_:1})])]),_:1}),n(f(l),{ref_key:"searchRef",ref:$},null,512)],4)}}}),Re=we(Me,[["__scopeId","data-v-4ef10f29"]]);export{Re as default};
