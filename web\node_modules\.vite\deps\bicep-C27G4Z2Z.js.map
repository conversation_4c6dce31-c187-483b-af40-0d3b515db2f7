{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/bicep/bicep.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/bicep/bicep.ts\nvar bounded = (text) => `\\\\b${text}\\\\b`;\nvar identifierStart = \"[_a-zA-Z]\";\nvar identifierContinue = \"[_a-zA-Z0-9]\";\nvar identifier = bounded(`${identifierStart}${identifierContinue}*`);\nvar keywords = [\n  \"targetScope\",\n  \"resource\",\n  \"module\",\n  \"param\",\n  \"var\",\n  \"output\",\n  \"for\",\n  \"in\",\n  \"if\",\n  \"existing\"\n];\nvar namedLiterals = [\"true\", \"false\", \"null\"];\nvar nonCommentWs = `[ \\\\t\\\\r\\\\n]`;\nvar numericLiteral = `[0-9]+`;\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\" },\n    { open: \"'''\", close: \"'''\" }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"'''\", close: \"'''\", notIn: [\"string\", \"comment\"] }\n  ],\n  autoCloseBefore: \":.,=}])' \\n\t\",\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\"^((?!\\\\/\\\\/).)*(\\\\{[^}\\\"'`]*|\\\\([^)\\\"'`]*|\\\\[[^\\\\]\\\"'`]*)$\"),\n    decreaseIndentPattern: new RegExp(\"^((?!.*?\\\\/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\")\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".bicep\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  symbols: /[=><!~?:&|+\\-*/^%]+/,\n  keywords,\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|'|\\\\\\${)`,\n  tokenizer: {\n    root: [{ include: \"@expression\" }, { include: \"@whitespace\" }],\n    stringVerbatim: [\n      { regex: `(|'|'')[^']`, action: { token: \"string\" } },\n      { regex: `'''`, action: { token: \"string.quote\", next: \"@pop\" } }\n    ],\n    stringLiteral: [\n      { regex: `\\\\\\${`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `[^\\\\\\\\'$]+`, action: { token: \"string\" } },\n      { regex: \"@escapes\", action: { token: \"string.escape\" } },\n      { regex: `\\\\\\\\.`, action: { token: \"string.escape.invalid\" } },\n      { regex: `'`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    bracketCounting: [\n      { regex: `{`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `}`, action: { token: \"delimiter.bracket\", next: \"@pop\" } },\n      { include: \"expression\" }\n    ],\n    comment: [\n      { regex: `[^\\\\*]+`, action: { token: \"comment\" } },\n      { regex: `\\\\*\\\\/`, action: { token: \"comment\", next: \"@pop\" } },\n      { regex: `[\\\\/*]`, action: { token: \"comment\" } }\n    ],\n    whitespace: [\n      { regex: nonCommentWs },\n      { regex: `\\\\/\\\\*`, action: { token: \"comment\", next: \"@comment\" } },\n      { regex: `\\\\/\\\\/.*$`, action: { token: \"comment\" } }\n    ],\n    expression: [\n      { regex: `'''`, action: { token: \"string.quote\", next: \"@stringVerbatim\" } },\n      { regex: `'`, action: { token: \"string.quote\", next: \"@stringLiteral\" } },\n      { regex: numericLiteral, action: { token: \"number\" } },\n      {\n        regex: identifier,\n        action: {\n          cases: {\n            \"@keywords\": { token: \"keyword\" },\n            \"@namedLiterals\": { token: \"keyword\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,UAAU,CAAC,SAAS,MAAM,IAAI;AAClC,IAAI,kBAAkB;AACtB,IAAI,qBAAqB;AACzB,IAAI,aAAa,QAAQ,GAAG,eAAe,GAAG,kBAAkB,GAAG;AACnE,IAAI,WAAW;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,gBAAgB,CAAC,QAAQ,SAAS,MAAM;AAC5C,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,EAC9B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC5D;AAAA,EACA,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,IAChB,uBAAuB,IAAI,OAAO,4DAA4D;AAAA,IAC9F,uBAAuB,IAAI,OAAO,wCAAwC;AAAA,EAC5E;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM,CAAC,EAAE,SAAS,cAAc,GAAG,EAAE,SAAS,cAAc,CAAC;AAAA,IAC7D,gBAAgB;AAAA,MACd,EAAE,OAAO,eAAe,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,MACpD,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,gBAAgB,MAAM,OAAO,EAAE;AAAA,IAClE;AAAA,IACA,eAAe;AAAA,MACb,EAAE,OAAO,SAAS,QAAQ,EAAE,OAAO,qBAAqB,MAAM,mBAAmB,EAAE;AAAA,MACnF,EAAE,OAAO,cAAc,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,MACnD,EAAE,OAAO,YAAY,QAAQ,EAAE,OAAO,gBAAgB,EAAE;AAAA,MACxD,EAAE,OAAO,SAAS,QAAQ,EAAE,OAAO,wBAAwB,EAAE;AAAA,MAC7D,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,UAAU,MAAM,OAAO,EAAE;AAAA,IAC1D;AAAA,IACA,iBAAiB;AAAA,MACf,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,qBAAqB,MAAM,mBAAmB,EAAE;AAAA,MAC/E,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,qBAAqB,MAAM,OAAO,EAAE;AAAA,MACnE,EAAE,SAAS,aAAa;AAAA,IAC1B;AAAA,IACA,SAAS;AAAA,MACP,EAAE,OAAO,WAAW,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,MACjD,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,WAAW,MAAM,OAAO,EAAE;AAAA,MAC9D,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,IAClD;AAAA,IACA,YAAY;AAAA,MACV,EAAE,OAAO,aAAa;AAAA,MACtB,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,WAAW,MAAM,WAAW,EAAE;AAAA,MAClE,EAAE,OAAO,aAAa,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,IACrD;AAAA,IACA,YAAY;AAAA,MACV,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,gBAAgB,MAAM,kBAAkB,EAAE;AAAA,MAC3E,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,gBAAgB,MAAM,iBAAiB,EAAE;AAAA,MACxE,EAAE,OAAO,gBAAgB,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,MACrD;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,UAAU;AAAA,YAChC,kBAAkB,EAAE,OAAO,UAAU;AAAA,YACrC,YAAY,EAAE,OAAO,aAAa;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}