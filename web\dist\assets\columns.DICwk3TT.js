const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/aside.D_PtffXs.js","assets/index.GuQX7xXE.js","assets/vue.zNq9Glab.js","assets/index.WGbL7eFL.css","assets/header.BPbW56P8.js","assets/main.Mab_8bra.js","assets/columnsAside.DiBFgY_H.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/columnsAside.TyXXNMHi.css"])))=>i.map(i=>d[i]);
import{u as C,_ as o}from"./index.GuQX7xXE.js";import{d as p,a as _,Q as E,M as w,o as A,m as i,b as f,g as L,e as M,w as r,f as e,u as t,P as a,I as k}from"./vue.zNq9Glab.js";const P=p({name:"layoutColumns"}),D=p({...P,setup(S){const m=a(()=>o(()=>import("./aside.D_PtffXs.js"),__vite__mapDeps([0,1,2,3]))),d=a(()=>o(()=>import("./header.BPbW56P8.js"),__vite__mapDeps([4,1,2,3]))),y=a(()=>o(()=>import("./main.Mab_8bra.js"),__vite__mapDeps([5,1,2,3]))),R=a(()=>o(()=>import("./columnsAside.DiBFgY_H.js"),__vite__mapDeps([6,2,1,3,7,8]))),s=_(""),n=_(),v=E(),T=C(),{themeConfig:b}=w(T),l=()=>{s.value.update(),n.value.layoutMainScrollbarRef.update()},c=()=>{k(()=>{setTimeout(()=>{l(),s.value.wrapRef.scrollTop=0,n.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return A(()=>{c()}),i(()=>v.path,()=>{c()}),i(b,()=>{l()},{deep:!0}),(g,I)=>{const h=f("el-scrollbar"),u=f("el-container");return M(),L(u,{class:"layout-container"},{default:r(()=>[e(t(R)),e(u,{class:"layout-columns-warp layout-container-view h100"},{default:r(()=>[e(t(m)),e(h,{ref_key:"layoutScrollbarRef",ref:s,class:"layout-backtop"},{default:r(()=>[e(t(d)),e(t(y),{ref_key:"layoutMainRef",ref:n},null,512)]),_:1},512)]),_:1})]),_:1})}}});export{D as default};
