import{a6 as he,C as de,m as pe,f as ye,h as ge}from"./index.GuQX7xXE.js";import"./vue.zNq9Glab.js";var re=function(){var r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,i){o.__proto__=i}||function(o,i){for(var n in i)i.hasOwnProperty(n)&&(o[n]=i[n])},r(t,e)};return function(t,e){r(t,e);function o(){this.constructor=t}t.prototype=e===null?Object.create(e):(o.prototype=e.prototype,new o)}}(),b;(function(r){r.InvalidFile="InvalidFile",r.InvalidToken="InvalidToken",r.InvalidMetadata="InvalidMetadata",r.InvalidChunkSize="InvalidChunkSize",r.InvalidCustomVars="InvalidCustomVars",r.NotAvailableUploadHost="NotAvailableUploadHost",r.ReadCacheFailed="ReadCacheFailed",r.InvalidCacheData="InvalidCacheData",r.WriteCacheFailed="WriteCacheFailed",r.RemoveCacheFailed="RemoveCacheFailed",r.GetCanvasContextFailed="GetCanvasContextFailed",r.UnsupportedFileType="UnsupportedFileType",r.FileReaderReadFailed="FileReaderReadFailed",r.NotAvailableXMLHttpRequest="NotAvailableXMLHttpRequest",r.InvalidProgressEventTarget="InvalidProgressEventTarget",r.RequestError="RequestError"})(b||(b={}));var w=function(){function r(t,e){this.name=t,this.message=e,this.stack=new Error().stack}return r}(),H=function(r){re(t,r);function t(e,o,i,n){var c=r.call(this,b.RequestError,i)||this;return c.code=e,c.reqId=o,c.isRequestError=!0,c.data=n,c}return t}(w),ve=function(r){re(t,r);function t(e,o){return o===void 0&&(o=""),r.call(this,0,o,e)||this}return t}(H),me=function(){function r(t,e){this.runTask=t,this.limit=e,this.aborted=!1,this.queue=[],this.processing=[]}return r.prototype.enqueue=function(t){var e=this;return new Promise(function(o,i){e.queue.push({task:t,resolve:o,reject:i}),e.check()})},r.prototype.run=function(t){var e=this;this.queue=this.queue.filter(function(o){return o!==t}),this.processing.push(t),this.runTask(t.task).then(function(){e.processing=e.processing.filter(function(o){return o!==t}),t.resolve(),e.check()},function(o){return t.reject(o)})},r.prototype.check=function(){var t=this;if(!this.aborted){var e=this.processing.length,o=this.limit-e;this.queue.slice(0,o).forEach(function(i){t.run(i)})}},r.prototype.abort=function(){this.queue=[],this.aborted=!0},r}(),be=function(){var r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,i){o.__proto__=i}||function(o,i){for(var n in i)i.hasOwnProperty(n)&&(o[n]=i[n])},r(t,e)};return function(t,e){r(t,e);function o(){this.constructor=t}t.prototype=e===null?Object.create(e):(o.prototype=e.prototype,new o)}}(),L=function(){return L=Object.assign||function(r){for(var t,e=1,o=arguments.length;e<o;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=t[i])}return r},L.apply(this,arguments)},we=function(){function r(){this.closed=!1}return r.prototype.unsubscribe=function(){this.closed||(this.closed=!0,this._unsubscribe&&this._unsubscribe())},r.prototype.add=function(t){this._unsubscribe=t},r}(),_e=function(r){be(t,r);function t(e,o,i){var n=r.call(this)||this;return n.isStopped=!1,e&&typeof e=="object"?n.destination=e:n.destination=L(L(L({},e&&{next:e}),o&&{error:o}),i&&{complete:i}),n}return t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,r.prototype.unsubscribe.call(this))},t.prototype.next=function(e){!this.isStopped&&this.destination.next&&this.destination.next(e)},t.prototype.error=function(e){!this.isStopped&&this.destination.error&&(this.isStopped=!0,this.destination.error(e))},t.prototype.complete=function(e){!this.isStopped&&this.destination.complete&&(this.isStopped=!0,this.destination.complete(e))},t}(we),xe=function(){function r(t){this._subscribe=t}return r.prototype.subscribe=function(t,e,o){var i=new _e(t,e,o);return i.add(this._subscribe(i)),i},r}();function Ce(r){if(r===null||typeof r>"u")return"";var t=r+"",e="",o,i,n=0;o=i=0,n=t.length;for(var c=0;c<n;c++){var p=t.charCodeAt(c),g=null;if(p<128)i++;else if(p>127&&p<2048)g=String.fromCharCode(p>>6|192,p&63|128);else if((p&63488^55296)>0)g=String.fromCharCode(p>>12|224,p>>6&63|128,p&63|128);else{if((p&64512^55296)>0)throw new RangeError("Unmatched trail surrogate at "+c);var a=t.charCodeAt(++c);if((a&64512^56320)>0)throw new RangeError("Unmatched lead surrogate at "+(c-1));p=((p&1023)<<10)+(a&1023)+65536,g=String.fromCharCode(p>>18|240,p>>12&63|128,p>>6&63|128,p&63|128)}g!==null&&(i>o&&(e+=t.slice(o,i)),e+=g,o=i=c+1)}return i>o&&(e+=t.slice(o,n)),e}function Se(r){var t=[],e=0,o=0,i=0;for(r+="";e<r.length;){o=r.charCodeAt(e)&255,i=0,o<=191?(o=o&127,i=1):o<=223?(o=o&31,i=2):o<=239?(o=o&15,i=3):(o=o&7,i=4);for(var n=1;n<i;++n)o=o<<6|r.charCodeAt(n+e)&63;i===4?(o-=65536,t.push(String.fromCharCode(55296|o>>10&1023)),t.push(String.fromCharCode(56320|o&1023))):t.push(String.fromCharCode(o)),e+=i}return t.join("")}function ke(r){var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",e,o,i,n,c,p,g,a,d=0,v=0,_="",C=[];if(!r)return r;r=Ce(r+"");do e=r.charCodeAt(d++),o=r.charCodeAt(d++),i=r.charCodeAt(d++),a=e<<16|o<<8|i,n=a>>18&63,c=a>>12&63,p=a>>6&63,g=a&63,C[v++]=t.charAt(n)+t.charAt(c)+t.charAt(p)+t.charAt(g);while(d<r.length);switch(_=C.join(""),r.length%3){case 1:_=_.slice(0,-2)+"==";break;case 2:_=_.slice(0,-1)+"=";break}return _}function Ae(r){var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",e,o,i,n,c,p,g,a,d=0,v=0,_="",C=[];if(!r)return r;r+="";do n=t.indexOf(r.charAt(d++)),c=t.indexOf(r.charAt(d++)),p=t.indexOf(r.charAt(d++)),g=t.indexOf(r.charAt(d++)),a=n<<18|c<<12|p<<6|g,e=a>>16&255,o=a>>8&255,i=a&255,p===64?C[v++]=String.fromCharCode(e):g===64?C[v++]=String.fromCharCode(e,o):C[v++]=String.fromCharCode(e,o,i);while(d<r.length);return _=C.join(""),Se(_)}function ie(r){return r=ke(r),r.replace(/\//g,"_").replace(/\+/g,"-")}function Ie(r){return r=r.replace(/_/g,"/").replace(/-/g,"+"),Ae(r)}var oe={exports:{}};(function(r,t){(function(e){r.exports=e()})(function(e){var o=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function i(y,h){var u=y[0],s=y[1],l=y[2],f=y[3];u+=(s&l|~s&f)+h[0]-680876936|0,u=(u<<7|u>>>25)+s|0,f+=(u&s|~u&l)+h[1]-389564586|0,f=(f<<12|f>>>20)+u|0,l+=(f&u|~f&s)+h[2]+606105819|0,l=(l<<17|l>>>15)+f|0,s+=(l&f|~l&u)+h[3]-1044525330|0,s=(s<<22|s>>>10)+l|0,u+=(s&l|~s&f)+h[4]-176418897|0,u=(u<<7|u>>>25)+s|0,f+=(u&s|~u&l)+h[5]+1200080426|0,f=(f<<12|f>>>20)+u|0,l+=(f&u|~f&s)+h[6]-1473231341|0,l=(l<<17|l>>>15)+f|0,s+=(l&f|~l&u)+h[7]-45705983|0,s=(s<<22|s>>>10)+l|0,u+=(s&l|~s&f)+h[8]+1770035416|0,u=(u<<7|u>>>25)+s|0,f+=(u&s|~u&l)+h[9]-1958414417|0,f=(f<<12|f>>>20)+u|0,l+=(f&u|~f&s)+h[10]-42063|0,l=(l<<17|l>>>15)+f|0,s+=(l&f|~l&u)+h[11]-1990404162|0,s=(s<<22|s>>>10)+l|0,u+=(s&l|~s&f)+h[12]+1804603682|0,u=(u<<7|u>>>25)+s|0,f+=(u&s|~u&l)+h[13]-40341101|0,f=(f<<12|f>>>20)+u|0,l+=(f&u|~f&s)+h[14]-1502002290|0,l=(l<<17|l>>>15)+f|0,s+=(l&f|~l&u)+h[15]+1236535329|0,s=(s<<22|s>>>10)+l|0,u+=(s&f|l&~f)+h[1]-165796510|0,u=(u<<5|u>>>27)+s|0,f+=(u&l|s&~l)+h[6]-1069501632|0,f=(f<<9|f>>>23)+u|0,l+=(f&s|u&~s)+h[11]+643717713|0,l=(l<<14|l>>>18)+f|0,s+=(l&u|f&~u)+h[0]-373897302|0,s=(s<<20|s>>>12)+l|0,u+=(s&f|l&~f)+h[5]-701558691|0,u=(u<<5|u>>>27)+s|0,f+=(u&l|s&~l)+h[10]+38016083|0,f=(f<<9|f>>>23)+u|0,l+=(f&s|u&~s)+h[15]-660478335|0,l=(l<<14|l>>>18)+f|0,s+=(l&u|f&~u)+h[4]-405537848|0,s=(s<<20|s>>>12)+l|0,u+=(s&f|l&~f)+h[9]+568446438|0,u=(u<<5|u>>>27)+s|0,f+=(u&l|s&~l)+h[14]-1019803690|0,f=(f<<9|f>>>23)+u|0,l+=(f&s|u&~s)+h[3]-187363961|0,l=(l<<14|l>>>18)+f|0,s+=(l&u|f&~u)+h[8]+1163531501|0,s=(s<<20|s>>>12)+l|0,u+=(s&f|l&~f)+h[13]-1444681467|0,u=(u<<5|u>>>27)+s|0,f+=(u&l|s&~l)+h[2]-51403784|0,f=(f<<9|f>>>23)+u|0,l+=(f&s|u&~s)+h[7]+1735328473|0,l=(l<<14|l>>>18)+f|0,s+=(l&u|f&~u)+h[12]-1926607734|0,s=(s<<20|s>>>12)+l|0,u+=(s^l^f)+h[5]-378558|0,u=(u<<4|u>>>28)+s|0,f+=(u^s^l)+h[8]-2022574463|0,f=(f<<11|f>>>21)+u|0,l+=(f^u^s)+h[11]+1839030562|0,l=(l<<16|l>>>16)+f|0,s+=(l^f^u)+h[14]-35309556|0,s=(s<<23|s>>>9)+l|0,u+=(s^l^f)+h[1]-1530992060|0,u=(u<<4|u>>>28)+s|0,f+=(u^s^l)+h[4]+1272893353|0,f=(f<<11|f>>>21)+u|0,l+=(f^u^s)+h[7]-155497632|0,l=(l<<16|l>>>16)+f|0,s+=(l^f^u)+h[10]-1094730640|0,s=(s<<23|s>>>9)+l|0,u+=(s^l^f)+h[13]+681279174|0,u=(u<<4|u>>>28)+s|0,f+=(u^s^l)+h[0]-358537222|0,f=(f<<11|f>>>21)+u|0,l+=(f^u^s)+h[3]-722521979|0,l=(l<<16|l>>>16)+f|0,s+=(l^f^u)+h[6]+76029189|0,s=(s<<23|s>>>9)+l|0,u+=(s^l^f)+h[9]-640364487|0,u=(u<<4|u>>>28)+s|0,f+=(u^s^l)+h[12]-421815835|0,f=(f<<11|f>>>21)+u|0,l+=(f^u^s)+h[15]+530742520|0,l=(l<<16|l>>>16)+f|0,s+=(l^f^u)+h[2]-995338651|0,s=(s<<23|s>>>9)+l|0,u+=(l^(s|~f))+h[0]-198630844|0,u=(u<<6|u>>>26)+s|0,f+=(s^(u|~l))+h[7]+1126891415|0,f=(f<<10|f>>>22)+u|0,l+=(u^(f|~s))+h[14]-1416354905|0,l=(l<<15|l>>>17)+f|0,s+=(f^(l|~u))+h[5]-57434055|0,s=(s<<21|s>>>11)+l|0,u+=(l^(s|~f))+h[12]+1700485571|0,u=(u<<6|u>>>26)+s|0,f+=(s^(u|~l))+h[3]-1894986606|0,f=(f<<10|f>>>22)+u|0,l+=(u^(f|~s))+h[10]-1051523|0,l=(l<<15|l>>>17)+f|0,s+=(f^(l|~u))+h[1]-2054922799|0,s=(s<<21|s>>>11)+l|0,u+=(l^(s|~f))+h[8]+1873313359|0,u=(u<<6|u>>>26)+s|0,f+=(s^(u|~l))+h[15]-30611744|0,f=(f<<10|f>>>22)+u|0,l+=(u^(f|~s))+h[6]-1560198380|0,l=(l<<15|l>>>17)+f|0,s+=(f^(l|~u))+h[13]+1309151649|0,s=(s<<21|s>>>11)+l|0,u+=(l^(s|~f))+h[4]-145523070|0,u=(u<<6|u>>>26)+s|0,f+=(s^(u|~l))+h[11]-1120210379|0,f=(f<<10|f>>>22)+u|0,l+=(u^(f|~s))+h[2]+718787259|0,l=(l<<15|l>>>17)+f|0,s+=(f^(l|~u))+h[9]-343485551|0,s=(s<<21|s>>>11)+l|0,y[0]=u+y[0]|0,y[1]=s+y[1]|0,y[2]=l+y[2]|0,y[3]=f+y[3]|0}function n(y){var h=[],u;for(u=0;u<64;u+=4)h[u>>2]=y.charCodeAt(u)+(y.charCodeAt(u+1)<<8)+(y.charCodeAt(u+2)<<16)+(y.charCodeAt(u+3)<<24);return h}function c(y){var h=[],u;for(u=0;u<64;u+=4)h[u>>2]=y[u]+(y[u+1]<<8)+(y[u+2]<<16)+(y[u+3]<<24);return h}function p(y){var h=y.length,u=[1732584193,-271733879,-1732584194,271733878],s,l,f,x,S,k;for(s=64;s<=h;s+=64)i(u,n(y.substring(s-64,s)));for(y=y.substring(s-64),l=y.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],s=0;s<l;s+=1)f[s>>2]|=y.charCodeAt(s)<<(s%4<<3);if(f[s>>2]|=128<<(s%4<<3),s>55)for(i(u,f),s=0;s<16;s+=1)f[s]=0;return x=h*8,x=x.toString(16).match(/(.*?)(.{0,8})$/),S=parseInt(x[2],16),k=parseInt(x[1],16)||0,f[14]=S,f[15]=k,i(u,f),u}function g(y){var h=y.length,u=[1732584193,-271733879,-1732584194,271733878],s,l,f,x,S,k;for(s=64;s<=h;s+=64)i(u,c(y.subarray(s-64,s)));for(y=s-64<h?y.subarray(s-64):new Uint8Array(0),l=y.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],s=0;s<l;s+=1)f[s>>2]|=y[s]<<(s%4<<3);if(f[s>>2]|=128<<(s%4<<3),s>55)for(i(u,f),s=0;s<16;s+=1)f[s]=0;return x=h*8,x=x.toString(16).match(/(.*?)(.{0,8})$/),S=parseInt(x[2],16),k=parseInt(x[1],16)||0,f[14]=S,f[15]=k,i(u,f),u}function a(y){var h="",u;for(u=0;u<4;u+=1)h+=o[y>>u*8+4&15]+o[y>>u*8&15];return h}function d(y){var h;for(h=0;h<y.length;h+=1)y[h]=a(y[h]);return y.join("")}d(p("hello")),typeof ArrayBuffer<"u"&&!ArrayBuffer.prototype.slice&&function(){function y(h,u){return h=h|0||0,h<0?Math.max(h+u,0):Math.min(h,u)}ArrayBuffer.prototype.slice=function(h,u){var s=this.byteLength,l=y(h,s),f=s,x,S,k,J;return u!==e&&(f=y(u,s)),l>f?new ArrayBuffer(0):(x=f-l,S=new ArrayBuffer(x),k=new Uint8Array(S),J=new Uint8Array(this,l,x),k.set(J),S)}}();function v(y){return/[\u0080-\uFFFF]/.test(y)&&(y=unescape(encodeURIComponent(y))),y}function _(y,h){var u=y.length,s=new ArrayBuffer(u),l=new Uint8Array(s),f;for(f=0;f<u;f+=1)l[f]=y.charCodeAt(f);return h?l:s}function C(y){return String.fromCharCode.apply(null,new Uint8Array(y))}function z(y,h,u){var s=new Uint8Array(y.byteLength+h.byteLength);return s.set(new Uint8Array(y)),s.set(new Uint8Array(h),y.byteLength),s}function D(y){var h=[],u=y.length,s;for(s=0;s<u-1;s+=2)h.push(parseInt(y.substr(s,2),16));return String.fromCharCode.apply(String,h)}function m(){this.reset()}return m.prototype.append=function(y){return this.appendBinary(v(y)),this},m.prototype.appendBinary=function(y){this._buff+=y,this._length+=y.length;var h=this._buff.length,u;for(u=64;u<=h;u+=64)i(this._hash,n(this._buff.substring(u-64,u)));return this._buff=this._buff.substring(u-64),this},m.prototype.end=function(y){var h=this._buff,u=h.length,s,l=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],f;for(s=0;s<u;s+=1)l[s>>2]|=h.charCodeAt(s)<<(s%4<<3);return this._finish(l,u),f=d(this._hash),y&&(f=D(f)),this.reset(),f},m.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},m.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash.slice()}},m.prototype.setState=function(y){return this._buff=y.buff,this._length=y.length,this._hash=y.hash,this},m.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},m.prototype._finish=function(y,h){var u=h,s,l,f;if(y[u>>2]|=128<<(u%4<<3),u>55)for(i(this._hash,y),u=0;u<16;u+=1)y[u]=0;s=this._length*8,s=s.toString(16).match(/(.*?)(.{0,8})$/),l=parseInt(s[2],16),f=parseInt(s[1],16)||0,y[14]=l,y[15]=f,i(this._hash,y)},m.hash=function(y,h){return m.hashBinary(v(y),h)},m.hashBinary=function(y,h){var u=p(y),s=d(u);return h?D(s):s},m.ArrayBuffer=function(){this.reset()},m.ArrayBuffer.prototype.append=function(y){var h=z(this._buff.buffer,y),u=h.length,s;for(this._length+=y.byteLength,s=64;s<=u;s+=64)i(this._hash,c(h.subarray(s-64,s)));return this._buff=s-64<u?new Uint8Array(h.buffer.slice(s-64)):new Uint8Array(0),this},m.ArrayBuffer.prototype.end=function(y){var h=this._buff,u=h.length,s=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],l,f;for(l=0;l<u;l+=1)s[l>>2]|=h[l]<<(l%4<<3);return this._finish(s,u),f=d(this._hash),y&&(f=D(f)),this.reset(),f},m.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},m.ArrayBuffer.prototype.getState=function(){var y=m.prototype.getState.call(this);return y.buff=C(y.buff),y},m.ArrayBuffer.prototype.setState=function(y){return y.buff=_(y.buff,!0),m.prototype.setState.call(this,y)},m.ArrayBuffer.prototype.destroy=m.prototype.destroy,m.ArrayBuffer.prototype._finish=m.prototype._finish,m.ArrayBuffer.hash=function(y,h){var u=g(new Uint8Array(y)),s=d(u);return h?D(s):s},m})})(oe);var Ue=oe.exports;const Fe=he(Ue);var M=function(){return M=Object.assign||function(r){for(var t,e=1,o=arguments.length;e<o;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=t[i])}return r},M.apply(this,arguments)},Oe=function(r,t,e,o){function i(n){return n instanceof e?n:new e(function(c){c(n)})}return new(e||(e=Promise))(function(n,c){function p(d){try{a(o.next(d))}catch(v){c(v)}}function g(d){try{a(o.throw(d))}catch(v){c(v)}}function a(d){d.done?n(d.value):i(d.value).then(p,g)}a((o=o.apply(r,t||[])).next())})},ze=function(r,t){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},o,i,n,c;return c={next:p(0),throw:p(1),return:p(2)},typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function p(a){return function(d){return g([a,d])}}function g(a){if(o)throw new TypeError("Generator is already executing.");for(;e;)try{if(o=1,i&&(n=a[0]&2?i.return:a[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,a[1])).done)return n;switch(i=0,n&&(a=[a[0]&2,n.value]),a[0]){case 0:case 1:n=a;break;case 4:return e.label++,{value:a[1],done:!1};case 5:e.label++,i=a[1],a=[0];continue;case 7:a=e.ops.pop(),e.trys.pop();continue;default:if(n=e.trys,!(n=n.length>0&&n[n.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!n||a[1]>n[0]&&a[1]<n[3])){e.label=a[1];break}if(a[0]===6&&e.label<n[1]){e.label=n[1],n=a;break}if(n&&e.label<n[2]){e.label=n[2],e.ops.push(a);break}n[2]&&e.ops.pop(),e.trys.pop();continue}a=t.call(r,e)}catch(d){a=[6,d],i=0}finally{o=n=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},F=Math.pow(1024,2);function Pe(r,t){var e=t*F;if(e>r.size)e=r.size;else for(;r.size>e*1e4;)e*=2;for(var o=[],i=Math.ceil(r.size/e),n=0;n<i;n++){var c=r.slice(e*n,n===i-1?r.size:e*(n+1));o.push(c)}return o}function Re(r){return Object.keys(r).every(function(t){return t.indexOf("x-qn-meta-")===0})}function Ee(r){return Object.keys(r).every(function(t){return t.indexOf("x:")===0})}function Le(r){return r.reduce(function(t,e){return t+e},0)}function Te(r,t,e){try{localStorage.setItem(r,JSON.stringify(t))}catch{e.warn(new w(b.WriteCacheFailed,"setLocalFileInfo failed: "+r))}}function He(r,t,e){var o=t==null?"_":"_key_"+t+"_";return"qiniu_js_sdk_upload_file_name_"+r+o+"size_"+e}function N(r,t){try{localStorage.removeItem(r)}catch{t.warn(new w(b.RemoveCacheFailed,"removeLocalFileInfo failed. key: "+r))}}function qe(r,t){var e=null;try{e=localStorage.getItem(r)}catch{t.warn(new w(b.ReadCacheFailed,"getLocalFileInfo failed. key: "+r))}if(e==null)return null;var o=null;try{o=JSON.parse(e)}catch{N(r,t),t.warn(new w(b.InvalidCacheData,"getLocalFileInfo failed to parse. key: "+r))}return o}function $(r){var t="UpToken "+r;return{Authorization:t}}function De(r){var t=$(r);return M({"content-type":"application/octet-stream"},t)}function Be(r){var t=$(r);return M({"content-type":"application/json"},t)}function ae(){if(window.XMLHttpRequest)return new XMLHttpRequest;if(window.ActiveXObject)return new window.ActiveXObject("Microsoft.XMLHTTP");throw new w(b.NotAvailableXMLHttpRequest,"the current environment does not support.")}function Me(r){return Oe(this,void 0,void 0,function(){var t,e;return ze(this,function(o){switch(o.label){case 0:return[4,je(r)];case 1:return t=o.sent(),e=new Fe.ArrayBuffer,e.append(t),[2,e.end()]}})})}function je(r){return new Promise(function(t,e){var o=new FileReader;o.onload=function(i){if(i.target){var n=i.target.result;t(n)}else e(new w(b.InvalidProgressEventTarget,"progress event target is undefined"))},o.onerror=function(){e(new w(b.FileReaderReadFailed,"fileReader read failed"))},o.readAsArrayBuffer(r)})}function q(r,t){return new Promise(function(e,o){var i=ae();if(i.open(t.method,r),t.onCreate&&t.onCreate(i),t.headers){var n=t.headers;Object.keys(n).forEach(function(c){i.setRequestHeader(c,n[c])})}i.upload.addEventListener("progress",function(c){c.lengthComputable&&t.onProgress&&t.onProgress({loaded:c.loaded,total:c.total})}),i.onreadystatechange=function(){var c=i.responseText;if(i.readyState===4){var p=i.getResponseHeader("x-reqId")||"";if(i.status===0){o(new ve("network error.",p));return}if(i.status!==200){var g="xhr request failed, code: "+i.status;c&&(g+=" response: "+c);var a=void 0;try{a=JSON.parse(c)}catch{}o(new H(i.status,p,g,a));return}try{e({data:JSON.parse(c),reqId:p})}catch(d){o(d)}}},i.send(t.body)})}function $e(r){if(r&&r.match){var t=r.match(/(^https?)/);if(!t)return"";var e=t[1];return t=r.match(/^https?:\/\/([^:^/]*):(\d*)/),t?t[2]:e==="http"?"80":"443"}return""}function Ve(r){if(r&&r.match){var t=r.match(/^https?:\/\/([^:^/]*)/);return t?t[1]:""}return""}function X(r){if(!r)throw new w(b.InvalidToken,"invalid token.");var t=r.split(":");if(t.length===1)throw new w(b.InvalidToken,"invalid token segments.");var e=t.length>3?t[1]:t[0];if(!e)throw new w(b.InvalidToken,"missing assess key field.");var o=null;try{o=JSON.parse(Ie(t[t.length-1]))}catch{throw new w(b.InvalidToken,"token parse failed.")}if(o==null)throw new w(b.InvalidToken,"putPolicy is null.");if(o.scope==null)throw new w(b.InvalidToken,"scope field is null.");var i=o.scope.split(":")[0];if(!i)throw new w(b.InvalidToken,"resolve bucketName failed.");return{assessKey:e,bucketName:i,scope:o.scope}}var A,U={z0:"z0",z1:"z1",z2:"z2",na0:"na0",as0:"as0",cnEast2:"cn-east-2"},Ge=(A={},A[U.z0]={srcUphost:["up.qiniup.com"],cdnUphost:["upload.qiniup.com"]},A[U.z1]={srcUphost:["up-z1.qiniup.com"],cdnUphost:["upload-z1.qiniup.com"]},A[U.z2]={srcUphost:["up-z2.qiniup.com"],cdnUphost:["upload-z2.qiniup.com"]},A[U.na0]={srcUphost:["up-na0.qiniup.com"],cdnUphost:["upload-na0.qiniup.com"]},A[U.as0]={srcUphost:["up-as0.qiniup.com"],cdnUphost:["upload-as0.qiniup.com"]},A[U.cnEast2]={srcUphost:["up-cn-east-2.qiniup.com"],cdnUphost:["upload-cn-east-2.qiniup.com"]},A),P=function(r){switch(typeof r){case"string":return r;case"boolean":return r?"true":"false";case"number":return isFinite(r)?r:"";default:return""}},Ne=function(r,t,e,o){return t=t||"&",e=e||"=",r===null&&(r=void 0),typeof r=="object"?Object.keys(r).map(function(i){var n=encodeURIComponent(P(i))+e;return Array.isArray(r[i])?r[i].map(function(c){return n+encodeURIComponent(P(c))}).join(t):n+encodeURIComponent(P(r[i]))}).filter(Boolean).join(t):o?encodeURIComponent(P(o))+e+encodeURIComponent(P(r)):""},se;se=Ne;var I=function(){return I=Object.assign||function(r){for(var t,e=1,o=arguments.length;e<o;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=t[i])}return r},I.apply(this,arguments)},Xe=function(r,t,e,o){function i(n){return n instanceof e?n:new e(function(c){c(n)})}return new(e||(e=Promise))(function(n,c){function p(d){try{a(o.next(d))}catch(v){c(v)}}function g(d){try{a(o.throw(d))}catch(v){c(v)}}function a(d){d.done?n(d.value):i(d.value).then(p,g)}a((o=o.apply(r,t||[])).next())})},Je=function(r,t){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},o,i,n,c;return c={next:p(0),throw:p(1),return:p(2)},typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function p(a){return function(d){return g([a,d])}}function g(a){if(o)throw new TypeError("Generator is already executing.");for(;e;)try{if(o=1,i&&(n=a[0]&2?i.return:a[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,a[1])).done)return n;switch(i=0,n&&(a=[a[0]&2,n.value]),a[0]){case 0:case 1:n=a;break;case 4:return e.label++,{value:a[1],done:!1};case 5:e.label++,i=a[1],a=[0];continue;case 7:a=e.ops.pop(),e.trys.pop();continue;default:if(n=e.trys,!(n=n.length>0&&n[n.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!n||a[1]>n[0]&&a[1]<n[3])){e.label=a[1];break}if(a[0]===6&&e.label<n[1]){e.label=n[1],n=a;break}if(n&&e.label<n[2]){e.label=n[2],e.ops.push(a);break}n[2]&&e.ops.pop(),e.trys.pop();continue}a=t.call(r,e)}catch(d){a=[6,d],i=0}finally{o=n=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}};function We(r,t,e){return Xe(this,void 0,void 0,function(){var o,i;return Je(this,function(n){return o=se({ak:r,bucket:t}),i=e+"://api.qiniu.com/v2/query?"+o,[2,q(i,{method:"GET"})]})})}function ue(r,t,e){var o=e.url,i=e.id;return o+"/buckets/"+r+"/objects/"+(t!=null?ie(t):"~")+"/uploads/"+i}function Ke(r,t,e,o){var i=o+"/buckets/"+t+"/objects/"+(e!=null?ie(e):"~")+"/uploads";return q(i,{method:"POST",headers:$(r)})}function Ze(r,t,e,o,i){var n=X(r).bucketName,c=ue(n,t,o)+("/"+e),p=De(r);return i.md5&&(p["Content-MD5"]=i.md5),q(c,I(I({},i),{method:"PUT",headers:p}))}function Ye(r,t,e,o){var i=X(r).bucketName,n=ue(i,t,e);return q(n,I(I({},o),{method:"POST",headers:Be(r)}))}function Qe(r,t,e){return q(r,I({method:"POST",body:t},e))}var j=function(){return j=Object.assign||function(r){for(var t,e=1,o=arguments.length;e<o;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=t[i])}return r},j.apply(this,arguments)},W=function(r,t,e,o){function i(n){return n instanceof e?n:new e(function(c){c(n)})}return new(e||(e=Promise))(function(n,c){function p(d){try{a(o.next(d))}catch(v){c(v)}}function g(d){try{a(o.throw(d))}catch(v){c(v)}}function a(d){d.done?n(d.value):i(d.value).then(p,g)}a((o=o.apply(r,t||[])).next())})},K=function(r,t){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},o,i,n,c;return c={next:p(0),throw:p(1),return:p(2)},typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function p(a){return function(d){return g([a,d])}}function g(a){if(o)throw new TypeError("Generator is already executing.");for(;e;)try{if(o=1,i&&(n=a[0]&2?i.return:a[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,a[1])).done)return n;switch(i=0,n&&(a=[a[0]&2,n.value]),a[0]){case 0:case 1:n=a;break;case 4:return e.label++,{value:a[1],done:!1};case 5:e.label++,i=a[1],a=[0];continue;case 7:a=e.ops.pop(),e.trys.pop();continue;default:if(n=e.trys,!(n=n.length>0&&n[n.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!n||a[1]>n[0]&&a[1]<n[3])){e.label=a[1];break}if(a[0]===6&&e.label<n[1]){e.label=n[1],n=a;break}if(n&&e.label<n[2]){e.label=n[2],e.ops.push(a);break}n[2]&&e.ops.pop(),e.trys.pop();continue}a=t.call(r,e)}catch(d){a=[6,d],i=0}finally{o=n=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},et=function(r,t){var e=typeof Symbol=="function"&&r[Symbol.iterator];if(!e)return r;var o=e.call(r),i,n=[],c;try{for(;(t===void 0||t-- >0)&&!(i=o.next()).done;)n.push(i.value)}catch(p){c={error:p}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(c)throw c.error}}return n},tt=function(){for(var r=[],t=0;t<arguments.length;t++)r=r.concat(et(arguments[t]));return r},nt=4,fe=[0,502,503,504,599],rt=tt(fe,[612]),it=Math.pow(1024,3),ce=function(){function r(t,e,o,i){this.hostPool=o,this.logger=i,this.aborted=!1,this.retryCount=0,this.xhrList=[],this.config=t.config,i.info("config inited.",this.config),this.putExtra=j({fname:""},t.putExtra),i.info("putExtra inited.",this.putExtra),this.key=t.key,this.file=t.file,this.token=t.token,this.onData=e.onData,this.onError=e.onError,this.onComplete=e.onComplete;try{var n=X(this.token);this.bucketName=n.bucketName,this.assessKey=n.assessKey}catch(c){i.error("get putPolicy from token failed.",c),this.onError(c)}}return r.prototype.checkAndUpdateUploadHost=function(){return W(this,void 0,void 0,function(){var t;return K(this,function(e){switch(e.label){case 0:return this.logger.info("get available upload host."),[4,this.hostPool.getUp(this.assessKey,this.bucketName,this.config.upprotocol)];case 1:if(t=e.sent(),t==null)throw new w(b.NotAvailableUploadHost,"no available upload host.");return this.uploadHost!=null&&this.uploadHost.host!==t.host?this.logger.warn("host switches from "+this.uploadHost.host+" to "+t.host+"."):this.logger.info("use host "+t.host+"."),this.uploadHost=t,[2]}})})},r.prototype.checkAndUnfreezeHost=function(){this.logger.info("check unfreeze host."),this.uploadHost!=null&&this.uploadHost.isFrozen()&&(this.logger.warn(this.uploadHost.host+" will be unfrozen."),this.uploadHost.unfreeze())},r.prototype.checkAndFreezeHost=function(t){this.logger.info("check freeze host."),t instanceof H&&this.uploadHost!=null&&fe.includes(t.code)&&(this.logger.warn(this.uploadHost.host+" will be temporarily frozen."),this.uploadHost.freeze())},r.prototype.handleError=function(t){this.logger.error(t.message),this.onError(t)},r.prototype.putFile=function(){return W(this,void 0,void 0,function(){var t,e,o,i;return K(this,function(n){switch(n.label){case 0:if(this.aborted=!1,this.putExtra.fname||(this.logger.info("use file.name as fname."),this.putExtra.fname=this.file.name),this.file.size>1e4*it)return this.handleError(new w(b.InvalidFile,"file size exceed maximum value 10000G")),[2];if(this.putExtra.customVars&&!Ee(this.putExtra.customVars))return this.handleError(new w(b.InvalidCustomVars,"customVars key should start width x:")),[2];if(this.putExtra.metadata&&!Re(this.putExtra.metadata))return this.handleError(new w(b.InvalidMetadata,"metadata key should start with x-qn-meta-")),[2];n.label=1;case 1:return n.trys.push([1,4,,5]),this.uploadAt=new Date().getTime(),[4,this.checkAndUpdateUploadHost()];case 2:return n.sent(),[4,this.run()];case 3:return t=n.sent(),this.onComplete(t.data),this.checkAndUnfreezeHost(),this.sendLog(t.reqId,200),[2];case 4:return e=n.sent(),this.aborted?(this.logger.warn("upload is aborted."),this.sendLog("",-2),[2]):(this.clear(),this.logger.error(e),e instanceof H&&(this.sendLog(e.reqId,e.code),this.checkAndFreezeHost(e),o=++this.retryCount<=this.config.retryCount,i=rt.includes(e.code),i&&o)?(this.logger.warn("error auto retry: "+this.retryCount+"/"+this.config.retryCount+"."),this.putFile(),[2]):(this.onError(e),[3,5]));case 5:return[2]}})})},r.prototype.clear=function(){this.xhrList.forEach(function(t){t.onreadystatechange=null,t.abort()}),this.xhrList=[],this.logger.info("cleanup uploading xhr.")},r.prototype.stop=function(){this.logger.info("aborted."),this.clear(),this.aborted=!0},r.prototype.addXhr=function(t){this.xhrList.push(t)},r.prototype.sendLog=function(t,e){var o,i;this.logger.report({code:e,reqId:t,remoteIp:"",upType:"jssdk-h5",size:this.file.size,time:Math.floor(this.uploadAt/1e3),port:$e((o=this.uploadHost)===null||o===void 0?void 0:o.getUrl()),host:Ve((i=this.uploadHost)===null||i===void 0?void 0:i.getUrl()),bytesSent:this.progress?this.progress.total.loaded:0,duration:Math.floor((new Date().getTime()-this.uploadAt)/1e3)})},r.prototype.getProgressInfoItem=function(t,e,o){return j({size:e,loaded:t,percent:t/e*100},o==null?{}:{fromCache:o})},r}(),ot=function(){var r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,i){o.__proto__=i}||function(o,i){for(var n in i)i.hasOwnProperty(n)&&(o[n]=i[n])},r(t,e)};return function(t,e){r(t,e);function o(){this.constructor=t}t.prototype=e===null?Object.create(e):(o.prototype=e.prototype,new o)}}(),T=function(){return T=Object.assign||function(r){for(var t,e=1,o=arguments.length;e<o;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=t[i])}return r},T.apply(this,arguments)},R=function(r,t,e,o){function i(n){return n instanceof e?n:new e(function(c){c(n)})}return new(e||(e=Promise))(function(n,c){function p(d){try{a(o.next(d))}catch(v){c(v)}}function g(d){try{a(o.throw(d))}catch(v){c(v)}}function a(d){d.done?n(d.value):i(d.value).then(p,g)}a((o=o.apply(r,t||[])).next())})},E=function(r,t){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},o,i,n,c;return c={next:p(0),throw:p(1),return:p(2)},typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function p(a){return function(d){return g([a,d])}}function g(a){if(o)throw new TypeError("Generator is already executing.");for(;e;)try{if(o=1,i&&(n=a[0]&2?i.return:a[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,a[1])).done)return n;switch(i=0,n&&(a=[a[0]&2,n.value]),a[0]){case 0:case 1:n=a;break;case 4:return e.label++,{value:a[1],done:!1};case 5:e.label++,i=a[1],a=[0];continue;case 7:a=e.ops.pop(),e.trys.pop();continue;default:if(n=e.trys,!(n=n.length>0&&n[n.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!n||a[1]>n[0]&&a[1]<n[3])){e.label=a[1];break}if(a[0]===6&&e.label<n[1]){e.label=n[1],n=a;break}if(n&&e.label<n[2]){e.label=n[2],e.ops.push(a);break}n[2]&&e.ops.pop(),e.trys.pop();continue}a=t.call(r,e)}catch(d){a=[6,d],i=0}finally{o=n=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}};function at(r){var t=/^[1-9]\d*$/;return t.test(String(r))}var st=function(r){ot(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.run=function(){return R(this,void 0,void 0,function(){var e,o,i,n,c,p=this;return E(this,function(g){switch(g.label){case 0:if(this.logger.info("start run Resume."),!this.config.chunkSize||!at(this.config.chunkSize))throw new w(b.InvalidChunkSize,"chunkSize must be a positive integer");if(this.config.chunkSize>1024)throw new w(b.InvalidChunkSize,"chunkSize maximum value is 1024");return[4,this.initBeforeUploadChunks()];case 1:g.sent(),e=new me(function(a){return R(p,void 0,void 0,function(){return E(this,function(d){switch(d.label){case 0:if(this.aborted)throw e.abort(),new Error("pool is aborted");return[4,this.uploadChunk(a)];case 1:return d.sent(),[2]}})})},this.config.concurrentRequestLimit),o=null,i=this.getLocalKey(),n=this.chunks.map(function(a,d){return e.enqueue({chunk:a,index:d})}),g.label=2;case 2:return g.trys.push([2,5,,6]),[4,Promise.all(n)];case 3:return g.sent(),[4,this.mkFileReq()];case 4:return o=g.sent(),[3,6];case 5:throw c=g.sent(),c instanceof H&&(c.code===612||c.code===400)&&N(i,this.logger),c;case 6:return N(i,this.logger),[2,o]}})})},t.prototype.uploadChunk=function(e){return R(this,void 0,void 0,function(){var o,i,n,c,p,g,a,d,v,_=this;return E(this,function(C){switch(C.label){case 0:return o=e.index,i=e.chunk,n=this.cachedUploadedList[o],this.logger.info("upload part "+o+", cache:",n),c=this.config.checkByMD5,p=function(){_.usedCacheList[o]=!0,_.updateChunkProgress(i.size,o),_.uploadedList[o]=n,_.updateLocalCache()},n&&!c?(p(),[2]):[4,Me(i)];case 1:return g=C.sent(),this.logger.info("computed part md5.",g),n&&g===n.md5?(p(),[2]):(this.usedCacheList[o]=!1,a=function(z){_.updateChunkProgress(z.loaded,o)},d={body:i,md5:this.config.checkByServer?g:void 0,onProgress:a,onCreate:function(z){return _.addXhr(z)}},this.logger.info("part "+o+" start uploading."),[4,Ze(this.token,this.key,e.index+1,this.getUploadInfo(),d)]);case 2:return v=C.sent(),this.logger.info("part "+o+" upload completed."),a({loaded:i.size,total:i.size}),this.uploadedList[o]={etag:v.data.etag,md5:v.data.md5,size:i.size},this.updateLocalCache(),[2]}})})},t.prototype.mkFileReq=function(){return R(this,void 0,void 0,function(){var e,o,i=this;return E(this,function(n){switch(n.label){case 0:return e=T(T(T({parts:this.uploadedList.map(function(c,p){return{etag:c.etag,partNumber:p+1}}),fname:this.putExtra.fname},this.putExtra.mimeType&&{mimeType:this.putExtra.mimeType}),this.putExtra.customVars&&{customVars:this.putExtra.customVars}),this.putExtra.metadata&&{metadata:this.putExtra.metadata}),this.logger.info("parts upload completed, make file.",e),[4,Ye(this.token,this.key,this.getUploadInfo(),{onCreate:function(c){return i.addXhr(c)},body:JSON.stringify(e)})];case 1:return o=n.sent(),this.logger.info("finish Resume Progress."),this.updateMkFileProgress(1),[2,o]}})})},t.prototype.initBeforeUploadChunks=function(){return R(this,void 0,void 0,function(){var e,o,i;return E(this,function(n){switch(n.label){case 0:return this.uploadedList=[],this.usedCacheList=[],e=qe(this.getLocalKey(),this.logger),e?[3,2]:(this.logger.info("init upload parts from api."),[4,Ke(this.token,this.bucketName,this.key,this.uploadHost.getUrl())]);case 1:return o=n.sent(),this.logger.info("initd upload parts of id: "+o.data.uploadId+"."),this.uploadId=o.data.uploadId,this.cachedUploadedList=[],[3,3];case 2:i=["resume upload parts from local cache,","total "+e.data.length+" part,","id is "+e.id+"."],this.logger.info(i.join(" ")),this.cachedUploadedList=e.data,this.uploadId=e.id,n.label=3;case 3:return this.chunks=Pe(this.file,this.config.chunkSize),this.loaded={mkFileProgress:0,chunks:this.chunks.map(function(c){return 0})},this.notifyResumeProgress(),[2]}})})},t.prototype.getUploadInfo=function(){return{id:this.uploadId,url:this.uploadHost.getUrl()}},t.prototype.getLocalKey=function(){return He(this.file.name,this.key,this.file.size)},t.prototype.updateLocalCache=function(){Te(this.getLocalKey(),{id:this.uploadId,data:this.uploadedList},this.logger)},t.prototype.updateChunkProgress=function(e,o){this.loaded.chunks[o]=e,this.notifyResumeProgress()},t.prototype.updateMkFileProgress=function(e){this.loaded.mkFileProgress=e,this.notifyResumeProgress()},t.prototype.notifyResumeProgress=function(){var e=this;this.progress={total:this.getProgressInfoItem(Le(this.loaded.chunks)+this.loaded.mkFileProgress,this.file.size+1),chunks:this.chunks.map(function(o,i){var n=e.usedCacheList[i];return e.getProgressInfoItem(e.loaded.chunks[i],o.size,n)}),uploadInfo:{id:this.uploadId,url:this.uploadHost.getUrl()}},this.onData(this.progress)},t}(ce),Z=function(r,t,e,o){function i(n){return n instanceof e?n:new e(function(c){c(n)})}return new(e||(e=Promise))(function(n,c){function p(d){try{a(o.next(d))}catch(v){c(v)}}function g(d){try{a(o.throw(d))}catch(v){c(v)}}function a(d){d.done?n(d.value):i(d.value).then(p,g)}a((o=o.apply(r,t||[])).next())})},Y=function(r,t){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},o,i,n,c;return c={next:p(0),throw:p(1),return:p(2)},typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function p(a){return function(d){return g([a,d])}}function g(a){if(o)throw new TypeError("Generator is already executing.");for(;e;)try{if(o=1,i&&(n=a[0]&2?i.return:a[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,a[1])).done)return n;switch(i=0,n&&(a=[a[0]&2,n.value]),a[0]){case 0:case 1:n=a;break;case 4:return e.label++,{value:a[1],done:!1};case 5:e.label++,i=a[1],a=[0];continue;case 7:a=e.ops.pop(),e.trys.pop();continue;default:if(n=e.trys,!(n=n.length>0&&n[n.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!n||a[1]>n[0]&&a[1]<n[3])){e.label=a[1];break}if(a[0]===6&&e.label<n[1]){e.label=n[1],n=a;break}if(n&&e.label<n[2]){e.label=n[2],e.ops.push(a);break}n[2]&&e.ops.pop(),e.trys.pop();continue}a=t.call(r,e)}catch(d){a=[6,d],i=0}finally{o=n=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},ut=function(){function r(){this.crc=-1,this.table=this.makeTable()}return r.prototype.makeTable=function(){for(var t=new Array,e=0;e<256;e++){for(var o=e,i=0;i<8;i++)o&1?o=o>>>1^3988292384:o>>>=1;t[e]=o}return t},r.prototype.append=function(t){for(var e=this.crc,o=0;o<t.byteLength;o++)e=e>>>8^this.table[(e^t[o])&255];this.crc=e},r.prototype.compute=function(){return(this.crc^-1)>>>0},r.prototype.readAsUint8Array=function(t){return Z(this,void 0,void 0,function(){var e;return Y(this,function(o){switch(o.label){case 0:return typeof t.arrayBuffer!="function"?[3,2]:(e=Uint8Array.bind,[4,t.arrayBuffer()]);case 1:return[2,new(e.apply(Uint8Array,[void 0,o.sent()]))];case 2:return[2,new Promise(function(i,n){var c=new FileReader;c.onload=function(){if(c.result==null){n();return}if(typeof c.result=="string"){n();return}i(new Uint8Array(c.result))},c.readAsArrayBuffer(t)})]}})})},r.prototype.file=function(t){return Z(this,void 0,void 0,function(){var e,o,i,n,c,p;return Y(this,function(g){switch(g.label){case 0:return t.size<=F?(e=this.append,[4,this.readAsUint8Array(t)]):[3,2];case 1:return e.apply(this,[g.sent()]),[2,this.compute()];case 2:o=Math.ceil(t.size/F),i=0,g.label=3;case 3:return i<o?(n=i*F,c=i===o-1?t.size:n+F,[4,this.readAsUint8Array(t.slice(n,c))]):[3,6];case 4:p=g.sent(),this.append(new Uint8Array(p)),g.label=5;case 5:return i++,[3,3];case 6:return[2,this.compute()]}})})},r.file=function(t){var e=new r;return e.file(t)},r}(),ft=function(){var r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,i){o.__proto__=i}||function(o,i){for(var n in i)i.hasOwnProperty(n)&&(o[n]=i[n])},r(t,e)};return function(t,e){r(t,e);function o(){this.constructor=t}t.prototype=e===null?Object.create(e):(o.prototype=e.prototype,new o)}}(),ct=function(r,t,e,o){function i(n){return n instanceof e?n:new e(function(c){c(n)})}return new(e||(e=Promise))(function(n,c){function p(d){try{a(o.next(d))}catch(v){c(v)}}function g(d){try{a(o.throw(d))}catch(v){c(v)}}function a(d){d.done?n(d.value):i(d.value).then(p,g)}a((o=o.apply(r,t||[])).next())})},lt=function(r,t){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},o,i,n,c;return c={next:p(0),throw:p(1),return:p(2)},typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function p(a){return function(d){return g([a,d])}}function g(a){if(o)throw new TypeError("Generator is already executing.");for(;e;)try{if(o=1,i&&(n=a[0]&2?i.return:a[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,a[1])).done)return n;switch(i=0,n&&(a=[a[0]&2,n.value]),a[0]){case 0:case 1:n=a;break;case 4:return e.label++,{value:a[1],done:!1};case 5:e.label++,i=a[1],a=[0];continue;case 7:a=e.ops.pop(),e.trys.pop();continue;default:if(n=e.trys,!(n=n.length>0&&n[n.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!n||a[1]>n[0]&&a[1]<n[3])){e.label=a[1];break}if(a[0]===6&&e.label<n[1]){e.label=n[1],n=a;break}if(n&&e.label<n[2]){e.label=n[2],e.ops.push(a);break}n[2]&&e.ops.pop(),e.trys.pop();continue}a=t.call(r,e)}catch(d){a=[6,d],i=0}finally{o=n=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},Q=function(r){ft(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.run=function(){return ct(this,void 0,void 0,function(){var e,o,i,n,c=this;return lt(this,function(p){switch(p.label){case 0:return this.logger.info("start run Direct."),e=new FormData,e.append("file",this.file),e.append("token",this.token),this.key!=null&&e.append("key",this.key),e.append("fname",this.putExtra.fname),this.config.checkByServer?[4,ut.file(this.file)]:[3,2];case 1:o=p.sent(),e.append("crc32",o.toString()),p.label=2;case 2:return this.putExtra.customVars&&(this.logger.info("init customVars."),i=this.putExtra.customVars,Object.keys(i).forEach(function(g){return e.append(g,i[g].toString())}),this.logger.info("customVars inited.")),this.logger.info("formData inited."),[4,Qe(this.uploadHost.getUrl(),e,{onProgress:function(g){c.updateDirectProgress(g.loaded,g.total)},onCreate:function(g){return c.addXhr(g)}})];case 3:return n=p.sent(),this.logger.info("Direct progress finish."),this.finishDirectProgress(),[2,n]}})})},t.prototype.updateDirectProgress=function(e,o){this.progress={total:this.getProgressInfoItem(e,o+1)},this.onData(this.progress)},t.prototype.finishDirectProgress=function(){if(!this.progress){this.logger.warn("progress is null."),this.progress={total:this.getProgressInfoItem(this.file.size,this.file.size)},this.onData(this.progress);return}var e=this.progress.total;this.progress={total:this.getProgressInfoItem(e.loaded+1,e.size)},this.onData(this.progress)},t}(ce);function le(r,t,e){e===void 0&&(e=3);var o=ae();o.open("POST","https://uplog.qbox.me/log/3"),o.setRequestHeader("Content-type","application/x-www-form-urlencoded"),o.setRequestHeader("Authorization",$(r).Authorization),o.onreadystatechange=function(){o.readyState===4&&o.status!==200&&e>0&&le(r,t,e-1)};var i=[t.code||"",t.reqId||"",t.host||"",t.remoteIp||"",t.port||"",t.duration||"",t.time||"",t.bytesSent||"",t.upType||"",t.size||""].join(",");o.send(i)}var ht=function(r,t){var e=typeof Symbol=="function"&&r[Symbol.iterator];if(!e)return r;var o=e.call(r),i,n=[],c;try{for(;(t===void 0||t-- >0)&&!(i=o.next()).done;)n.push(i.value)}catch(p){c={error:p}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(c)throw c.error}}return n},V=function(){for(var r=[],t=0;t<arguments.length;t++)r=r.concat(ht(arguments[t]));return r},dt=function(){function r(t,e,o,i){e===void 0&&(e=!0),o===void 0&&(o="OFF"),i===void 0&&(i="UPLOAD"),this.token=t,this.disableReport=e,this.level=o,this.prefix=i,this.id=++r.id}return r.prototype.getPrintPrefix=function(t){return"Qiniu-JS-SDK ["+t+"]["+this.prefix+"#"+this.id+"]:"},r.prototype.report=function(t,e){if(!this.disableReport)try{le(this.token,t,e)}catch(o){this.warn(o)}},r.prototype.info=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var o=["INFO"];o.includes(this.level)&&console.log.apply(console,V([this.getPrintPrefix("INFO")],t))},r.prototype.warn=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var o=["INFO","WARN"];o.includes(this.level)&&console.warn.apply(console,V([this.getPrintPrefix("WARN")],t))},r.prototype.error=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var o=["INFO","WARN","ERROR"];o.includes(this.level)&&console.error.apply(console,V([this.getPrintPrefix("ERROR")],t))},r.id=0,r}(),ee=function(r,t,e,o){function i(n){return n instanceof e?n:new e(function(c){c(n)})}return new(e||(e=Promise))(function(n,c){function p(d){try{a(o.next(d))}catch(v){c(v)}}function g(d){try{a(o.throw(d))}catch(v){c(v)}}function a(d){d.done?n(d.value):i(d.value).then(p,g)}a((o=o.apply(r,t||[])).next())})},te=function(r,t){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},o,i,n,c;return c={next:p(0),throw:p(1),return:p(2)},typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function p(a){return function(d){return g([a,d])}}function g(a){if(o)throw new TypeError("Generator is already executing.");for(;e;)try{if(o=1,i&&(n=a[0]&2?i.return:a[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,a[1])).done)return n;switch(i=0,n&&(a=[a[0]&2,n.value]),a[0]){case 0:case 1:n=a;break;case 4:return e.label++,{value:a[1],done:!1};case 5:e.label++,i=a[1],a=[0];continue;case 7:a=e.ops.pop(),e.trys.pop();continue;default:if(n=e.trys,!(n=n.length>0&&n[n.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!n||a[1]>n[0]&&a[1]<n[3])){e.label=a[1];break}if(a[0]===6&&e.label<n[1]){e.label=n[1],n=a;break}if(n&&e.label<n[2]){e.label=n[2],e.ops.push(a);break}n[2]&&e.ops.pop(),e.trys.pop();continue}a=t.call(r,e)}catch(d){a=[6,d],i=0}finally{o=n=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},pt=function(r,t){var e=typeof Symbol=="function"&&r[Symbol.iterator];if(!e)return r;var o=e.call(r),i,n=[],c;try{for(;(t===void 0||t-- >0)&&!(i=o.next()).done;)n.push(i.value)}catch(p){c={error:p}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(c)throw c.error}}return n},yt=function(){for(var r=[],t=0;t<arguments.length;t++)r=r.concat(pt(arguments[t]));return r},B=new Map,gt=function(){function r(t,e){this.host=t,this.protocol=e}return r.prototype.isFrozen=function(){var t=new Date().getTime(),e=B.get(this.host);return e!=null&&e>=t},r.prototype.freeze=function(t){t===void 0&&(t=20);var e=new Date().getTime()+t*1e3;B.set(this.host,e)},r.prototype.unfreeze=function(){B.delete(this.host)},r.prototype.getUrl=function(){return this.protocol+"://"+this.host},r.prototype.getUnfreezeTime=function(){return B.get(this.host)},r}(),vt=function(){function r(t){t===void 0&&(t=[]),this.initHosts=t,this.cachedHostsMap=new Map}return r.prototype.register=function(t,e,o,i){this.cachedHostsMap.set(t+"@"+e,o.map(function(n){return new gt(n,i)}))},r.prototype.refresh=function(t,e,o){var i,n,c,p;return ee(this,void 0,void 0,function(){var g,a,d;return te(this,function(v){switch(v.label){case 0:return g=this.cachedHostsMap.get(t+"@"+e)||[],g.length>0?[2]:this.initHosts.length>0?(this.register(t,e,this.initHosts,o),[2]):[4,We(t,e,o)];case 1:return a=v.sent(),(a==null?void 0:a.data)!=null&&(d=yt(((n=(i=a.data.up)===null||i===void 0?void 0:i.acc)===null||n===void 0?void 0:n.main)||[],((p=(c=a.data.up)===null||c===void 0?void 0:c.acc)===null||p===void 0?void 0:p.backup)||[]),this.register(t,e,d,o)),[2]}})})},r.prototype.getUp=function(t,e,o){return ee(this,void 0,void 0,function(){var i,n,c;return te(this,function(p){switch(p.label){case 0:return[4,this.refresh(t,e,o)];case 1:return p.sent(),i=this.cachedHostsMap.get(t+"@"+e)||[],i.length===0?[2,null]:(n=i.filter(function(g){return!g.isFrozen()}),n.length>0?[2,n[0]]:(c=i.slice().sort(function(g,a){return(g.getUnfreezeTime()||0)-(a.getUnfreezeTime()||0)}),[2,c[0]]))}})})},r}();function mt(r,t,e,o){return r.config&&r.config.forceDirect?(o.info("ues forceDirect mode."),new Q(r,t,e,o)):r.file.size>4*F?(o.info("file size over 4M, use Resume."),new st(r,t,e,o)):(o.info("file size less or equal than 4M, use Direct."),new Q(r,t,e,o))}function bt(r,t,e,o,i){var n=new dt(e,i==null?void 0:i.disableStatisticsReport,i==null?void 0:i.debugLogLevel,r.name),c={file:r,key:t,token:e,putExtra:o,config:xt(i,n)},p=new vt(c.config.uphost);return new xe(function(g){var a=mt(c,{onData:function(d){return g.next(d)},onError:function(d){return g.error(d)},onComplete:function(d){return g.complete(d)}},p,n);return a.putFile(),a.stop.bind(a)})}var O=function(){return O=Object.assign||function(r){for(var t,e=1,o=arguments.length;e<o;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=t[i])}return r},O.apply(this,arguments)},wt=function(r,t){var e={};for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&t.indexOf(o)<0&&(e[o]=r[o]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,o=Object.getOwnPropertySymbols(r);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(r,o[i])&&(e[o[i]]=r[o[i]]);return e},_t=function(r,t){var e=typeof Symbol=="function"&&r[Symbol.iterator];if(!e)return r;var o=e.call(r),i,n=[],c;try{for(;(t===void 0||t-- >0)&&!(i=o.next()).done;)n.push(i.value)}catch(p){c={error:p}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(c)throw c.error}}return n},G=function(){for(var r=[],t=0;t<arguments.length;t++)r=r.concat(_t(arguments[t]));return r};function xt(r,t){var e=O({},r),o=e.upprotocol,i=e.uphost,n=wt(e,["upprotocol","uphost"]),c=O({uphost:[],retryCount:3,checkByMD5:!1,forceDirect:!1,useCdnDomain:!0,checkByServer:!1,concurrentRequestLimit:3,chunkSize:nt,upprotocol:"https",debugLogLevel:"OFF",disableStatisticsReport:!1},n);o&&(c.upprotocol=o.replace(/:$/,""));var p=[];if(t&&(r==null?void 0:r.uphost)!=null&&(r==null?void 0:r.region)!=null&&t.warn("do not use both the uphost and region config."),i)Array.isArray(i)?p.push.apply(p,G(i)):p.push(i);else if(c!=null&&c.region){var g=Ge[c==null?void 0:c.region];c.useCdnDomain?p.push.apply(p,G(g.cdnUphost)):p.push.apply(p,G(g.srcUphost))}return O(O({},c),{uphost:p.filter(Boolean)})}var ne={PNG:"image/png",JPEG:"image/jpeg",WEBP:"image/webp",BMP:"image/bmp"};Object.keys(ne).map(function(r){return ne[r]});async function Ct(r,t,e,o){const i=await o.getToken({fileName:t,key:e,file:r,...o.custom,...o});let n=null;return typeof i=="string"?n={token:i,expires:3600}:n=i,n.expiresTime=new Date().getTime()+n.expires*1e3,n.token}async function St({file:r,fileName:t,onProgress:e,options:o}){const i=await ge(r,t,o),n=await Ct(r,t,i,o);return new Promise((c,p)=>{bt(r,i,n,o.putExtra,o.putConfig).subscribe({next(g){g&&e(g.total)},error(g){p(g)},async complete(g){let a={url:o.domain+"/"+i,key:i};if(o.successHandle){a=await o.successHandle(a),c(a);return}c(a)}})})}async function It(r){const{getConfig:t}=de(),e=t("qiniu"),o=r.options,i=pe(ye(e),o);return r.options=i,await St(r)}export{Ct as getToken,It as upload};
