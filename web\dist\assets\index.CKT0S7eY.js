const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.Bov7pIhy.js","assets/index.GuQX7xXE.js","assets/vue.zNq9Glab.js","assets/index.WGbL7eFL.css","assets/crud.CBfqHRU6.js","assets/dictionary.DBJS--kg.js"])))=>i.map(i=>d[i]);
import{a as p,_ as d}from"./index.GuQX7xXE.js";import{createCrudOptions as i}from"./crud.p58HRu5b.js";import{d as n,a as m,o as l,b as o,g as R,e as g,w as x,f as t,n as C,u as r,P as b}from"./vue.zNq9Glab.js";import"./dictionary.DBJS--kg.js";import"./authFunction.BcROZVTX.js";const k=n({name:"dictionary"}),A=n({...k,setup(y){const s=b(()=>d(()=>import("./index.Bov7pIhy.js"),__vite__mapDeps([0,1,2,3,4,5]))),e=m(),{crudBinding:a,crudRef:c,crudExpose:_}=p({createCrudOptions:i,context:{subDictRef:e}});return l(()=>{_.doRefresh()}),(D,P)=>{const f=o("fs-crud"),u=o("fs-page");return g(),R(u,null,{default:x(()=>[t(f,C({ref_key:"crudRef",ref:c},r(a)),null,16),t(r(s),{ref_key:"subDictRef",ref:e},null,512)]),_:1})}}});export{A as default};
