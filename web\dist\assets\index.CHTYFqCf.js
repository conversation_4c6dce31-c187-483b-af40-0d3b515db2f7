import{a as c}from"./index.GuQX7xXE.js";import{createCrudOptions as f}from"./crud.B130-qbr.js";import{d as o,o as p,b as e,g as u,e as _,w as d,f as m,n as i,u as l}from"./vue.zNq9Glab.js";const g=o({name:"operationLog"}),w=o({...g,setup(x){const{crudBinding:r,crudRef:n,crudExpose:s}=c({createCrudOptions:f});return p(()=>{s.doRefresh()}),(C,h)=>{const t=e("fs-crud"),a=e("fs-page");return _(),u(a,null,{default:d(()=>[m(t,i({ref_key:"crudRef",ref:n},l(r)),null,16)]),_:1})}}});export{w as default};
