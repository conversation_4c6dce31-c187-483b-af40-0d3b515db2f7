import{d as i,r as p,R as g,o as h,b,c as n,e as t,l as e,h as v,t as a,g as w,F as f,j as y}from"./vue.zNq9Glab.js";import{r as k}from"./index.GuQX7xXE.js";import{_ as L}from"./_plugin-vue_export-helper.DlAUqK2U.js";const x={class:"layout-navbars-breadcrumb-user-news"},C={class:"head-box"},B={class:"head-box-title"},M={class:"content-box"},N={class:"content-box-msg"},T=["innerHTML"],G={class:"content-box-time"},$=i({name:"layoutBreadcrumbUserNews"}),D=i({...$,setup(F){const o=p({newsList:[]}),l=g(),d=()=>{l.push("/messageCenter")},_=()=>{k({url:"/api/system/message_center/get_newest_msg/",method:"get",params:{}}).then(s=>{const{data:c}=s;console.log(c),o.newsList=[c]})};return h(()=>{_()}),(s,c)=>{const m=b("el-empty");return t(),n("div",x,[e("div",C,[e("div",B,a(s.$t("message.user.newTitle")),1)]),e("div",M,[o.newsList.length>0?(t(!0),n(f,{key:0},y(o.newsList,(r,u)=>(t(),n("div",{class:"content-box-item",key:u},[e("div",null,a(r.title),1),e("div",N,[e("div",{innerHTML:r.content},null,8,T)]),e("div",G,a(r.create_datetime),1)]))),128)):(t(),w(m,{key:1,description:s.$t("message.user.newDesc")},null,8,["description"]))]),o.newsList.length>0?(t(),n("div",{key:0,class:"foot-box",onClick:d},a(s.$t("message.user.newGo")),1)):v("",!0)])}}}),j=L(D,[["__scopeId","data-v-202f0690"]]);export{j as default};
