{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/_.contribution.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../editor/editor.api.js\";\n\n// src/basic-languages/_.contribution.ts\nvar languageDefinitions = {};\nvar lazyLanguageLoaders = {};\nvar LazyLanguageLoader = class _LazyLanguageLoader {\n  static getOrCreate(languageId) {\n    if (!lazyLanguageLoaders[languageId]) {\n      lazyLanguageLoaders[languageId] = new _LazyLanguageLoader(languageId);\n    }\n    return lazyLanguageLoaders[languageId];\n  }\n  constructor(languageId) {\n    this._languageId = languageId;\n    this._loadingTriggered = false;\n    this._lazyLoadPromise = new Promise((resolve, reject) => {\n      this._lazyLoadPromiseResolve = resolve;\n      this._lazyLoadPromiseReject = reject;\n    });\n  }\n  load() {\n    if (!this._loadingTriggered) {\n      this._loadingTriggered = true;\n      languageDefinitions[this._languageId].loader().then(\n        (mod) => this._lazyLoadPromiseResolve(mod),\n        (err) => this._lazyLoadPromiseReject(err)\n      );\n    }\n    return this._lazyLoadPromise;\n  }\n};\nasync function loadLanguage(languageId) {\n  await LazyLanguageLoader.getOrCreate(languageId).load();\n  const model = monaco_editor_core_exports.editor.createModel(\"\", languageId);\n  model.dispose();\n}\nfunction registerLanguage(def) {\n  const languageId = def.id;\n  languageDefinitions[languageId] = def;\n  monaco_editor_core_exports.languages.register(def);\n  const lazyLanguageLoader = LazyLanguageLoader.getOrCreate(languageId);\n  monaco_editor_core_exports.languages.registerTokensProviderFactory(languageId, {\n    create: async () => {\n      const mod = await lazyLanguageLoader.load();\n      return mod.language;\n    }\n  });\n  monaco_editor_core_exports.languages.onLanguageEncountered(languageId, async () => {\n    const mod = await lazyLanguageLoader.load();\n    monaco_editor_core_exports.languages.setLanguageConfiguration(languageId, mod.conf);\n  });\n}\nexport {\n  loadLanguage,\n  registerLanguage\n};\n"], "mappings": ";;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,sBAAsB,CAAC;AAC3B,IAAI,sBAAsB,CAAC;AAC3B,IAAI,qBAAqB,MAAM,oBAAoB;AAAA,EACjD,OAAO,YAAY,YAAY;AAC7B,QAAI,CAAC,oBAAoB,UAAU,GAAG;AACpC,0BAAoB,UAAU,IAAI,IAAI,oBAAoB,UAAU;AAAA,IACtE;AACA,WAAO,oBAAoB,UAAU;AAAA,EACvC;AAAA,EACA,YAAY,YAAY;AACtB,SAAK,cAAc;AACnB,SAAK,oBAAoB;AACzB,SAAK,mBAAmB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvD,WAAK,0BAA0B;AAC/B,WAAK,yBAAyB;AAAA,IAChC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,oBAAoB;AACzB,0BAAoB,KAAK,WAAW,EAAE,OAAO,EAAE;AAAA,QAC7C,CAAC,QAAQ,KAAK,wBAAwB,GAAG;AAAA,QACzC,CAAC,QAAQ,KAAK,uBAAuB,GAAG;AAAA,MAC1C;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AACF;AAMA,SAAS,iBAAiB,KAAK;AAC7B,QAAM,aAAa,IAAI;AACvB,sBAAoB,UAAU,IAAI;AAClC,6BAA2B,UAAU,SAAS,GAAG;AACjD,QAAM,qBAAqB,mBAAmB,YAAY,UAAU;AACpE,6BAA2B,UAAU,8BAA8B,YAAY;AAAA,IAC7E,QAAQ,YAAY;AAClB,YAAM,MAAM,MAAM,mBAAmB,KAAK;AAC1C,aAAO,IAAI;AAAA,IACb;AAAA,EACF,CAAC;AACD,6BAA2B,UAAU,sBAAsB,YAAY,YAAY;AACjF,UAAM,MAAM,MAAM,mBAAmB,KAAK;AAC1C,+BAA2B,UAAU,yBAAyB,YAAY,IAAI,IAAI;AAAA,EACpF,CAAC;AACH;", "names": []}