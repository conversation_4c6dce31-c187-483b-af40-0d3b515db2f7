import f from"./index.BJyC0LIb.js";import g from"./index.C3kfzwT4.js";import{r as p}from"./index.GuQX7xXE.js";import{d as x,a as w,r as k,b as c,g as y,e as C,w as l,f as s,l as r,u as v}from"./vue.zNq9Glab.js";import{_ as I}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./index.BznPD_Ny.js";import"./api.CAHR2Rwu.js";function D(m){return p({url:"/api/system/role/",method:"get",params:m})}function T(){return p({url:"/api/system/column/get_models/",method:"get"})}const R={class:"columns-box columns-left"},B={class:"columns-box columns-center"},$={class:"columns-box columns-right"},q=x({__name:"index",setup(m){const u=w(null);let o=k({role:"",model:"",app:"",menu:""});const _=async(e,t)=>{const a=await D(e);t(a)},d=async(e,t)=>{const a=await T();a.data.forEach(n=>{n.showText=`${n.app}-${n.title}(${n.key})`}),t(a)},h=()=>{var e;if(o.role&&o.model&&o.app){(e=u.value)==null||e.fetchData(o);return}},i=(e,t)=>{e==="role"&&(o.role=t.id),e==="menu"&&(o.menu=t.id),e==="model"&&(o.model=t.key,o.app=t.app),h()};return(e,t)=>{const a=c("el-col"),n=c("el-row"),b=c("fs-page");return C(),y(b,{class:"columns"},{default:l(()=>[s(n,{class:"columns-el-row",gutter:10},{default:l(()=>[s(a,{span:6},{default:l(()=>[r("div",R,[s(f,{title:"角色",type:"role",showPagination:"",onFetchData:_,onItemClick:i})])]),_:1}),s(a,{span:8},{default:l(()=>[r("div",B,[s(f,{title:"模型表",type:"model",label:"showText",value:"key",onFetchData:d,onItemClick:i})])]),_:1}),s(a,{span:10},{default:l(()=>[r("div",$,[s(g,{ref_key:"columnsTableRef",ref:u,currentInfo:v(o)},null,8,["currentInfo"])])]),_:1})]),_:1})]),_:1})}}}),j=I(q,[["__scopeId","data-v-ebd54b07"]]);export{j as default};
