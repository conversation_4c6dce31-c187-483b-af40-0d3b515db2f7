import{d as g,M as c,p as s,c as r,e as u,l as t,t as d,u as y}from"./vue.zNq9Glab.js";import{u as v,A as C,a9 as h}from"./index.GuQX7xXE.js";import{l as k}from"./logo-mini.DjPeV5ul.js";import{_ as S}from"./_plugin-vue_export-helper.DlAUqK2U.js";const x=["src"],T={style:{"font-size":"x-large"}},b=["src"],z=g({name:"layoutLogo"}),B=g({...z,setup(L){const m=v(),{themeConfig:o}=c(m),f=s(()=>{let{isCollapse:n,layout:i}=o.value;return!n||i==="classic"||document.body.clientWidth<1e3}),l=()=>{if(o.value.layout==="transverse")return!1;o.value.isCollapse=!o.value.isCollapse},_=C(),{systemConfig:p}=c(_),e=s(()=>p.value),a=s(()=>h.isEmpty(e.value["login.site_logo"])?k:e.value["login.site_logo"]);return(n,i)=>f.value?(u(),r("div",{key:0,class:"layout-logo",onClick:l},[t("img",{src:a.value,class:"layout-logo-medium-img"},null,8,x),t("span",T,d(e.value["login.site_title"]||y(o).globalTitle),1)])):(u(),r("div",{key:1,class:"layout-logo-size",onClick:l},[t("img",{src:a.value,class:"layout-logo-size-img"},null,8,b)]))}}),D=S(B,[["__scopeId","data-v-6b8f472f"]]);export{D as default};
