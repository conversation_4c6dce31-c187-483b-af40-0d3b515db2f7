import{r}from"./index.GuQX7xXE.js";import"./vue.zNq9Glab.js";const e="/api/dvadmin_celery/task/";function u(){return r({url:e+"field_permission/",method:"get"})}function i(t){return r({url:e,method:"get",params:t})}function s(t){return r({url:e+t,method:"get"})}function d(t){return t.kwargs&&(t.kwargs=JSON.stringify(t.kwargs)),r({url:e,method:"post",data:t})}function o(t){return t.kwargs&&(t.kwargs=JSON.stringify(t.kwargs)),r({url:e+t.id+"/",method:"put",data:t})}function f(t){return r({url:e+t+"/",method:"delete",data:{id:t}})}function m(t){return r({url:e+t.id+"/update_status/",method:"post",data:t})}function p(t){return r({url:e+t.id+"/run_task/",method:"post",data:t})}export{d as AddObj,f as DelObj,i as GetList,s as GetObj,u as GetPermission,p as RunTask,o as UpdateObj,m as UpdateTask,e as apiPrefix};
