"""
导入植物数据管理命令
"""
import json
import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.plant_encyclopedia.models import PlantEncyclopedia


class Command(BaseCommand):
    help = '从huabaike_spider.py生成的JSON文件导入植物数据'
    
    def add_arguments(self, parser):
        parser.add_argument(
            'json_file',
            type=str,
            help='JSON数据文件路径'
        )
        parser.add_argument(
            '--update',
            action='store_true',
            help='如果植物已存在则更新数据'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='批量处理大小'
        )
    
    def handle(self, *args, **options):
        json_file = options['json_file']
        update_existing = options['update']
        batch_size = options['batch_size']
        
        # 检查文件是否存在
        if not os.path.exists(json_file):
            raise CommandError(f'文件不存在: {json_file}')
        
        self.stdout.write(f'开始导入数据从文件: {json_file}')
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            raise CommandError(f'JSON文件格式错误: {e}')
        except Exception as e:
            raise CommandError(f'读取文件失败: {e}')
        
        if not isinstance(data, list):
            raise CommandError('JSON文件应包含植物数据数组')
        
        self.stdout.write(f'找到 {len(data)} 条植物数据')
        
        success_count = 0
        error_count = 0
        update_count = 0
        
        # 批量处理数据
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            
            with transaction.atomic():
                for item in batch:
                    try:
                        result = self.process_plant_item(item, update_existing)
                        if result == 'created':
                            success_count += 1
                        elif result == 'updated':
                            update_count += 1
                    except Exception as e:
                        error_count += 1
                        self.stdout.write(
                            self.style.ERROR(f'处理植物数据失败: {item.get("name", "未知")} - {e}')
                        )
            
            # 显示进度
            processed = min(i + batch_size, len(data))
            self.stdout.write(f'已处理: {processed}/{len(data)}')
        
        # 输出结果统计
        self.stdout.write(
            self.style.SUCCESS(
                f'导入完成! 新增: {success_count}, 更新: {update_count}, 失败: {error_count}'
            )
        )
    
    def process_plant_item(self, item, update_existing):
        """处理单个植物数据项"""
        name = item.get('name', '').strip()
        if not name:
            raise ValueError('植物名称不能为空')
        
        # 检查是否已存在
        existing = PlantEncyclopedia.objects.filter(name=name).first()
        
        if existing:
            if not update_existing:
                return 'skipped'
            
            # 更新现有记录
            self.update_plant_data(existing, item)
            return 'updated'
        else:
            # 创建新记录
            self.create_plant_data(item)
            return 'created'
    
    def create_plant_data(self, item):
        """创建新的植物数据"""
        plant = PlantEncyclopedia(
            name=item.get('name', '').strip(),
            scientific_name=item.get('scientific_name', '').strip(),
            category=self.map_category(item.get('category', '')),
            family=item.get('family', '').strip(),
            genus=item.get('genus', '').strip(),
            description=item.get('description', ''),
            care_tips=item.get('care_tips', ''),
            growth_habit=item.get('growth_habit', ''),
            flowering_period=item.get('flowering_period', '').strip(),
            images=item.get('images', []),
            source_url=item.get('url', ''),
            source_site='花百科',
            status=1,  # 默认发布状态
            tags=self.extract_tags(item),
            search_keywords=self.generate_keywords(item)
        )
        
        # 设置主图
        if plant.images and len(plant.images) > 0:
            plant.main_image = plant.images[0]
        
        plant.save()
        return plant
    
    def update_plant_data(self, plant, item):
        """更新现有植物数据"""
        plant.scientific_name = item.get('scientific_name', '').strip()
        plant.category = self.map_category(item.get('category', ''))
        plant.family = item.get('family', '').strip()
        plant.genus = item.get('genus', '').strip()
        plant.description = item.get('description', '')
        plant.care_tips = item.get('care_tips', '')
        plant.growth_habit = item.get('growth_habit', '')
        plant.flowering_period = item.get('flowering_period', '').strip()
        plant.images = item.get('images', [])
        plant.source_url = item.get('url', '')
        plant.tags = self.extract_tags(item)
        plant.search_keywords = self.generate_keywords(item)
        
        # 更新主图
        if plant.images and len(plant.images) > 0 and not plant.main_image:
            plant.main_image = plant.images[0]
        
        plant.save()
        return plant
    
    def map_category(self, category):
        """映射分类名称"""
        category_mapping = {
            '草本植物': '草本植物',
            '木本植物': '木本植物',
            '藤本植物': '藤本植物',
            '兰科植物': '兰科植物',
            '水生植物': '水生植物',
            '球根植物': '球根植物',
            '宿根植物': '宿根植物',
        }
        return category_mapping.get(category, '草本植物')  # 默认为草本植物
    
    def extract_tags(self, item):
        """从植物数据中提取标签"""
        tags = []
        
        # 添加分类作为标签
        if item.get('category'):
            tags.append(item['category'])
        
        # 添加科属作为标签
        if item.get('family'):
            tags.append(item['family'])
        if item.get('genus'):
            tags.append(item['genus'])
        
        # 添加花期作为标签
        if item.get('flowering_period'):
            tags.append(item['flowering_period'])
        
        return list(set(tags))  # 去重
    
    def generate_keywords(self, item):
        """生成搜索关键词"""
        keywords = []
        
        # 添加植物名称
        if item.get('name'):
            keywords.append(item['name'])
        
        # 添加学名
        if item.get('scientific_name'):
            keywords.append(item['scientific_name'])
        
        # 添加分类
        if item.get('category'):
            keywords.append(item['category'])
        
        # 添加科属
        if item.get('family'):
            keywords.append(item['family'])
        if item.get('genus'):
            keywords.append(item['genus'])
        
        return ', '.join(keywords)
