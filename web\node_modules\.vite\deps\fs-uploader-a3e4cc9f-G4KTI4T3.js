import {
  Ct,
  Fe
} from "./chunk-CP75YXJV.js";
import "./chunk-7AOKHL6I.js";
import "./chunk-TJTCSOX4.js";
import "./chunk-3XL3ODE2.js";
import "./chunk-YFT6OQ5R.js";
import "./chunk-TGOZU523.js";
import "./chunk-IUY2MIZJ.js";
import "./chunk-LK7GAOJV.js";
import "./chunk-44FYVRJW.js";
import {
  defineComponent
} from "./chunk-WEJJSMSC.js";
import "./chunk-LK32TJAX.js";

// node_modules/@fast-crud/fast-extends/dist/fs-uploader-a3e4cc9f.mjs
var l = defineComponent({
  name: "FsUploader",
  props: {
    type: {}
  },
  setup(e) {
    async function t() {
      const { getDefaultType: o } = Ct(), p = e.type || o();
      return await Fe(p);
    }
    return {
      getUploaderRef: t
    };
  }
});
export {
  l as default
};
//# sourceMappingURL=fs-uploader-a3e4cc9f-G4KTI4T3.js.map
