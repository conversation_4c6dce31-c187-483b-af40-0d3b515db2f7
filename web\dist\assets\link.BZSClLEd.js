import{d as c,Q as u,r as d,m as p,b as m,c as _,e as k,l as o,f,t as y,w}from"./vue.zNq9Glab.js";import{x as L,P as g}from"./index.GuQX7xXE.js";import{_ as v}from"./_plugin-vue_export-helper.DlAUqK2U.js";const x={class:"layout-padding layout-link-container"},C={class:"layout-padding-auto layout-padding-view"},h={class:"layout-link-warp"},q={class:"layout-link-msg"},U=c({name:"layoutLinkView"}),$=c({...U,setup(b){const e=u(),t=d({title:"",isLink:"",query:null}),r=()=>{const{origin:i,pathname:n}=window.location;if(t.isLink.includes("{{token}}")&&(t.isLink=t.isLink.replace("{{token}}",L.cookie.get("token"))),g(t.isLink))window.open(t.isLink);else{let s=function(a){return Object.keys(a).map(l=>encodeURIComponent(l)+"="+encodeURIComponent(a[l])).join("&")};window.open(`${i}${n}#${t.isLink}?${s(t.query)}`)}};return p(()=>e.path,()=>{t.title=e.meta.title,t.isLink=e.meta.isLink,t.query=e.query},{immediate:!0}),(i,n)=>{const s=m("el-button");return k(),_("div",x,[o("div",C,[o("div",h,[n[1]||(n[1]=o("i",{class:"layout-link-icon iconfont icon-xingqiu"},null,-1)),o("div",q,'页面 "'+y(i.$t(t.title))+'" 已在新窗口中打开',1),f(s,{class:"mt30",round:"",size:"default",onClick:r},{default:w(()=>n[0]||(n[0]=[o("i",{class:"iconfont icon-lianjie"},null,-1),o("span",null,"立即前往体验",-1)])),_:1,__:[0]})])])])}}}),P=v($,[["__scopeId","data-v-8d2984d4"]]);export{P as default};
