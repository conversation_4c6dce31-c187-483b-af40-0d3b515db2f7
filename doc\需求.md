# 植物语+

## 小程序端

### 用户登录（微信授权登录）
- 用户点击 "微信登录" 按钮，调用微信登录接口
- 获取用户基本信息（头像、昵称、openid 等）
- 系统自动创建或绑定用户账号
- 登录成功后跳转至小程序首页

### 养护提醒 （包括浇水、换水、施肥三类）

用户可自定义频率（每天、每周、每月）
设置具体的提醒时间（支持多个时间点）
可针对不同植物设置不同的提醒规则
智能推荐：

​	（1）根据植物种类、生长阶段和环境因素，提供科学的浇水建议

​	（2）结合用户所在地区的气候特点，优化浇水时间推荐	
​	（3）考虑季节变化，自动调整浇水频率	
提醒方式：

​	（1）系统通知提醒（微信服务通知）
​	 （2）小程序内消息提醒
​           	  

### 植物拍照识别 [Plant.id](http://plant.id/) ：

1. **拍照识别流程**：

- 用户点击 "拍照识花" 按钮，调用手机摄像头

- 系统自动识别拍摄的植物

- 显示识别结果和相关信息

1. **相册识别功能**：

- 支持从相册选择图片进行识别

- 识别历史记录保存和管理

1. **识别结果展示**：

- 显示植物名称（学名、俗名）

- 展示置信度（识别准确率）

- 提供植物分类信息（科、属、种）

- 显示植物形态特征和生态习性

### 健康评估 [Plant.id](http://plant.id/)

1. **拍照识别流程**：

- 用户点击 "健康评估" 按钮，调用手机摄像头

- 系统自动评估拍摄的植物

- 显示评估结果和相关信息

1. **相册识别功能**：

- 支持从相册选择图片进行健康评估

- 识别历史记录保存和管理

1. **识别结果展示**：

- 显示植物名称（学名、俗名）

- 展示置信度（评估准确率）

### 社区分享

1. **动态分享系统**：

- 用户可发布文字、图片内容

- 支持添加话题标签，方便内容分类和检索

- 显示发布时间、点赞数、评论数等互动数据

1. **互动功能设计**：

- 点赞、评论、转发等基本互动操作

- 支持 @用户、添加话题标签

- 提供内容举报和反馈机制

1. **社区结构设计**：

- 按内容类型分类（养护经验、品种展示、问题求助等）

- 按植物种类分类（多肉植物、观叶植物、开花植物等）

- 热门话题推荐、最新动态展示

1. **用户个人主页**：

- 展示用户发布的所有内容

- 显示用户关注和粉丝数量

- 展示用户的植物管理成就和等级
- 展示用户的植物养护记录

### 植物百科

**百科内容结构**：

- 植物基本信息（学名、别名、分类、原产地等）

- 形态特征（根、茎、叶、花、果的描述）

- 生态习性（光照、温度、水分、土壤要求）

- 繁殖方法（播种、扦插、分株等）

- 栽培管理（施肥、修剪、病虫害防治）

- 园林应用（观赏价值、应用场景）

**多媒体展示**：

- 主图展示（高清植物图片）

- 图片库（多角度、多生长阶段图片）

**内容分类与检索**：

- 按植物名称拼音首字母排序

- 按观赏部位分类（观花、观叶、观果等）

- 按生长环境分类（室内、阳台、庭院等）

- 强大的搜索功能，支持关键词、别名搜索

**用户参与机制**：

- 鼓励用户上传自己拍摄的植物图片，丰富图片库

## 后端管理端

### 植物百科管理

1. **内容录入与编辑**：

- 提供可视化编辑器，方便录入和编辑植物信息

- 支持批量导入和导出功能


### 社区管理

1. **用户管理模块**：

- 用户信息管理（基本信息、账号状态、权限设置）

- 用户行为分析（活跃程度、内容贡献、互动情况）

- 黑名单管理和违规处理

### 用户管理

1. **用户信息管理**：

- 用户基本信息管理（昵称、头像、联系方式等）

- 用户植物管理信息（拥有植物种类、数量等）

- 用户偏好设置（接收通知类型、感兴趣的植物种类等）

