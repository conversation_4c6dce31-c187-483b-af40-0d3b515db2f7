const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/defaults.DsLBNIkC.js","assets/index.GuQX7xXE.js","assets/vue.zNq9Glab.js","assets/index.WGbL7eFL.css","assets/classic.D_gH-GaX.js","assets/transverse.2-Cl789f.js","assets/columns.DICwk3TT.js"])))=>i.map(i=>d[i]);
import{u as _,_ as t,L as s,l as i}from"./index.GuQX7xXE.js";import{d as l,M as d,N as m,O as c,g as f,e as y,s as p,P as a,u as v}from"./vue.zNq9Glab.js";const L=l({name:"layout"}),T=l({...L,setup(E){const u={defaults:a(()=>t(()=>import("./defaults.DsLBNIkC.js"),__vite__mapDeps([0,1,2,3]))),classic:a(()=>t(()=>import("./classic.D_gH-GaX.js"),__vite__mapDeps([4,1,2,3]))),transverse:a(()=>t(()=>import("./transverse.2-Cl789f.js"),__vite__mapDeps([5,1,2,3]))),columns:a(()=>t(()=>import("./columns.DICwk3TT.js"),__vite__mapDeps([6,1,2,3])))},r=_(),{themeConfig:e}=d(r),n=()=>{s.get("oldLayout")||s.set("oldLayout",e.value.layout);const o=document.body.clientWidth;o<1e3?(e.value.isCollapse=!1,i.emit("layoutMobileResize",{layout:"defaults",clientWidth:o})):i.emit("layoutMobileResize",{layout:s.get("oldLayout")?s.get("oldLayout"):e.value.layout,clientWidth:o})};return m(()=>{n(),window.addEventListener("resize",n)}),c(()=>{window.removeEventListener("resize",n)}),(o,R)=>(y(),f(p(u[v(e).layout])))}});export{T as default};
