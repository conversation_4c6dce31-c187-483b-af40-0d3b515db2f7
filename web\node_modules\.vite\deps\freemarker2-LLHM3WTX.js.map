{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/freemarker2/freemarker2.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/freemarker2/freemarker2.ts\nvar EMPTY_ELEMENTS = [\n  \"assign\",\n  \"flush\",\n  \"ftl\",\n  \"return\",\n  \"global\",\n  \"import\",\n  \"include\",\n  \"break\",\n  \"continue\",\n  \"local\",\n  \"nested\",\n  \"nt\",\n  \"setting\",\n  \"stop\",\n  \"t\",\n  \"lt\",\n  \"rt\",\n  \"fallback\"\n];\nvar BLOCK_ELEMENTS = [\n  \"attempt\",\n  \"autoesc\",\n  \"autoEsc\",\n  \"compress\",\n  \"comment\",\n  \"escape\",\n  \"noescape\",\n  \"function\",\n  \"if\",\n  \"list\",\n  \"items\",\n  \"sep\",\n  \"macro\",\n  \"noparse\",\n  \"noParse\",\n  \"noautoesc\",\n  \"noAutoEsc\",\n  \"outputformat\",\n  \"switch\",\n  \"visit\",\n  \"recurse\"\n];\nvar TagSyntaxAngle = {\n  close: \">\",\n  id: \"angle\",\n  open: \"<\"\n};\nvar TagSyntaxBracket = {\n  close: \"\\\\]\",\n  id: \"bracket\",\n  open: \"\\\\[\"\n};\nvar TagSyntaxAuto = {\n  close: \"[>\\\\]]\",\n  id: \"auto\",\n  open: \"[<\\\\[]\"\n};\nvar InterpolationSyntaxDollar = {\n  close: \"\\\\}\",\n  id: \"dollar\",\n  open1: \"\\\\$\",\n  open2: \"\\\\{\"\n};\nvar InterpolationSyntaxBracket = {\n  close: \"\\\\]\",\n  id: \"bracket\",\n  open1: \"\\\\[\",\n  open2: \"=\"\n};\nfunction createLangConfiguration(ts) {\n  return {\n    brackets: [\n      [\"<\", \">\"],\n      [\"[\", \"]\"],\n      [\"(\", \")\"],\n      [\"{\", \"}\"]\n    ],\n    comments: {\n      blockComment: [`${ts.open}--`, `--${ts.close}`]\n    },\n    autoCloseBefore: \"\\n\\r\t }]),.:;=\",\n    autoClosingPairs: [\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: '\"', close: '\"', notIn: [\"string\"] },\n      { open: \"'\", close: \"'\", notIn: [\"string\"] }\n    ],\n    surroundingPairs: [\n      { open: '\"', close: '\"' },\n      { open: \"'\", close: \"'\" },\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: \"<\", close: \">\" }\n    ],\n    folding: {\n      markers: {\n        start: new RegExp(\n          `${ts.open}#(?:${BLOCK_ELEMENTS.join(\"|\")})([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        end: new RegExp(`${ts.open}/#(?:${BLOCK_ELEMENTS.join(\"|\")})[\\\\r\\\\n\\\\t ]*>`)\n      }\n    },\n    onEnterRules: [\n      {\n        beforeText: new RegExp(\n          `${ts.open}#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        afterText: new RegExp(`^${ts.open}/#([a-zA-Z_]+)[\\\\r\\\\n\\\\t ]*${ts.close}$`),\n        action: {\n          indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n        }\n      },\n      {\n        beforeText: new RegExp(\n          `${ts.open}#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n      }\n    ]\n  };\n}\nfunction createLangConfigurationAuto() {\n  return {\n    // Cannot set block comment delimiter in auto mode...\n    // It depends on the content and the cursor position of the file...\n    brackets: [\n      [\"<\", \">\"],\n      [\"[\", \"]\"],\n      [\"(\", \")\"],\n      [\"{\", \"}\"]\n    ],\n    autoCloseBefore: \"\\n\\r\t }]),.:;=\",\n    autoClosingPairs: [\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: '\"', close: '\"', notIn: [\"string\"] },\n      { open: \"'\", close: \"'\", notIn: [\"string\"] }\n    ],\n    surroundingPairs: [\n      { open: '\"', close: '\"' },\n      { open: \"'\", close: \"'\" },\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: \"<\", close: \">\" }\n    ],\n    folding: {\n      markers: {\n        start: new RegExp(`[<\\\\[]#(?:${BLOCK_ELEMENTS.join(\"|\")})([^/>\\\\]]*(?!/)[>\\\\]])[^<\\\\[]*$`),\n        end: new RegExp(`[<\\\\[]/#(?:${BLOCK_ELEMENTS.join(\"|\")})[\\\\r\\\\n\\\\t ]*>`)\n      }\n    },\n    onEnterRules: [\n      {\n        beforeText: new RegExp(\n          `[<\\\\[]#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/>\\\\]]*(?!/)[>\\\\]])[^[<\\\\[]]*$`\n        ),\n        afterText: new RegExp(`^[<\\\\[]/#([a-zA-Z_]+)[\\\\r\\\\n\\\\t ]*[>\\\\]]$`),\n        action: {\n          indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n        }\n      },\n      {\n        beforeText: new RegExp(\n          `[<\\\\[]#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/>\\\\]]*(?!/)[>\\\\]])[^[<\\\\[]]*$`\n        ),\n        action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n      }\n    ]\n  };\n}\nfunction createMonarchLanguage(ts, is) {\n  const id = `_${ts.id}_${is.id}`;\n  const s = (name) => name.replace(/__id__/g, id);\n  const r = (regexp) => {\n    const source = regexp.source.replace(/__id__/g, id);\n    return new RegExp(source, regexp.flags);\n  };\n  return {\n    // Settings\n    unicode: true,\n    includeLF: false,\n    start: s(\"default__id__\"),\n    ignoreCase: false,\n    defaultToken: \"invalid\",\n    tokenPostfix: `.freemarker2`,\n    brackets: [\n      { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n      { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n      { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n      { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n    ],\n    // Dynamic RegExp\n    [s(\"open__id__\")]: new RegExp(ts.open),\n    [s(\"close__id__\")]: new RegExp(ts.close),\n    [s(\"iOpen1__id__\")]: new RegExp(is.open1),\n    [s(\"iOpen2__id__\")]: new RegExp(is.open2),\n    [s(\"iClose__id__\")]: new RegExp(is.close),\n    // <#START_TAG : \"<\" | \"<#\" | \"[#\">\n    // <#END_TAG : \"</\" | \"</#\" | \"[/#\">\n    [s(\"startTag__id__\")]: r(/(@open__id__)(#)/),\n    [s(\"endTag__id__\")]: r(/(@open__id__)(\\/#)/),\n    [s(\"startOrEndTag__id__\")]: r(/(@open__id__)(\\/?#)/),\n    // <#CLOSE_TAG1 : (<BLANK>)* (\">\" | \"]\")>\n    [s(\"closeTag1__id__\")]: r(/((?:@blank)*)(@close__id__)/),\n    // <#CLOSE_TAG2 : (<BLANK>)* (\"/\")? (\">\" | \"]\")>\n    [s(\"closeTag2__id__\")]: r(/((?:@blank)*\\/?)(@close__id__)/),\n    // Static RegExp\n    // <#BLANK : \" \" | \"\\t\" | \"\\n\" | \"\\r\">\n    blank: /[ \\t\\n\\r]/,\n    // <FALSE : \"false\">\n    // <TRUE : \"true\">\n    // <IN : \"in\">\n    // <AS : \"as\">\n    // <USING : \"using\">\n    keywords: [\"false\", \"true\", \"in\", \"as\", \"using\"],\n    // Directive names that cannot have an expression parameters and cannot be self-closing\n    // E.g. <#if id==2> ... </#if>\n    directiveStartCloseTag1: /attempt|recover|sep|auto[eE]sc|no(?:autoe|AutoE)sc|compress|default|no[eE]scape|comment|no[pP]arse/,\n    // Directive names that cannot have an expression parameter and can be self-closing\n    // E.g. <#if> ... <#else>  ... </#if>\n    // E.g. <#if> ... <#else /></#if>\n    directiveStartCloseTag2: /else|break|continue|return|stop|flush|t|lt|rt|nt|nested|recurse|fallback|ftl/,\n    // Directive names that can have an expression parameter and cannot be self-closing\n    // E.g. <#if id==2> ... </#if>\n    directiveStartBlank: /if|else[iI]f|list|for[eE]ach|switch|case|assign|global|local|include|import|function|macro|transform|visit|stop|return|call|setting|output[fF]ormat|nested|recurse|escape|ftl|items/,\n    // Directive names that can have an end tag\n    // E.g. </#if>\n    directiveEndCloseTag1: /if|list|items|sep|recover|attempt|for[eE]ach|local|global|assign|function|macro|output[fF]ormat|auto[eE]sc|no(?:autoe|AutoE)sc|compress|transform|switch|escape|no[eE]scape/,\n    // <#ESCAPED_CHAR :\n    //     \"\\\\\"\n    //     (\n    //         (\"n\" | \"t\" | \"r\" | \"f\" | \"b\" | \"g\" | \"l\" | \"a\" | \"\\\\\" | \"'\" | \"\\\"\" | \"{\" | \"=\")\n    //         |\n    //         (\"x\" [\"0\"-\"9\", \"A\"-\"F\", \"a\"-\"f\"])\n    //     )\n    // >\n    // Note: While the JavaCC tokenizer rule only specifies one hex digit,\n    // FreeMarker actually interprets up to 4 hex digits.\n    escapedChar: /\\\\(?:[ntrfbgla\\\\'\"\\{=]|(?:x[0-9A-Fa-f]{1,4}))/,\n    // <#ASCII_DIGIT: [\"0\" - \"9\"]>\n    asciiDigit: /[0-9]/,\n    // <INTEGER : ([\"0\"-\"9\"])+>\n    integer: /[0-9]+/,\n    // <#NON_ESCAPED_ID_START_CHAR:\n    // [\n    // \t  // This was generated on JDK 1.8.0_20 Win64 with src/main/misc/identifierChars/IdentifierCharGenerator.java\n    //    ...\n    // ]\n    nonEscapedIdStartChar: /[\\$@-Z_a-z\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u1FFF\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183-\\u2184\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3006\\u3031-\\u3035\\u303B-\\u303C\\u3040-\\u318F\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3300-\\u337F\\u3400-\\u4DB5\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66E\\uA67F-\\uA697\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA793\\uA7A0-\\uA7AA\\uA7F8-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8D0-\\uA8D9\\uA8F2-\\uA8F7\\uA8FB\\uA900-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF-\\uA9D9\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A\\uAA80-\\uAAAF\\uAAB1\\uAAB5-\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uABC0-\\uABE2\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40-\\uFB41\\uFB43-\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]/,\n    // <#ESCAPED_ID_CHAR: \"\\\\\" (\"-\" | \".\" | \":\" | \"#\")>\n    escapedIdChar: /\\\\[\\-\\.:#]/,\n    // <#ID_START_CHAR: <NON_ESCAPED_ID_START_CHAR>|<ESCAPED_ID_CHAR>>\n    idStartChar: /(?:@nonEscapedIdStartChar)|(?:@escapedIdChar)/,\n    // <ID: <ID_START_CHAR> (<ID_START_CHAR>|<ASCII_DIGIT>)*>\n    id: /(?:@idStartChar)(?:(?:@idStartChar)|(?:@asciiDigit))*/,\n    // Certain keywords / operators are allowed to index hashes\n    //\n    // Expression DotVariable(Expression exp) :\n    // {\n    // \tToken t;\n    // }\n    // {\n    // \t\t<DOT>\n    // \t\t(\n    // \t\t\tt = <ID> | t = <TIMES> | t = <DOUBLE_STAR>\n    // \t\t\t|\n    // \t\t\t(\n    // \t\t\t\tt = <LESS_THAN>\n    // \t\t\t\t|\n    // \t\t\t\tt = <LESS_THAN_EQUALS>\n    // \t\t\t\t|\n    // \t\t\t\tt = <ESCAPED_GT>\n    // \t\t\t\t|\n    // \t\t\t\tt = <ESCAPED_GTE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <FALSE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <TRUE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <IN>\n    // \t\t\t\t|\n    // \t\t\t\tt = <AS>\n    // \t\t\t\t|\n    // \t\t\t\tt = <USING>\n    // \t\t\t)\n    // \t\t\t{\n    // \t\t\t\tif (!Character.isLetter(t.image.charAt(0))) {\n    // \t\t\t\t\tthrow new ParseException(t.image + \" is not a valid identifier.\", template, t);\n    // \t\t\t\t}\n    // \t\t\t}\n    // \t\t)\n    // \t\t{\n    // \t\t\tnotListLiteral(exp, \"hash\");\n    // \t\t\tnotStringLiteral(exp, \"hash\");\n    // \t\t\tnotBooleanLiteral(exp, \"hash\");\n    // \t\t\tDot dot = new Dot(exp, t.image);\n    // \t\t\tdot.setLocation(template, exp, t);\n    // \t\t\treturn dot;\n    // \t\t}\n    // }\n    specialHashKeys: /\\*\\*|\\*|false|true|in|as|using/,\n    // <DOUBLE_EQUALS : \"==\">\n    // <EQUALS : \"=\">\n    // <NOT_EQUALS : \"!=\">\n    // <PLUS_EQUALS : \"+=\">\n    // <MINUS_EQUALS : \"-=\">\n    // <TIMES_EQUALS : \"*=\">\n    // <DIV_EQUALS : \"/=\">\n    // <MOD_EQUALS : \"%=\">\n    // <PLUS_PLUS : \"++\">\n    // <MINUS_MINUS : \"--\">\n    // <LESS_THAN_EQUALS : \"lte\" | \"\\\\lte\" | \"<=\" | \"&lt;=\">\n    // <LESS_THAN : \"lt\" | \"\\\\lt\" | \"<\" | \"&lt;\">\n    // <ESCAPED_GTE : \"gte\" | \"\\\\gte\" | \"&gt;=\">\n    // <ESCAPED_GT: \"gt\" | \"\\\\gt\" |  \"&gt;\">\n    // <DOUBLE_STAR : \"**\">\n    // <PLUS : \"+\">\n    // <MINUS : \"-\">\n    // <TIMES : \"*\">\n    // <PERCENT : \"%\">\n    // <AND : \"&\" | \"&&\" | \"&amp;&amp;\" | \"\\\\and\" >\n    // <OR : \"|\" | \"||\">\n    // <EXCLAM : \"!\">\n    // <COMMA : \",\">\n    // <SEMICOLON : \";\">\n    // <COLON : \":\">\n    // <ELLIPSIS : \"...\">\n    // <DOT_DOT_ASTERISK : \"..*\" >\n    // <DOT_DOT_LESS : \"..<\" | \"..!\" >\n    // <DOT_DOT : \"..\">\n    // <EXISTS : \"??\">\n    // <BUILT_IN : \"?\">\n    // <LAMBDA_ARROW : \"->\" | \"-&gt;\">\n    namedSymbols: /&lt;=|&gt;=|\\\\lte|\\\\lt|&lt;|\\\\gte|\\\\gt|&gt;|&amp;&amp;|\\\\and|-&gt;|->|==|!=|\\+=|-=|\\*=|\\/=|%=|\\+\\+|--|<=|&&|\\|\\||:|\\.\\.\\.|\\.\\.\\*|\\.\\.<|\\.\\.!|\\?\\?|=|<|\\+|-|\\*|\\/|%|\\||\\.\\.|\\?|!|&|\\.|,|;/,\n    arrows: [\"->\", \"-&gt;\"],\n    delimiters: [\";\", \":\", \",\", \".\"],\n    stringOperators: [\"lte\", \"lt\", \"gte\", \"gt\"],\n    noParseTags: [\"noparse\", \"noParse\", \"comment\"],\n    tokenizer: {\n      // Parser states\n      // Plain text\n      [s(\"default__id__\")]: [\n        { include: s(\"@directive_token__id__\") },\n        { include: s(\"@interpolation_and_text_token__id__\") }\n      ],\n      // A FreeMarker expression inside a directive, e.g. <#if 2<3>\n      [s(\"fmExpression__id__.directive\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      // A FreeMarker expression inside an interpolation, e.g. ${2+3}\n      [s(\"fmExpression__id__.interpolation\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@expression_token__id__\") },\n        { include: s(\"@greater_operators_token__id__\") }\n      ],\n      // In an expression and inside a not-yet closed parenthesis / bracket\n      [s(\"inParen__id__.plain\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      [s(\"inParen__id__.gt\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@expression_token__id__\") },\n        { include: s(\"@greater_operators_token__id__\") }\n      ],\n      // Expression for the unified call, e.g. <@createMacro() ... >\n      [s(\"noSpaceExpression__id__\")]: [\n        { include: s(\"@no_space_expression_end_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      // For the function of a unified call. Special case for when the\n      // expression is a simple identifier.\n      // <@join [1,2] \",\">\n      // <@null!join [1,2] \",\">\n      [s(\"unifiedCall__id__\")]: [{ include: s(\"@unified_call_token__id__\") }],\n      // For singly and doubly quoted string (that may contain interpolations)\n      [s(\"singleString__id__\")]: [{ include: s(\"@string_single_token__id__\") }],\n      [s(\"doubleString__id__\")]: [{ include: s(\"@string_double_token__id__\") }],\n      // For singly and doubly quoted string (that may not contain interpolations)\n      [s(\"rawSingleString__id__\")]: [{ include: s(\"@string_single_raw_token__id__\") }],\n      [s(\"rawDoubleString__id__\")]: [{ include: s(\"@string_double_raw_token__id__\") }],\n      // For a comment in an expression\n      // ${ 1 + <#-- comment --> 2}\n      [s(\"expressionComment__id__\")]: [{ include: s(\"@expression_comment_token__id__\") }],\n      // For <#noparse> ... </#noparse>\n      // For <#noParse> ... </#noParse>\n      // For <#comment> ... </#comment>\n      [s(\"noParse__id__\")]: [{ include: s(\"@no_parse_token__id__\") }],\n      // For <#-- ... -->\n      [s(\"terseComment__id__\")]: [{ include: s(\"@terse_comment_token__id__\") }],\n      // Common rules\n      [s(\"directive_token__id__\")]: [\n        // <ATTEMPT : <START_TAG> \"attempt\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <RECOVER : <START_TAG> \"recover\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SEP : <START_TAG> \"sep\" <CLOSE_TAG1>>\n        // <AUTOESC : <START_TAG> \"auto\" (\"e\"|\"E\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), DEFAULT);\n        // }\n        // <NOAUTOESC : <START_TAG> \"no\" (\"autoe\"|\"AutoE\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        // <COMPRESS : <START_TAG> \"compress\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <DEFAUL : <START_TAG> \"default\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <NOESCAPE : <START_TAG> \"no\" (\"e\" | \"E\") \"scape\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        //\n        // <COMMENT : <START_TAG> \"comment\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, NO_PARSE); noparseTag = \"comment\";\n        // }\n        // <NOPARSE: <START_TAG> \"no\" (\"p\" | \"P\") \"arse\" <CLOSE_TAG1>> {\n        //     int tagNamingConvention = getTagNamingConvention(matchedToken, 2);\n        //     handleTagSyntaxAndSwitch(matchedToken, tagNamingConvention, NO_PARSE);\n        //     noparseTag = tagNamingConvention == Configuration.CAMEL_CASE_NAMING_CONVENTION ? \"noParse\" : \"noparse\";\n        // }\n        [\n          r(/(?:@startTag__id__)(@directiveStartCloseTag1)(?:@closeTag1__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            {\n              cases: {\n                \"@noParseTags\": { token: \"tag\", next: s(\"@noParse__id__.$3\") },\n                \"@default\": { token: \"tag\" }\n              }\n            },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <ELSE : <START_TAG> \"else\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <BREAK : <START_TAG> \"break\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <CONTINUE : <START_TAG> \"continue\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_RETURN : <START_TAG> \"return\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <HALT : <START_TAG> \"stop\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <FLUSH : <START_TAG> \"flush\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <TRIM : <START_TAG> \"t\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <LTRIM : <START_TAG> \"lt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <RTRIM : <START_TAG> \"rt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <NOTRIM : <START_TAG> \"nt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_NESTED : <START_TAG> \"nested\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_RECURSE : <START_TAG> \"recurse\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <FALLBACK : <START_TAG> \"fallback\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <TRIVIAL_FTL_HEADER : (\"<#ftl\" | \"[#ftl\") (\"/\")? (\">\" | \"]\")> { ftlHeader(matchedToken); }\n        [\n          r(/(?:@startTag__id__)(@directiveStartCloseTag2)(?:@closeTag2__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <IF : <START_TAG> \"if\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ELSE_IF : <START_TAG> \"else\" (\"i\" | \"I\") \"f\" <BLANK>> {\n        // \thandleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), FM_EXPRESSION);\n        // }\n        // <LIST : <START_TAG> \"list\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <FOREACH : <START_TAG> \"for\" (\"e\" | \"E\") \"ach\" <BLANK>> {\n        //    handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 3), FM_EXPRESSION);\n        // }\n        // <SWITCH : <START_TAG> \"switch\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <CASE : <START_TAG> \"case\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ASSIGN : <START_TAG> \"assign\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <GLOBALASSIGN : <START_TAG> \"global\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <LOCALASSIGN : <START_TAG> \"local\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <_INCLUDE : <START_TAG> \"include\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <IMPORT : <START_TAG> \"import\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <FUNCTION : <START_TAG> \"function\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <MACRO : <START_TAG> \"macro\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <TRANSFORM : <START_TAG> \"transform\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <VISIT : <START_TAG> \"visit\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <STOP : <START_TAG> \"stop\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <RETURN : <START_TAG> \"return\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <CALL : <START_TAG> \"call\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <SETTING : <START_TAG> \"setting\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <OUTPUTFORMAT : <START_TAG> \"output\" (\"f\"|\"F\") \"ormat\" <BLANK>> {\n        //    handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 6), FM_EXPRESSION);\n        // }\n        // <NESTED : <START_TAG> \"nested\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <RECURSE : <START_TAG> \"recurse\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ESCAPE : <START_TAG> \"escape\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        //\n        // Note: FreeMarker grammar appears to treat the FTL header as a special case,\n        // in order to remove new lines after the header (?), but since we only need\n        // to tokenize for highlighting, we can include this directive here.\n        // <FTL_HEADER : (\"<#ftl\" | \"[#ftl\") <BLANK>> { ftlHeader(matchedToken); }\n        //\n        // Note: FreeMarker grammar appears to treat the items directive as a special case for\n        // the AST parsing process, but since we only need to tokenize, we can include this\n        // directive here.\n        // <ITEMS : <START_TAG> \"items\" (<BLANK>)+ <AS> <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        [\n          r(/(?:@startTag__id__)(@directiveStartBlank)(@blank)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"\", next: s(\"@fmExpression__id__.directive\") }\n          ]\n        ],\n        // <END_IF : <END_TAG> \"if\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_LIST : <END_TAG> \"list\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_SEP : <END_TAG> \"sep\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_RECOVER : <END_TAG> \"recover\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ATTEMPT : <END_TAG> \"attempt\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_FOREACH : <END_TAG> \"for\" (\"e\" | \"E\") \"ach\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 3), DEFAULT);\n        // }\n        // <END_LOCAL : <END_TAG> \"local\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_GLOBAL : <END_TAG> \"global\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ASSIGN : <END_TAG> \"assign\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_FUNCTION : <END_TAG> \"function\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_MACRO : <END_TAG> \"macro\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_OUTPUTFORMAT : <END_TAG> \"output\" (\"f\" | \"F\") \"ormat\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 6), DEFAULT);\n        // }\n        // <END_AUTOESC : <END_TAG> \"auto\" (\"e\" | \"E\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), DEFAULT);\n        // }\n        // <END_NOAUTOESC : <END_TAG> \"no\" (\"autoe\"|\"AutoE\") \"sc\" <CLOSE_TAG1>> {\n        //   handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        // <END_COMPRESS : <END_TAG> \"compress\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_TRANSFORM : <END_TAG> \"transform\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_SWITCH : <END_TAG> \"switch\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ESCAPE : <END_TAG> \"escape\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_NOESCAPE : <END_TAG> \"no\" (\"e\" | \"E\") \"scape\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        [\n          r(/(?:@endTag__id__)(@directiveEndCloseTag1)(?:@closeTag1__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <UNIFIED_CALL : \"<@\" | \"[@\" > { unifiedCall(matchedToken); }\n        [\n          r(/(@open__id__)(@)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\", next: s(\"@unifiedCall__id__\") }\n          ]\n        ],\n        // <UNIFIED_CALL_END : (\"<\" | \"[\") \"/@\" ((<ID>) (\".\"<ID>)*)? <CLOSE_TAG1>> { unifiedCallEnd(matchedToken); }\n        [\n          r(/(@open__id__)(\\/@)((?:(?:@id)(?:\\.(?:@id))*)?)(?:@closeTag1__id__)/),\n          [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <TERSE_COMMENT : (\"<\" | \"[\") \"#--\" > { noparseTag = \"-->\"; handleTagSyntaxAndSwitch(matchedToken, NO_PARSE); }\n        [\n          r(/(@open__id__)#--/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : { token: \"comment\", next: s(\"@terseComment__id__\") }\n        ],\n        // <UNKNOWN_DIRECTIVE : (\"[#\" | \"[/#\" | \"<#\" | \"</#\") ([\"a\"-\"z\", \"A\"-\"Z\", \"_\"])+>\n        [\n          r(/(?:@startOrEndTag__id__)([a-zA-Z_]+)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag.invalid\", next: s(\"@fmExpression__id__.directive\") }\n          ]\n        ]\n      ],\n      // <DEFAULT, NO_DIRECTIVE> TOKEN :\n      [s(\"interpolation_and_text_token__id__\")]: [\n        // <DOLLAR_INTERPOLATION_OPENING : \"${\"> { startInterpolation(matchedToken); }\n        // <SQUARE_BRACKET_INTERPOLATION_OPENING : \"[=\"> { startInterpolation(matchedToken); }\n        [\n          r(/(@iOpen1__id__)(@iOpen2__id__)/),\n          [\n            { token: is.id === \"bracket\" ? \"@brackets.interpolation\" : \"delimiter.interpolation\" },\n            {\n              token: is.id === \"bracket\" ? \"delimiter.interpolation\" : \"@brackets.interpolation\",\n              next: s(\"@fmExpression__id__.interpolation\")\n            }\n          ]\n        ],\n        // <STATIC_TEXT_FALSE_ALARM : \"$\" | \"#\" | \"<\" | \"[\" | \"{\"> // to handle a lone dollar sign or \"<\" or \"# or <@ with whitespace after\"\n        // <STATIC_TEXT_WS : (\"\\n\" | \"\\r\" | \"\\t\" | \" \")+>\n        // <STATIC_TEXT_NON_WS : (~[\"$\", \"<\", \"#\", \"[\", \"{\", \"\\n\", \"\\r\", \"\\t\", \" \"])+>\n        [/[\\$#<\\[\\{]|(?:@blank)+|[^\\$<#\\[\\{\\n\\r\\t ]+/, { token: \"source\" }]\n      ],\n      // <STRING_LITERAL :\n      // \t(\n      // \t\t\"\\\"\"\n      // \t\t((~[\"\\\"\", \"\\\\\"]) | <ESCAPED_CHAR>)*\n      // \t\t\"\\\"\"\n      // \t)\n      // \t|\n      // \t(\n      // \t\t\"'\"\n      // \t\t((~[\"'\", \"\\\\\"]) | <ESCAPED_CHAR>)*\n      // \t\t\"'\"\n      // \t)\n      // >\n      [s(\"string_single_token__id__\")]: [\n        [/[^'\\\\]/, { token: \"string\" }],\n        [/@escapedChar/, { token: \"string.escape\" }],\n        [/'/, { token: \"string\", next: \"@pop\" }]\n      ],\n      [s(\"string_double_token__id__\")]: [\n        [/[^\"\\\\]/, { token: \"string\" }],\n        [/@escapedChar/, { token: \"string.escape\" }],\n        [/\"/, { token: \"string\", next: \"@pop\" }]\n      ],\n      // <RAW_STRING : \"r\" ((\"\\\"\" (~[\"\\\"\"])* \"\\\"\") | (\"'\" (~[\"'\"])* \"'\"))>\n      [s(\"string_single_raw_token__id__\")]: [\n        [/[^']+/, { token: \"string.raw\" }],\n        [/'/, { token: \"string.raw\", next: \"@pop\" }]\n      ],\n      [s(\"string_double_raw_token__id__\")]: [\n        [/[^\"]+/, { token: \"string.raw\" }],\n        [/\"/, { token: \"string.raw\", next: \"@pop\" }]\n      ],\n      // <FM_EXPRESSION, IN_PAREN, NO_SPACE_EXPRESSION, NAMED_PARAMETER_EXPRESSION> TOKEN :\n      [s(\"expression_token__id__\")]: [\n        // Strings\n        [\n          /(r?)(['\"])/,\n          {\n            cases: {\n              \"r'\": [\n                { token: \"keyword\" },\n                { token: \"string.raw\", next: s(\"@rawSingleString__id__\") }\n              ],\n              'r\"': [\n                { token: \"keyword\" },\n                { token: \"string.raw\", next: s(\"@rawDoubleString__id__\") }\n              ],\n              \"'\": [{ token: \"source\" }, { token: \"string\", next: s(\"@singleString__id__\") }],\n              '\"': [{ token: \"source\" }, { token: \"string\", next: s(\"@doubleString__id__\") }]\n            }\n          }\n        ],\n        // Numbers\n        // <INTEGER : ([\"0\"-\"9\"])+>\n        // <DECIMAL : <INTEGER> \".\" <INTEGER>>\n        [\n          /(?:@integer)(?:\\.(?:@integer))?/,\n          {\n            cases: {\n              \"(?:@integer)\": { token: \"number\" },\n              \"@default\": { token: \"number.float\" }\n            }\n          }\n        ],\n        // Special hash keys that must not be treated as identifiers\n        // after a period, e.g. a.** is accessing the key \"**\" of a\n        [\n          /(\\.)(@blank*)(@specialHashKeys)/,\n          [{ token: \"delimiter\" }, { token: \"\" }, { token: \"identifier\" }]\n        ],\n        // Symbols / operators\n        [\n          /(?:@namedSymbols)/,\n          {\n            cases: {\n              \"@arrows\": { token: \"meta.arrow\" },\n              \"@delimiters\": { token: \"delimiter\" },\n              \"@default\": { token: \"operators\" }\n            }\n          }\n        ],\n        // Identifiers\n        [\n          /@id/,\n          {\n            cases: {\n              \"@keywords\": { token: \"keyword.$0\" },\n              \"@stringOperators\": { token: \"operators\" },\n              \"@default\": { token: \"identifier\" }\n            }\n          }\n        ],\n        // <OPEN_BRACKET : \"[\">\n        // <CLOSE_BRACKET : \"]\">\n        // <OPEN_PAREN : \"(\">\n        // <CLOSE_PAREN : \")\">\n        // <OPENING_CURLY_BRACKET : \"{\">\n        // <CLOSING_CURLY_BRACKET : \"}\">\n        [\n          /[\\[\\]\\(\\)\\{\\}]/,\n          {\n            cases: {\n              \"\\\\[\": {\n                cases: {\n                  \"$S2==gt\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n                  \"@default\": { token: \"@brackets\", next: s(\"@inParen__id__.plain\") }\n                }\n              },\n              \"\\\\]\": {\n                cases: {\n                  ...is.id === \"bracket\" ? {\n                    \"$S2==interpolation\": { token: \"@brackets.interpolation\", next: \"@popall\" }\n                  } : {},\n                  // This cannot happen while in auto mode, since this applies only to an\n                  // fmExpression inside a directive. But once we encounter the start of a\n                  // directive, we can establish the tag syntax mode.\n                  ...ts.id === \"bracket\" ? {\n                    \"$S2==directive\": { token: \"@brackets.directive\", next: \"@popall\" }\n                  } : {},\n                  // Ignore mismatched paren\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              },\n              \"\\\\(\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n              \"\\\\)\": {\n                cases: {\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              },\n              \"\\\\{\": {\n                cases: {\n                  \"$S2==gt\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n                  \"@default\": { token: \"@brackets\", next: s(\"@inParen__id__.plain\") }\n                }\n              },\n              \"\\\\}\": {\n                cases: {\n                  ...is.id === \"bracket\" ? {} : {\n                    \"$S2==interpolation\": { token: \"@brackets.interpolation\", next: \"@popall\" }\n                  },\n                  // Ignore mismatched paren\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              }\n            }\n          }\n        ],\n        // <OPEN_MISPLACED_INTERPOLATION : \"${\" | \"#{\" | \"[=\">\n        [/\\$\\{/, { token: \"delimiter.invalid\" }]\n      ],\n      // <FM_EXPRESSION, IN_PAREN, NAMED_PARAMETER_EXPRESSION> SKIP :\n      [s(\"blank_and_expression_comment_token__id__\")]: [\n        // < ( \" \" | \"\\t\" | \"\\n\" | \"\\r\" )+ >\n        [/(?:@blank)+/, { token: \"\" }],\n        // < (\"<\" | \"[\") (\"#\" | \"!\") \"--\"> : EXPRESSION_COMMENT\n        [/[<\\[][#!]--/, { token: \"comment\", next: s(\"@expressionComment__id__\") }]\n      ],\n      // <FM_EXPRESSION, NO_SPACE_EXPRESSION, NAMED_PARAMETER_EXPRESSION> TOKEN :\n      [s(\"directive_end_token__id__\")]: [\n        // <DIRECTIVE_END : \">\">\n        // {\n        //     if (inFTLHeader) {\n        //         eatNewline();\n        //         inFTLHeader = false;\n        //     }\n        //     if (squBracTagSyntax || postInterpolationLexState != -1 /* We are in an interpolation */) {\n        //         matchedToken.kind = NATURAL_GT;\n        //     } else {\n        //         SwitchTo(DEFAULT);\n        //     }\n        // }\n        // This cannot happen while in auto mode, since this applies only to an\n        // fmExpression inside a directive. But once we encounter the start of a\n        // directive, we can establish the tag syntax mode.\n        [\n          />/,\n          ts.id === \"bracket\" ? { token: \"operators\" } : { token: \"@brackets.directive\", next: \"@popall\" }\n        ],\n        // <EMPTY_DIRECTIVE_END : \"/>\" | \"/]\">\n        // It is a syntax error to end a tag with the wrong close token\n        // Let's indicate that to the user by not closing the tag\n        [\n          r(/(\\/)(@close__id__)/),\n          [{ token: \"delimiter.directive\" }, { token: \"@brackets.directive\", next: \"@popall\" }]\n        ]\n      ],\n      // <IN_PAREN> TOKEN :\n      [s(\"greater_operators_token__id__\")]: [\n        // <NATURAL_GT : \">\">\n        [/>/, { token: \"operators\" }],\n        // <NATURAL_GTE : \">=\">\n        [/>=/, { token: \"operators\" }]\n      ],\n      // <NO_SPACE_EXPRESSION> TOKEN :\n      [s(\"no_space_expression_end_token__id__\")]: [\n        // <TERMINATING_WHITESPACE :  ([\"\\n\", \"\\r\", \"\\t\", \" \"])+> : FM_EXPRESSION\n        [/(?:@blank)+/, { token: \"\", switchTo: s(\"@fmExpression__id__.directive\") }]\n      ],\n      [s(\"unified_call_token__id__\")]: [\n        // Special case for a call where the expression is just an ID\n        // <UNIFIED_CALL> <ID> <BLANK>+\n        [\n          /(@id)((?:@blank)+)/,\n          [{ token: \"tag\" }, { token: \"\", next: s(\"@fmExpression__id__.directive\") }]\n        ],\n        [\n          r(/(@id)(\\/?)(@close__id__)/),\n          [\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\", next: \"@popall\" }\n          ]\n        ],\n        [/./, { token: \"@rematch\", next: s(\"@noSpaceExpression__id__\") }]\n      ],\n      // <NO_PARSE> TOKEN :\n      [s(\"no_parse_token__id__\")]: [\n        // <MAYBE_END :\n        // \t (\"<\" | \"[\")\n        // \t \"/\"\n        // \t (\"#\")?\n        // \t ([\"a\"-\"z\", \"A\"-\"Z\"])+\n        // \t ( \" \" | \"\\t\" | \"\\n\" | \"\\r\" )*\n        // \t (\">\" | \"]\")\n        // >\n        [\n          r(/(@open__id__)(\\/#?)([a-zA-Z]+)((?:@blank)*)(@close__id__)/),\n          {\n            cases: {\n              \"$S2==$3\": [\n                { token: \"@brackets.directive\" },\n                { token: \"delimiter.directive\" },\n                { token: \"tag\" },\n                { token: \"\" },\n                { token: \"@brackets.directive\", next: \"@popall\" }\n              ],\n              \"$S2==comment\": [\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" }\n              ],\n              \"@default\": [\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" }\n              ]\n            }\n          }\n        ],\n        // <KEEP_GOING : (~[\"<\", \"[\", \"-\"])+>\n        // <LONE_LESS_THAN_OR_DASH : [\"<\", \"[\", \"-\"]>\n        [\n          /[^<\\[\\-]+|[<\\[\\-]/,\n          {\n            cases: {\n              \"$S2==comment\": { token: \"comment\" },\n              \"@default\": { token: \"source\" }\n            }\n          }\n        ]\n      ],\n      // <EXPRESSION_COMMENT> SKIP:\n      [s(\"expression_comment_token__id__\")]: [\n        // < \"-->\" | \"--]\">\n        [\n          /--[>\\]]/,\n          {\n            token: \"comment\",\n            next: \"@pop\"\n          }\n        ],\n        // < (~[\"-\", \">\", \"]\"])+ >\n        // < \">\">\n        // < \"]\">\n        // < \"-\">\n        [/[^\\->\\]]+|[>\\]\\-]/, { token: \"comment\" }]\n      ],\n      [s(\"terse_comment_token__id__\")]: [\n        //  <TERSE_COMMENT_END : \"-->\" | \"--]\">\n        [r(/--(?:@close__id__)/), { token: \"comment\", next: \"@popall\" }],\n        // <KEEP_GOING : (~[\"<\", \"[\", \"-\"])+>\n        // <LONE_LESS_THAN_OR_DASH : [\"<\", \"[\", \"-\"]>\n        [/[^<\\[\\-]+|[<\\[\\-]/, { token: \"comment\" }]\n      ]\n    }\n  };\n}\nfunction createMonarchLanguageAuto(is) {\n  const angle = createMonarchLanguage(TagSyntaxAngle, is);\n  const bracket = createMonarchLanguage(TagSyntaxBracket, is);\n  const auto = createMonarchLanguage(TagSyntaxAuto, is);\n  return {\n    // Angle and bracket syntax mode\n    // We switch to one of these once we have determined the mode\n    ...angle,\n    ...bracket,\n    ...auto,\n    // Settings\n    unicode: true,\n    includeLF: false,\n    start: `default_auto_${is.id}`,\n    ignoreCase: false,\n    defaultToken: \"invalid\",\n    tokenPostfix: `.freemarker2`,\n    brackets: [\n      { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n      { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n      { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n      { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n    ],\n    tokenizer: {\n      ...angle.tokenizer,\n      ...bracket.tokenizer,\n      ...auto.tokenizer\n    }\n  };\n}\nvar TagAngleInterpolationDollar = {\n  conf: createLangConfiguration(TagSyntaxAngle),\n  language: createMonarchLanguage(TagSyntaxAngle, InterpolationSyntaxDollar)\n};\nvar TagBracketInterpolationDollar = {\n  conf: createLangConfiguration(TagSyntaxBracket),\n  language: createMonarchLanguage(TagSyntaxBracket, InterpolationSyntaxDollar)\n};\nvar TagAngleInterpolationBracket = {\n  conf: createLangConfiguration(TagSyntaxAngle),\n  language: createMonarchLanguage(TagSyntaxAngle, InterpolationSyntaxBracket)\n};\nvar TagBracketInterpolationBracket = {\n  conf: createLangConfiguration(TagSyntaxBracket),\n  language: createMonarchLanguage(TagSyntaxBracket, InterpolationSyntaxBracket)\n};\nvar TagAutoInterpolationDollar = {\n  conf: createLangConfigurationAuto(),\n  language: createMonarchLanguageAuto(InterpolationSyntaxDollar)\n};\nvar TagAutoInterpolationBracket = {\n  conf: createLangConfigurationAuto(),\n  language: createMonarchLanguageAuto(InterpolationSyntaxBracket)\n};\nexport {\n  TagAngleInterpolationBracket,\n  TagAngleInterpolationDollar,\n  TagAutoInterpolationBracket,\n  TagAutoInterpolationDollar,\n  TagBracketInterpolationBracket,\n  TagBracketInterpolationDollar\n};\n"], "mappings": ";;;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,iBAAiB;AAAA,EACnB,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM;AACR;AACA,IAAI,gBAAgB;AAAA,EAClB,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM;AACR;AACA,IAAI,4BAA4B;AAAA,EAC9B,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAI,6BAA6B;AAAA,EAC/B,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,OAAO;AACT;AACA,SAAS,wBAAwB,IAAI;AACnC,SAAO;AAAA,IACL,UAAU;AAAA,MACR,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,cAAc,CAAC,GAAG,GAAG,IAAI,MAAM,KAAK,GAAG,KAAK,EAAE;AAAA,IAChD;AAAA,IACA,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,MAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,MAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC7C;AAAA,IACA,kBAAkB;AAAA,MAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IAC1B;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,QACP,OAAO,IAAI;AAAA,UACT,GAAG,GAAG,IAAI,OAAO,eAAe,KAAK,GAAG,CAAC,QAAQ,GAAG,KAAK,UAAU,GAAG,KAAK,MAAM,GAAG,IAAI;AAAA,QAC1F;AAAA,QACA,KAAK,IAAI,OAAO,GAAG,GAAG,IAAI,QAAQ,eAAe,KAAK,GAAG,CAAC,iBAAiB;AAAA,MAC7E;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,YAAY,IAAI;AAAA,UACd,GAAG,GAAG,IAAI,UAAU,eAAe,KAAK,GAAG,CAAC,qBAAqB,GAAG,KAAK,UAAU,GAAG,KAAK,MAAM,GAAG,IAAI;AAAA,QAC1G;AAAA,QACA,WAAW,IAAI,OAAO,IAAI,GAAG,IAAI,8BAA8B,GAAG,KAAK,GAAG;AAAA,QAC1E,QAAQ;AAAA,UACN,cAAc,2BAA2B,UAAU,aAAa;AAAA,QAClE;AAAA,MACF;AAAA,MACA;AAAA,QACE,YAAY,IAAI;AAAA,UACd,GAAG,GAAG,IAAI,UAAU,eAAe,KAAK,GAAG,CAAC,qBAAqB,GAAG,KAAK,UAAU,GAAG,KAAK,MAAM,GAAG,IAAI;AAAA,QAC1G;AAAA,QACA,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,MACnF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,8BAA8B;AACrC,SAAO;AAAA;AAAA;AAAA,IAGL,UAAU;AAAA,MACR,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,KAAK,GAAG;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,MAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,MAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC7C;AAAA,IACA,kBAAkB;AAAA,MAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IAC1B;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,QACP,OAAO,IAAI,OAAO,aAAa,eAAe,KAAK,GAAG,CAAC,kCAAkC;AAAA,QACzF,KAAK,IAAI,OAAO,cAAc,eAAe,KAAK,GAAG,CAAC,iBAAiB;AAAA,MACzE;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,YAAY,IAAI;AAAA,UACd,gBAAgB,eAAe,KAAK,GAAG,CAAC;AAAA,QAC1C;AAAA,QACA,WAAW,IAAI,OAAO,2CAA2C;AAAA,QACjE,QAAQ;AAAA,UACN,cAAc,2BAA2B,UAAU,aAAa;AAAA,QAClE;AAAA,MACF;AAAA,MACA;AAAA,QACE,YAAY,IAAI;AAAA,UACd,gBAAgB,eAAe,KAAK,GAAG,CAAC;AAAA,QAC1C;AAAA,QACA,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,MACnF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,sBAAsB,IAAI,IAAI;AACrC,QAAM,KAAK,IAAI,GAAG,EAAE,IAAI,GAAG,EAAE;AAC7B,QAAM,IAAI,CAAC,SAAS,KAAK,QAAQ,WAAW,EAAE;AAC9C,QAAM,IAAI,CAAC,WAAW;AACpB,UAAM,SAAS,OAAO,OAAO,QAAQ,WAAW,EAAE;AAClD,WAAO,IAAI,OAAO,QAAQ,OAAO,KAAK;AAAA,EACxC;AACA,SAAO;AAAA;AAAA,IAEL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,OAAO,EAAE,eAAe;AAAA,IACxB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,cAAc;AAAA,IACd,UAAU;AAAA,MACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,MAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,MACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,MACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IACpD;AAAA;AAAA,IAEA,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,OAAO,GAAG,IAAI;AAAA,IACrC,CAAC,EAAE,aAAa,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK;AAAA,IACvC,CAAC,EAAE,cAAc,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK;AAAA,IACxC,CAAC,EAAE,cAAc,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK;AAAA,IACxC,CAAC,EAAE,cAAc,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK;AAAA;AAAA;AAAA,IAGxC,CAAC,EAAE,gBAAgB,CAAC,GAAG,EAAE,kBAAkB;AAAA,IAC3C,CAAC,EAAE,cAAc,CAAC,GAAG,EAAE,oBAAoB;AAAA,IAC3C,CAAC,EAAE,qBAAqB,CAAC,GAAG,EAAE,qBAAqB;AAAA;AAAA,IAEnD,CAAC,EAAE,iBAAiB,CAAC,GAAG,EAAE,6BAA6B;AAAA;AAAA,IAEvD,CAAC,EAAE,iBAAiB,CAAC,GAAG,EAAE,gCAAgC;AAAA;AAAA;AAAA,IAG1D,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMP,UAAU,CAAC,SAAS,QAAQ,MAAM,MAAM,OAAO;AAAA;AAAA;AAAA,IAG/C,yBAAyB;AAAA;AAAA;AAAA;AAAA,IAIzB,yBAAyB;AAAA;AAAA;AAAA,IAGzB,qBAAqB;AAAA;AAAA;AAAA,IAGrB,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWvB,aAAa;AAAA;AAAA,IAEb,YAAY;AAAA;AAAA,IAEZ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMT,uBAAuB;AAAA;AAAA,IAEvB,eAAe;AAAA;AAAA,IAEf,aAAa;AAAA;AAAA,IAEb,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA8CJ,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAiCjB,cAAc;AAAA,IACd,QAAQ,CAAC,MAAM,OAAO;AAAA,IACtB,YAAY,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,IAC/B,iBAAiB,CAAC,OAAO,MAAM,OAAO,IAAI;AAAA,IAC1C,aAAa,CAAC,WAAW,WAAW,SAAS;AAAA,IAC7C,WAAW;AAAA;AAAA;AAAA,MAGT,CAAC,EAAE,eAAe,CAAC,GAAG;AAAA,QACpB,EAAE,SAAS,EAAE,wBAAwB,EAAE;AAAA,QACvC,EAAE,SAAS,EAAE,qCAAqC,EAAE;AAAA,MACtD;AAAA;AAAA,MAEA,CAAC,EAAE,8BAA8B,CAAC,GAAG;AAAA,QACnC,EAAE,SAAS,EAAE,2CAA2C,EAAE;AAAA,QAC1D,EAAE,SAAS,EAAE,4BAA4B,EAAE;AAAA,QAC3C,EAAE,SAAS,EAAE,yBAAyB,EAAE;AAAA,MAC1C;AAAA;AAAA,MAEA,CAAC,EAAE,kCAAkC,CAAC,GAAG;AAAA,QACvC,EAAE,SAAS,EAAE,2CAA2C,EAAE;AAAA,QAC1D,EAAE,SAAS,EAAE,yBAAyB,EAAE;AAAA,QACxC,EAAE,SAAS,EAAE,gCAAgC,EAAE;AAAA,MACjD;AAAA;AAAA,MAEA,CAAC,EAAE,qBAAqB,CAAC,GAAG;AAAA,QAC1B,EAAE,SAAS,EAAE,2CAA2C,EAAE;AAAA,QAC1D,EAAE,SAAS,EAAE,4BAA4B,EAAE;AAAA,QAC3C,EAAE,SAAS,EAAE,yBAAyB,EAAE;AAAA,MAC1C;AAAA,MACA,CAAC,EAAE,kBAAkB,CAAC,GAAG;AAAA,QACvB,EAAE,SAAS,EAAE,2CAA2C,EAAE;AAAA,QAC1D,EAAE,SAAS,EAAE,yBAAyB,EAAE;AAAA,QACxC,EAAE,SAAS,EAAE,gCAAgC,EAAE;AAAA,MACjD;AAAA;AAAA,MAEA,CAAC,EAAE,yBAAyB,CAAC,GAAG;AAAA,QAC9B,EAAE,SAAS,EAAE,sCAAsC,EAAE;AAAA,QACrD,EAAE,SAAS,EAAE,4BAA4B,EAAE;AAAA,QAC3C,EAAE,SAAS,EAAE,yBAAyB,EAAE;AAAA,MAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,CAAC,EAAE,mBAAmB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,2BAA2B,EAAE,CAAC;AAAA;AAAA,MAEtE,CAAC,EAAE,oBAAoB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,4BAA4B,EAAE,CAAC;AAAA,MACxE,CAAC,EAAE,oBAAoB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,4BAA4B,EAAE,CAAC;AAAA;AAAA,MAExE,CAAC,EAAE,uBAAuB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,gCAAgC,EAAE,CAAC;AAAA,MAC/E,CAAC,EAAE,uBAAuB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,gCAAgC,EAAE,CAAC;AAAA;AAAA;AAAA,MAG/E,CAAC,EAAE,yBAAyB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,iCAAiC,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA,MAIlF,CAAC,EAAE,eAAe,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,uBAAuB,EAAE,CAAC;AAAA;AAAA,MAE9D,CAAC,EAAE,oBAAoB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,4BAA4B,EAAE,CAAC;AAAA;AAAA,MAExE,CAAC,EAAE,uBAAuB,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAwB5B;AAAA,UACE,EAAE,mEAAmE;AAAA,UACrE,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B;AAAA,cACE,OAAO;AAAA,gBACL,gBAAgB,EAAE,OAAO,OAAO,MAAM,EAAE,mBAAmB,EAAE;AAAA,gBAC7D,YAAY,EAAE,OAAO,MAAM;AAAA,cAC7B;AAAA,YACF;AAAA,YACA,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,UACjC;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAeA;AAAA,UACE,EAAE,mEAAmE;AAAA,UACrE,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,MAAM;AAAA,YACf,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,UACjC;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAwCA;AAAA,UACE,EAAE,mDAAmD;AAAA,UACrD,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,MAAM;AAAA,YACf,EAAE,OAAO,IAAI,MAAM,EAAE,+BAA+B,EAAE;AAAA,UACxD;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QA8BA;AAAA,UACE,EAAE,+DAA+D;AAAA,UACjE,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,MAAM;AAAA,YACf,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,UACjC;AAAA,QACF;AAAA;AAAA,QAEA;AAAA,UACE,EAAE,kBAAkB;AAAA,UACpB,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,uBAAuB,MAAM,EAAE,oBAAoB,EAAE;AAAA,UAChE;AAAA,QACF;AAAA;AAAA,QAEA;AAAA,UACE,EAAE,oEAAoE;AAAA,UACtE;AAAA,YACE,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,MAAM;AAAA,YACf,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,UACjC;AAAA,QACF;AAAA;AAAA,QAEA;AAAA,UACE,EAAE,kBAAkB;AAAA,UACpB,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI,EAAE,OAAO,WAAW,MAAM,EAAE,qBAAqB,EAAE;AAAA,QACzD;AAAA;AAAA,QAEA;AAAA,UACE,EAAE,sCAAsC;AAAA,UACxC,GAAG,OAAO,SAAS;AAAA,YACjB,OAAO;AAAA,cACL,SAAS,EAAE,OAAO,YAAY,UAAU,kBAAkB,GAAG,EAAE,GAAG;AAAA,cAClE,SAAS,EAAE,OAAO,YAAY,UAAU,oBAAoB,GAAG,EAAE,GAAG;AAAA,YACtE;AAAA,UACF,IAAI;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,eAAe,MAAM,EAAE,+BAA+B,EAAE;AAAA,UACnE;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,EAAE,oCAAoC,CAAC,GAAG;AAAA;AAAA;AAAA,QAGzC;AAAA,UACE,EAAE,gCAAgC;AAAA,UAClC;AAAA,YACE,EAAE,OAAO,GAAG,OAAO,YAAY,4BAA4B,0BAA0B;AAAA,YACrF;AAAA,cACE,OAAO,GAAG,OAAO,YAAY,4BAA4B;AAAA,cACzD,MAAM,EAAE,mCAAmC;AAAA,YAC7C;AAAA,UACF;AAAA,QACF;AAAA;AAAA;AAAA;AAAA,QAIA,CAAC,8CAA8C,EAAE,OAAO,SAAS,CAAC;AAAA,MACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcA,CAAC,EAAE,2BAA2B,CAAC,GAAG;AAAA,QAChC,CAAC,UAAU,EAAE,OAAO,SAAS,CAAC;AAAA,QAC9B,CAAC,gBAAgB,EAAE,OAAO,gBAAgB,CAAC;AAAA,QAC3C,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MACzC;AAAA,MACA,CAAC,EAAE,2BAA2B,CAAC,GAAG;AAAA,QAChC,CAAC,UAAU,EAAE,OAAO,SAAS,CAAC;AAAA,QAC9B,CAAC,gBAAgB,EAAE,OAAO,gBAAgB,CAAC;AAAA,QAC3C,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MACzC;AAAA;AAAA,MAEA,CAAC,EAAE,+BAA+B,CAAC,GAAG;AAAA,QACpC,CAAC,SAAS,EAAE,OAAO,aAAa,CAAC;AAAA,QACjC,CAAC,KAAK,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,MAC7C;AAAA,MACA,CAAC,EAAE,+BAA+B,CAAC,GAAG;AAAA,QACpC,CAAC,SAAS,EAAE,OAAO,aAAa,CAAC;AAAA,QACjC,CAAC,KAAK,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,MAC7C;AAAA;AAAA,MAEA,CAAC,EAAE,wBAAwB,CAAC,GAAG;AAAA;AAAA,QAE7B;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,MAAM;AAAA,gBACJ,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,cAAc,MAAM,EAAE,wBAAwB,EAAE;AAAA,cAC3D;AAAA,cACA,MAAM;AAAA,gBACJ,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,cAAc,MAAM,EAAE,wBAAwB,EAAE;AAAA,cAC3D;AAAA,cACA,KAAK,CAAC,EAAE,OAAO,SAAS,GAAG,EAAE,OAAO,UAAU,MAAM,EAAE,qBAAqB,EAAE,CAAC;AAAA,cAC9E,KAAK,CAAC,EAAE,OAAO,SAAS,GAAG,EAAE,OAAO,UAAU,MAAM,EAAE,qBAAqB,EAAE,CAAC;AAAA,YAChF;AAAA,UACF;AAAA,QACF;AAAA;AAAA;AAAA;AAAA,QAIA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,gBAAgB,EAAE,OAAO,SAAS;AAAA,cAClC,YAAY,EAAE,OAAO,eAAe;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AAAA;AAAA;AAAA,QAGA;AAAA,UACE;AAAA,UACA,CAAC,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,aAAa,CAAC;AAAA,QACjE;AAAA;AAAA,QAEA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,WAAW,EAAE,OAAO,aAAa;AAAA,cACjC,eAAe,EAAE,OAAO,YAAY;AAAA,cACpC,YAAY,EAAE,OAAO,YAAY;AAAA,YACnC;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,aAAa,EAAE,OAAO,aAAa;AAAA,cACnC,oBAAoB,EAAE,OAAO,YAAY;AAAA,cACzC,YAAY,EAAE,OAAO,aAAa;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,OAAO;AAAA,gBACL,OAAO;AAAA,kBACL,WAAW,EAAE,OAAO,aAAa,MAAM,EAAE,mBAAmB,EAAE;AAAA,kBAC9D,YAAY,EAAE,OAAO,aAAa,MAAM,EAAE,sBAAsB,EAAE;AAAA,gBACpE;AAAA,cACF;AAAA,cACA,OAAO;AAAA,gBACL,OAAO;AAAA,kBACL,GAAG,GAAG,OAAO,YAAY;AAAA,oBACvB,sBAAsB,EAAE,OAAO,2BAA2B,MAAM,UAAU;AAAA,kBAC5E,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIL,GAAG,GAAG,OAAO,YAAY;AAAA,oBACvB,kBAAkB,EAAE,OAAO,uBAAuB,MAAM,UAAU;AAAA,kBACpE,IAAI,CAAC;AAAA;AAAA,kBAEL,CAAC,EAAE,oBAAoB,CAAC,GAAG,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,kBAC9D,YAAY,EAAE,OAAO,YAAY;AAAA,gBACnC;AAAA,cACF;AAAA,cACA,OAAO,EAAE,OAAO,aAAa,MAAM,EAAE,mBAAmB,EAAE;AAAA,cAC1D,OAAO;AAAA,gBACL,OAAO;AAAA,kBACL,CAAC,EAAE,oBAAoB,CAAC,GAAG,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,kBAC9D,YAAY,EAAE,OAAO,YAAY;AAAA,gBACnC;AAAA,cACF;AAAA,cACA,OAAO;AAAA,gBACL,OAAO;AAAA,kBACL,WAAW,EAAE,OAAO,aAAa,MAAM,EAAE,mBAAmB,EAAE;AAAA,kBAC9D,YAAY,EAAE,OAAO,aAAa,MAAM,EAAE,sBAAsB,EAAE;AAAA,gBACpE;AAAA,cACF;AAAA,cACA,OAAO;AAAA,gBACL,OAAO;AAAA,kBACL,GAAG,GAAG,OAAO,YAAY,CAAC,IAAI;AAAA,oBAC5B,sBAAsB,EAAE,OAAO,2BAA2B,MAAM,UAAU;AAAA,kBAC5E;AAAA;AAAA,kBAEA,CAAC,EAAE,oBAAoB,CAAC,GAAG,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,kBAC9D,YAAY,EAAE,OAAO,YAAY;AAAA,gBACnC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA,CAAC,QAAQ,EAAE,OAAO,oBAAoB,CAAC;AAAA,MACzC;AAAA;AAAA,MAEA,CAAC,EAAE,0CAA0C,CAAC,GAAG;AAAA;AAAA,QAE/C,CAAC,eAAe,EAAE,OAAO,GAAG,CAAC;AAAA;AAAA,QAE7B,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,EAAE,0BAA0B,EAAE,CAAC;AAAA,MAC3E;AAAA;AAAA,MAEA,CAAC,EAAE,2BAA2B,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAgBhC;AAAA,UACE;AAAA,UACA,GAAG,OAAO,YAAY,EAAE,OAAO,YAAY,IAAI,EAAE,OAAO,uBAAuB,MAAM,UAAU;AAAA,QACjG;AAAA;AAAA;AAAA;AAAA,QAIA;AAAA,UACE,EAAE,oBAAoB;AAAA,UACtB,CAAC,EAAE,OAAO,sBAAsB,GAAG,EAAE,OAAO,uBAAuB,MAAM,UAAU,CAAC;AAAA,QACtF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,EAAE,+BAA+B,CAAC,GAAG;AAAA;AAAA,QAEpC,CAAC,KAAK,EAAE,OAAO,YAAY,CAAC;AAAA;AAAA,QAE5B,CAAC,MAAM,EAAE,OAAO,YAAY,CAAC;AAAA,MAC/B;AAAA;AAAA,MAEA,CAAC,EAAE,qCAAqC,CAAC,GAAG;AAAA;AAAA,QAE1C,CAAC,eAAe,EAAE,OAAO,IAAI,UAAU,EAAE,+BAA+B,EAAE,CAAC;AAAA,MAC7E;AAAA,MACA,CAAC,EAAE,0BAA0B,CAAC,GAAG;AAAA;AAAA;AAAA,QAG/B;AAAA,UACE;AAAA,UACA,CAAC,EAAE,OAAO,MAAM,GAAG,EAAE,OAAO,IAAI,MAAM,EAAE,+BAA+B,EAAE,CAAC;AAAA,QAC5E;AAAA,QACA;AAAA,UACE,EAAE,0BAA0B;AAAA,UAC5B;AAAA,YACE,EAAE,OAAO,MAAM;AAAA,YACf,EAAE,OAAO,sBAAsB;AAAA,YAC/B,EAAE,OAAO,uBAAuB,MAAM,UAAU;AAAA,UAClD;AAAA,QACF;AAAA,QACA,CAAC,KAAK,EAAE,OAAO,YAAY,MAAM,EAAE,0BAA0B,EAAE,CAAC;AAAA,MAClE;AAAA;AAAA,MAEA,CAAC,EAAE,sBAAsB,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAS3B;AAAA,UACE,EAAE,2DAA2D;AAAA,UAC7D;AAAA,YACE,OAAO;AAAA,cACL,WAAW;AAAA,gBACT,EAAE,OAAO,sBAAsB;AAAA,gBAC/B,EAAE,OAAO,sBAAsB;AAAA,gBAC/B,EAAE,OAAO,MAAM;AAAA,gBACf,EAAE,OAAO,GAAG;AAAA,gBACZ,EAAE,OAAO,uBAAuB,MAAM,UAAU;AAAA,cAClD;AAAA,cACA,gBAAgB;AAAA,gBACd,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,UAAU;AAAA,gBACnB,EAAE,OAAO,UAAU;AAAA,cACrB;AAAA,cACA,YAAY;AAAA,gBACV,EAAE,OAAO,SAAS;AAAA,gBAClB,EAAE,OAAO,SAAS;AAAA,gBAClB,EAAE,OAAO,SAAS;AAAA,gBAClB,EAAE,OAAO,SAAS;AAAA,gBAClB,EAAE,OAAO,SAAS;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA;AAAA,QAGA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,gBAAgB,EAAE,OAAO,UAAU;AAAA,cACnC,YAAY,EAAE,OAAO,SAAS;AAAA,YAChC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,EAAE,gCAAgC,CAAC,GAAG;AAAA;AAAA,QAErC;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,UACR;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,CAAC,qBAAqB,EAAE,OAAO,UAAU,CAAC;AAAA,MAC5C;AAAA,MACA,CAAC,EAAE,2BAA2B,CAAC,GAAG;AAAA;AAAA,QAEhC,CAAC,EAAE,oBAAoB,GAAG,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA;AAAA;AAAA,QAG/D,CAAC,qBAAqB,EAAE,OAAO,UAAU,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,0BAA0B,IAAI;AACrC,QAAM,QAAQ,sBAAsB,gBAAgB,EAAE;AACtD,QAAM,UAAU,sBAAsB,kBAAkB,EAAE;AAC1D,QAAM,OAAO,sBAAsB,eAAe,EAAE;AACpD,SAAO;AAAA;AAAA;AAAA,IAGL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA;AAAA,IAEH,SAAS;AAAA,IACT,WAAW;AAAA,IACX,OAAO,gBAAgB,GAAG,EAAE;AAAA,IAC5B,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,cAAc;AAAA,IACd,UAAU;AAAA,MACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,MAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,MACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,MACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IACpD;AAAA,IACA,WAAW;AAAA,MACT,GAAG,MAAM;AAAA,MACT,GAAG,QAAQ;AAAA,MACX,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AACF;AACA,IAAI,8BAA8B;AAAA,EAChC,MAAM,wBAAwB,cAAc;AAAA,EAC5C,UAAU,sBAAsB,gBAAgB,yBAAyB;AAC3E;AACA,IAAI,gCAAgC;AAAA,EAClC,MAAM,wBAAwB,gBAAgB;AAAA,EAC9C,UAAU,sBAAsB,kBAAkB,yBAAyB;AAC7E;AACA,IAAI,+BAA+B;AAAA,EACjC,MAAM,wBAAwB,cAAc;AAAA,EAC5C,UAAU,sBAAsB,gBAAgB,0BAA0B;AAC5E;AACA,IAAI,iCAAiC;AAAA,EACnC,MAAM,wBAAwB,gBAAgB;AAAA,EAC9C,UAAU,sBAAsB,kBAAkB,0BAA0B;AAC9E;AACA,IAAI,6BAA6B;AAAA,EAC/B,MAAM,4BAA4B;AAAA,EAClC,UAAU,0BAA0B,yBAAyB;AAC/D;AACA,IAAI,8BAA8B;AAAA,EAChC,MAAM,4BAA4B;AAAA,EAClC,UAAU,0BAA0B,0BAA0B;AAChE;", "names": []}