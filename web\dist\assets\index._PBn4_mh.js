import{X as M,E as V,y as N}from"./index.GuQX7xXE.js";import U from"./index.DBa8QZAz.js";import{_ as E}from"./index.vue_vue_type_script_setup_true_lang.onPCusOB.js";import G from"./index.DtYAmyPJ.js";import X from"./index.BhoHfB2k.js";import{G as j,D as z}from"./api.D3e6WFkQ.js";import{d as T,a as r,o as A,b as s,g as y,e as C,w as n,f as o,l as _,u,$ as I,h as K}from"./vue.zNq9Glab.js";import{_ as L}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./index.es.DmevZXPX.js";import"./index.vue_vue_type_script_setup_true_name_svgIcon_lang.BRW_FJF_.js";import"./crud.DeLz-9l3.js";import"./authFunction.BcROZVTX.js";import"./crud.S2_AIPea.js";import"./api.CAHR2Rwu.js";const O={class:"menu-box menu-left-box"},P={style:{height:"72vh"}},$={style:{height:"72vh"}},q=T({name:"menuPages"}),H=T({...q,setup(J){let m=r([]),p=r([]),l=r(!1),i=r({}),h=r(null),D=r(null),b=r(null);const f=()=>{j({}).then(a=>{const t=a.data,e=M.toArrayTree(t,{parentKey:"parent",children:"children",strict:!0});m.value=e})},x=a=>{var t,e;(t=D.value)==null||t.handleRefreshTable(a),(e=b.value)==null||e.handleRefreshTable(a)},g=(a,t)=>{var e,c;if(a==="update"&&t){const d=((c=(e=h.value)==null?void 0:e.treeRef)==null?void 0:c.currentNode.parent.data)||{};p.value=[d],i.value=t}l.value=!0},v=a=>{a==="submit"&&f(),l.value=!1,i.value={}},R=(a,t)=>{V.confirm("您确认删除该菜单项吗?","温馨提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{const e=await z(a);t(),(e==null?void 0:e.code)===2e3&&(N(e.msg),f())})};return A(()=>{f()}),(a,t)=>{const e=s("el-col"),c=s("el-tab-pane"),d=s("el-tabs"),k=s("el-row"),B=s("el-drawer"),F=s("fs-page");return C(),y(F,null,{default:n(()=>[o(k,{class:"menu-el-row"},{default:n(()=>[o(e,{span:6},{default:n(()=>[_("div",O,[o(U,{ref_key:"menuTreeRef",ref:h,treeData:u(m),onTreeClick:x,onUpdateDept:g,onDeleteDept:R},null,8,["treeData"])])]),_:1}),o(e,{span:18},{default:n(()=>[o(d,{type:"border-card"},{default:n(()=>[o(c,{label:"按钮权限配置"},{default:n(()=>[_("div",P,[o(E,{ref_key:"menuButtonRef",ref:D},null,512)])]),_:1}),o(c,{label:"列权限配置"},{default:n(()=>[_("div",$,[o(X,{ref_key:"menuFieldRef",ref:b},null,512)])]),_:1})]),_:1})]),_:1})]),_:1}),o(B,{modelValue:u(l),"onUpdate:modelValue":t[0]||(t[0]=w=>I(l)?l.value=w:l=w),title:"菜单配置",direction:"rtl",size:"500px","close-on-click-modal":!1,"before-close":v},{default:n(()=>[u(l)?(C(),y(G,{key:0,initFormData:u(i),cacheData:u(p),treeData:u(m),onDrawerClose:v},null,8,["initFormData","cacheData","treeData"])):K("",!0)]),_:1},8,["modelValue"])]),_:1})}}}),ce=L(H,[["__scopeId","data-v-9655b33c"]]);export{ce as default};
