import{B as o}from"./vue.zNq9Glab.js";import{r as t,X as n}from"./index.GuQX7xXE.js";const s={drawerVisible:!1,roleId:void 0,roleName:void 0,users:[]},i=o("RoleDrawerStores",{state:()=>({...s}),actions:{handleDrawerOpen(e){this.drawerVisible=!0,this.set_state(e)},set_state(e){this.roleName=e.name,this.roleId=e.id,this.users=e.users},handleDrawerClose(){Object.assign(this.$state,s)}}});function l(e){return t({url:"/api/system/role_menu_button_permission/get_role_menu/",method:"get",params:e}).then(r=>n.toArrayTree(r.data,{key:"id",parentKey:"parent",children:"children",strict:!1}))}function m(e){return t({url:"/api/system/role_menu_button_permission/set_role_menu/",method:"put",data:e})}function _(e){return t({url:"/api/system/role_menu_button_permission/get_role_menu_btn_field/",method:"get",params:e})}function p(e){return t({url:"/api/system/role_menu_button_permission/set_role_menu_btn/",method:"put",data:e})}function d(e,r){return t({url:`/api/system/role_menu_button_permission/${e}/set_role_menu_field/`,method:"put",data:r})}function h(e){return t({url:"/api/system/role_menu_button_permission/set_role_menu_btn_data_range/",method:"put",data:e})}function c(e){return t({url:"/api/system/role_menu_button_permission/role_to_dept_all/",method:"get",params:e})}function f(){return t({url:"/api/system/user/",method:"get",params:{limit:999}}).then(e=>n.map(e.data,r=>({id:r.id,name:r.name})))}function b(e,r){return t({url:`/api/system/role/${e}/set_role_users/`,method:"put",data:r})}export{i as R,l as a,_ as b,b as c,c as d,h as e,p as f,f as g,d as h,m as s};
