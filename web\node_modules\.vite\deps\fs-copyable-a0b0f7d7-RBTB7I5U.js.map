{"version": 3, "sources": ["../../@fast-crud/fast-extends/src/copyable/components/fs-copyable.vue", "../../@fast-crud/fast-extends/src/copyable/components/fs-copyable.vue"], "sourcesContent": ["<template>\n  <div class=\"fs-copyable\" :class=\"{ 'show-on-hover': copyButton.showOnHover, inline: inline }\">\n    <span v-clipboard=\"modelValue\" v-clipboard:success=\"onSuccess\" v-clipboard:error=\"onError\" class=\"pointer text\">\n      <template v-if=\"$slots.default\">\n        <slot></slot>\n      </template>\n      <template v-else>\n        {{ modelValue }}\n      </template>\n    </span>\n\n    <div v-if=\"modelValue != null && copyButton.show !== false\" class=\"copy-button\">\n      <component\n        :is=\"tagName\"\n        v-clipboard=\"modelValue\"\n        v-clipboard:success=\"onSuccess\"\n        v-clipboard:error=\"onError\"\n        class=\"pointer text\"\n        v-bind=\"copyButton\"\n      >\n        {{ copyButton.text ?? \"复制\" }}\n      </component>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, PropType, ref, Ref } from \"vue\";\nimport { useUi } from \"@fast-crud/fast-crud\";\nimport { merge } from \"lodash-es\";\n\ninterface CopyButton {\n  show: boolean;\n  size: string;\n  showOnHover: boolean;\n  text: string;\n  [key: string]: any;\n}\n\n/**\n * fs-copyable\n * 可以点击复制文本\n * 通过默认插槽可以自定义文本主体\n */\nexport default defineComponent({\n  name: \"FsCopyable\",\n  props: {\n    modelValue: {\n      type: [String, Number, Boolean],\n      default: undefined\n    },\n    /**\n     * 复制按钮\n     * show: 是否显示，默认true\n     * text: 按钮文字\n     * ...其他tag组件参数\n     */\n    button: {\n      type: Object as PropType<CopyButton>,\n      default() {\n        return {};\n      }\n    },\n    /**\n     * 成功信息\n     */\n    successMessage: {\n      type: [Boolean, String],\n      default: true\n    },\n    /**\n     * 错误时的信息\n     */\n    errorMessage: {\n      type: [Boolean, String],\n      default: true\n    },\n\n    inline: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: [\n    \"update:modelValue\",\n    /**\n     * 成功事件\n     */\n    \"success\",\n    /**\n     * 失败事件\n     */\n    \"error\"\n  ],\n  setup(props: any, { emit, slots }) {\n    const textInSlot = computed(() => {\n      return slots.default != null;\n    });\n    const { ui } = useUi();\n    const tagName: Ref<string> = ref(ui.tag.name);\n\n    const copyButton = computed(() => {\n      const defaultButton = {\n        text: \"复制\",\n        size: \"small\",\n        [ui.tag.type]: \"success\",\n        show: true,\n        showOnHover: false\n      };\n      return merge({}, defaultButton, props.button);\n    });\n    function onSuccess() {\n      emit(\"success\");\n      if (props.successMessage) {\n        ui.message.success(props.successMessage === true ? \"复制成功\" : props.successMessage);\n      }\n    }\n    function onError() {\n      emit(\"error\");\n      if (props.errorMessage) {\n        ui.message.error(props.errorMessage === true ? \"复制失败\" : props.errorMessage);\n      }\n    }\n    return {\n      textInSlot,\n      tagName,\n      copyButton,\n      onSuccess,\n      onError\n    };\n  }\n});\n</script>\n<style lang=\"less\">\n.fs-copyable {\n  position: relative;\n  display: flex;\n  align-items: center;\n  &.inline {\n    display: inline-flex;\n  }\n  .pointer {\n    cursor: pointer;\n  }\n  .text {\n    white-space: nowrap; /* 确保文本在一行内显示 */\n    overflow: hidden; /* 隐藏溢出的内容 */\n    text-overflow: ellipsis; /* 使用省略号表示文本溢出 */\n    flex: 1;\n  }\n  .copy-button {\n    //position: absolute;\n    //right: 0;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    margin-left: 10px;\n  }\n  &.show-on-hover {\n    .copy-button {\n      display: none;\n    }\n    &:hover {\n      .copy-button {\n        display: block;\n      }\n    }\n  }\n}\n</style>\n", "<template>\n  <div class=\"fs-copyable\" :class=\"{ 'show-on-hover': copyButton.showOnHover, inline: inline }\">\n    <span v-clipboard=\"modelValue\" v-clipboard:success=\"onSuccess\" v-clipboard:error=\"onError\" class=\"pointer text\">\n      <template v-if=\"$slots.default\">\n        <slot></slot>\n      </template>\n      <template v-else>\n        {{ modelValue }}\n      </template>\n    </span>\n\n    <div v-if=\"modelValue != null && copyButton.show !== false\" class=\"copy-button\">\n      <component\n        :is=\"tagName\"\n        v-clipboard=\"modelValue\"\n        v-clipboard:success=\"onSuccess\"\n        v-clipboard:error=\"onError\"\n        class=\"pointer text\"\n        v-bind=\"copyButton\"\n      >\n        {{ copyButton.text ?? \"复制\" }}\n      </component>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, PropType, ref, Ref } from \"vue\";\nimport { useUi } from \"@fast-crud/fast-crud\";\nimport { merge } from \"lodash-es\";\n\ninterface CopyButton {\n  show: boolean;\n  size: string;\n  showOnHover: boolean;\n  text: string;\n  [key: string]: any;\n}\n\n/**\n * fs-copyable\n * 可以点击复制文本\n * 通过默认插槽可以自定义文本主体\n */\nexport default defineComponent({\n  name: \"FsCopyable\",\n  props: {\n    modelValue: {\n      type: [String, Number, Boolean],\n      default: undefined\n    },\n    /**\n     * 复制按钮\n     * show: 是否显示，默认true\n     * text: 按钮文字\n     * ...其他tag组件参数\n     */\n    button: {\n      type: Object as PropType<CopyButton>,\n      default() {\n        return {};\n      }\n    },\n    /**\n     * 成功信息\n     */\n    successMessage: {\n      type: [Boolean, String],\n      default: true\n    },\n    /**\n     * 错误时的信息\n     */\n    errorMessage: {\n      type: [Boolean, String],\n      default: true\n    },\n\n    inline: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: [\n    \"update:modelValue\",\n    /**\n     * 成功事件\n     */\n    \"success\",\n    /**\n     * 失败事件\n     */\n    \"error\"\n  ],\n  setup(props: any, { emit, slots }) {\n    const textInSlot = computed(() => {\n      return slots.default != null;\n    });\n    const { ui } = useUi();\n    const tagName: Ref<string> = ref(ui.tag.name);\n\n    const copyButton = computed(() => {\n      const defaultButton = {\n        text: \"复制\",\n        size: \"small\",\n        [ui.tag.type]: \"success\",\n        show: true,\n        showOnHover: false\n      };\n      return merge({}, defaultButton, props.button);\n    });\n    function onSuccess() {\n      emit(\"success\");\n      if (props.successMessage) {\n        ui.message.success(props.successMessage === true ? \"复制成功\" : props.successMessage);\n      }\n    }\n    function onError() {\n      emit(\"error\");\n      if (props.errorMessage) {\n        ui.message.error(props.errorMessage === true ? \"复制失败\" : props.errorMessage);\n      }\n    }\n    return {\n      textInSlot,\n      tagName,\n      copyButton,\n      onSuccess,\n      onError\n    };\n  }\n});\n</script>\n<style lang=\"less\">\n.fs-copyable {\n  position: relative;\n  display: flex;\n  align-items: center;\n  &.inline {\n    display: inline-flex;\n  }\n  .pointer {\n    cursor: pointer;\n  }\n  .text {\n    white-space: nowrap; /* 确保文本在一行内显示 */\n    overflow: hidden; /* 隐藏溢出的内容 */\n    text-overflow: ellipsis; /* 使用省略号表示文本溢出 */\n    flex: 1;\n  }\n  .copy-button {\n    //position: absolute;\n    //right: 0;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    margin-left: 10px;\n  }\n  &.show-on-hover {\n    .copy-button {\n      display: none;\n    }\n    &:hover {\n      .copy-button {\n        display: block;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,IAAAA,IAAeC,gBAAgB;EAC7B,MAAM;EACN,OAAO;IACL,YAAY;MACV,MAAM,CAAC,QAAQ,QAAQ,OAAO;MAC9B,SAAS;IACX;;;;;;;IAOA,QAAQ;MACN,MAAM;MACN,UAAU;AACR,eAAO,CAAA;MACT;IACF;;;;IAIA,gBAAgB;MACd,MAAM,CAAC,SAAS,MAAM;MACtB,SAAS;IACX;;;;IAIA,cAAc;MACZ,MAAM,CAAC,SAAS,MAAM;MACtB,SAAS;IACX;IAEA,QAAQ;MACN,MAAM;MACN,SAAS;IACX;EACF;EACA,OAAO;IACL;;;;IAIA;;;;IAIA;EACF;EACA,MAAMC,GAAY,EAAE,MAAAC,GAAM,OAAAC,EAAAA,GAAS;AAC3B,UAAAC,IAAaC,SAAS,MACnBF,EAAM,WAAW,IACzB,GACK,EAAE,IAAAG,EAAAA,IAAOC,EAAAA,GACTC,IAAuBC,IAAIH,EAAG,IAAI,IAAI,GAEtCI,IAAaL,SAAS,MAAM;AAChC,YAAMM,IAAgB;QACpB,MAAM;QACN,MAAM;QACN,CAACL,EAAG,IAAI,IAAI,GAAG;QACf,MAAM;QACN,aAAa;MAAA;AAEf,aAAOM,cAAM,CAAI,GAAAD,GAAeV,EAAM,MAAM;IAAA,CAC7C;AACD,aAASY,IAAY;AACnBX,QAAK,SAAS,GACVD,EAAM,kBACRK,EAAG,QAAQ,QAAQL,EAAM,mBAAmB,OAAO,SAASA,EAAM,cAAc;IAEpF;AACA,aAASa,IAAU;AACjBZ,QAAK,OAAO,GACRD,EAAM,gBACRK,EAAG,QAAQ,MAAML,EAAM,iBAAiB,OAAO,SAASA,EAAM,YAAY;IAE9E;AACO,WAAA;MACL,YAAAG;MACA,SAAAI;MACA,YAAAE;MACA,WAAAG;MACA,SAAAC;IAAA;EAEJ;AACF,CAAC;ACnID,IAAAC,IAAA,EAAA,OAAA,eAAA;AAAA,IAWgEC,IAAM;EAAA,KAAA;EAAA,OAAA;;;YAVpEC,iBAsBM,WAAA;;IArBJ,OAAAC,eAAA,CAAA,eAAA,EAAA,iBAOOC,EAPP,WAOO,aAAA,QAAAA,EAAA,OAAA,CAAA,CAAA;EAAA,GAAA;IALHC,gBAAAC,UAJR,GAAAC,mBAAA,QAAAP,GAAA;MAOWQ,EAAAA,OAAAA,UAAAA,WAAAA,EAAAA,QAAAA,WAAAA,EAAAA,KAAAA,EAAAA,CAAAA,KAAAA,UAAAA,GAAAA,mBAAAA,UAAAA,EAAAA,KAAAA,EAAAA,GAAAA;QAAAA,gBAAAA,gBAAAA,EAAAA,UAAAA,GAAAA,CAAAA;;;UAL2ET,EAAP,UAAA;MAAA,CAAAU,GAAAL,EAAA,WAAA,SAAA;MAShEI,CAAAA,GAAUJ,EAAYT,SAAW,OAAA;IAAA,CAAA;IAC1CS,EAAA,cAAA,QAAAA,EAAA,WAAA,SAZN,SAoBqCE,UAAA,GAAAC,mBAAA,OAAAN,GAAA;MAAAI,gBApBrCC,UAAAA,GAAAA,YAoBWX,wBAAeS,EAAA,OAAA,GAAAM,WAAA,EAAA,OAAA,eAAA,GAAAN,EAAA,UAAA,GAAA;QAAA,SAAAO,QAAA,MAAA;UApB1BC,gBAAAC,gBAAAT,EAAA,WAAA,QAAA,IAAA,GAAA,CAAA;QAAA,CAAA;QAAA,GAAA;;YAgB2BL,EAAP,UAAA;QAAA,CAAAU,GAAAL,EAAA,WAAA,SAAA;QAAA,CAAA,GAAA,EAAA,SAAA,OAAA;MAhBpB,CAAA;IAAA,CAAA,KAAAU,mBAAA,IAAA,IAAA;EAAA,GAAA,CAAA;;;", "names": ["_sfc_main", "defineComponent", "props", "emit", "slots", "textInSlot", "computed", "ui", "useUi", "tagName", "ref", "copyButton", "defaultButton", "merge", "onSuccess", "onError", "_hoisted_1", "_hoisted_2", "_resolveDirective", "_normalizeClass", "_ctx", "_withDirectives", "_openBlock", "_createElementBlock", "modelValue", "_directive_clipboard", "_mergeProps", "_withCtx", "_createTextVNode", "_toDisplayString", "_createCommentVNode"]}