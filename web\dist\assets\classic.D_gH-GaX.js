const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/aside.D_PtffXs.js","assets/index.GuQX7xXE.js","assets/vue.zNq9Glab.js","assets/index.WGbL7eFL.css","assets/header.BPbW56P8.js","assets/main.Mab_8bra.js","assets/tagsView.DP7TjeKr.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/tagsView.B9nwrDkZ.css"])))=>i.map(i=>d[i]);
import{u as C,_ as e}from"./index.GuQX7xXE.js";import{d as p,a as E,Q as L,M as V,p as w,o as M,m as l,b as k,g as _,e as f,w as d,f as t,u as o,P as a,l as x,h as A,I as P}from"./vue.zNq9Glab.js";const b={class:"flex-center layout-backtop"},I=p({name:"layoutClassic"}),N=p({...I,setup(B){const m=a(()=>e(()=>import("./aside.D_PtffXs.js"),__vite__mapDeps([0,1,2,3]))),y=a(()=>e(()=>import("./header.BPbW56P8.js"),__vite__mapDeps([4,1,2,3]))),v=a(()=>e(()=>import("./main.Mab_8bra.js"),__vite__mapDeps([5,1,2,3]))),T=a(()=>e(()=>import("./tagsView.DP7TjeKr.js"),__vite__mapDeps([6,1,2,3,7,8]))),s=E(),h=L(),R=C(),{themeConfig:r}=V(R),g=w(()=>r.value.isTagsview),c=()=>{var n;(n=s.value)==null||n.layoutMainScrollbarRef.update()},u=()=>{P(()=>{setTimeout(()=>{c(),s.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return M(()=>{u()}),l(()=>h.path,()=>{u()}),l(r,()=>{c()},{deep:!0}),(n,D)=>{const i=k("el-container");return f(),_(i,{class:"layout-container flex-center"},{default:d(()=>[t(o(y)),t(i,{class:"layout-mian-height-50"},{default:d(()=>[t(o(m)),x("div",b,[g.value?(f(),_(o(T),{key:0})):A("",!0),t(o(v),{ref_key:"layoutMainRef",ref:s},null,512)])]),_:1})]),_:1})}}});export{N as default};
