import{r as a,v as c,p as l,s as d}from"./index.GuQX7xXE.js";import{d as p}from"./dictionary.DBJS--kg.js";import{a as i}from"./authFunction.BcROZVTX.js";import{f as m,b as h,I as f}from"./vue.zNq9Glab.js";const o="/api/system/dictionary/";function w(t){return a({url:o,method:"get",params:t})}function y(t){return a({url:o,method:"post",data:t})}function u(t){return a({url:o+t.id+"/",method:"put",data:t})}function b(t){return a({url:o+t+"/",method:"delete",data:{id:t}})}const C=function({crudExpose:t,context:n}){return{crudOptions:{request:{pageRequest:async e=>await w(e),addRequest:async({form:e})=>await y(e),editRequest:async({form:e,row:r})=>(e.id=r.id,await u(e)),delRequest:async({row:e})=>await b(e.id)},rowHandle:{fixed:"right",width:200,buttons:{view:{show:!1},edit:{iconRight:"Edit",type:"text",show:i("dictionary:Update")},remove:{iconRight:"Delete",type:"text",show:i("dictionary:Delete")},custom:{text:"字典配置",type:"text",show:i("dictionary:Update"),tooltip:{placement:"top",content:"字典配置"},click:e=>{const{row:r}=e;n.subDictRef.value.drawer=!0,f(()=>{n.subDictRef.value.setSearchFormData({form:{parent:r.id}}),n.subDictRef.value.doRefresh()})}}}},columns:{_index:{title:"序号",form:{show:!1},column:{align:"center",width:"70px",columnSetDisabled:!0,formatter:e=>{let r=e.index??1,s=t.crudBinding.value.pagination;return((s.currentPage??1)-1)*s.pageSize+r+1}}},search:{title:"关键词",column:{show:!1},search:{show:!0,component:{props:{clearable:!0},placeholder:"请输入关键词"}},form:{show:!1,component:{props:{clearable:!0}}}},label:{title:"字典名称",search:{show:!0,component:{props:{clearable:!0}}},type:"input",column:{minWidth:120},form:{rules:[{required:!0,message:"字典名称必填项"}],component:{props:{clearable:!0},placeholder:"请输入字典名称"}}},value:{title:"字典编号",search:{disabled:!0,component:{props:{clearable:!0}}},type:"input",column:{minWidth:120},form:{rules:[{required:!0,message:"字典编号必填项"}],component:{props:{clearable:!0},placeholder:"请输入字典编号"},helper:{render(e){return m(h("el-alert"),{title:"使用方法：dictionary('字典编号')",type:"warning"},null)}}}},status:{title:"状态",search:{show:!0},type:"dict-radio",column:{minWidth:90,component:{name:"fs-dict-switch",activeText:"",inactiveText:"",style:"--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6",onChange:l(e=>()=>{u(e.row).then(r=>{d(r.msg)})})}},dict:c({data:p("button_status_bool")})},sort:{title:"排序",type:"number",column:{minWidth:80},form:{value:1}}}}}};export{C as createCrudOptions};
