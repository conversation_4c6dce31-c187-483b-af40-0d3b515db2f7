{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/azcli/azcli.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/azcli/azcli.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  }\n};\nvar language = {\n  defaultToken: \"keyword\",\n  ignoreCase: true,\n  tokenPostfix: \".azcli\",\n  str: /[^#\\s]/,\n  tokenizer: {\n    root: [\n      { include: \"@comment\" },\n      [\n        /\\s-+@str*\\s*/,\n        {\n          cases: {\n            \"@eos\": { token: \"key.identifier\", next: \"@popall\" },\n            \"@default\": { token: \"key.identifier\", next: \"@type\" }\n          }\n        }\n      ],\n      [\n        /^-+@str*\\s*/,\n        {\n          cases: {\n            \"@eos\": { token: \"key.identifier\", next: \"@popall\" },\n            \"@default\": { token: \"key.identifier\", next: \"@type\" }\n          }\n        }\n      ]\n    ],\n    type: [\n      { include: \"@comment\" },\n      [\n        /-+@str*\\s*/,\n        {\n          cases: {\n            \"@eos\": { token: \"key.identifier\", next: \"@popall\" },\n            \"@default\": \"key.identifier\"\n          }\n        }\n      ],\n      [\n        /@str+\\s*/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    comment: [\n      [\n        /#.*$/,\n        {\n          cases: {\n            \"@eos\": { token: \"comment\", next: \"@popall\" }\n          }\n        }\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,KAAK;AAAA,EACL,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,WAAW;AAAA,MACtB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,kBAAkB,MAAM,UAAU;AAAA,YACnD,YAAY,EAAE,OAAO,kBAAkB,MAAM,QAAQ;AAAA,UACvD;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,kBAAkB,MAAM,UAAU;AAAA,YACnD,YAAY,EAAE,OAAO,kBAAkB,MAAM,QAAQ;AAAA,UACvD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,EAAE,SAAS,WAAW;AAAA,MACtB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,kBAAkB,MAAM,UAAU;AAAA,YACnD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,WAAW,MAAM,UAAU;AAAA,UAC9C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}