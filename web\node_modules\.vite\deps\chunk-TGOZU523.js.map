{"version": 3, "sources": ["../../@fast-crud/src/context.ts", "../../@fast-crud/src/use/use-ui.ts", "../../@fast-crud/src/use/use-render.tsx", "../../@fast-crud/ui-interface/src/components/fs-ui-render.vue"], "sourcesContent": [null, null, null, "<script lang=\"tsx\">\nimport { UiSlot } from \"../ui-interface\";\nimport { defineComponent, PropType } from \"vue\";\n\ninterface UiRenderProps {\n  renderFn: UiRenderFn;\n}\n\ntype UiRenderFn = () => UiSlot;\nexport default defineComponent({\n  name: \"FsUiRender\",\n  props: {\n    renderFn: {\n      type: Function as PropType<UiRenderFn>,\n      default() {\n        return () => {\n          return null;\n        };\n      }\n    }\n  },\n  setup(props: UiRenderProps) {\n    return () => {\n      return props.renderFn();\n    };\n  }\n});\n</script>\n\n<style lang=\"less\"></style>\n"], "mappings": ";;;;;;;;;;;;;;IAIaA,UAAS;EAAtB,cAAA;AACE,SAAA,MAAyBC,IAAI,IAAI;EAanC;EAZE,IAAIC,GAAe;AACjB,SAAK,IAAI,QAAQA;EACnB;EAEA,MAAG;AACG,QAAA,KAAK,IAAI,SAAS;AACd,YAAA,IAAI,MACR,mPAAmP;AAGvP,WAAO,KAAK,IAAI;EAClB;AACD;AACY,IAAAC,IAAY,IAAIH,EAAAA;SChBbI,IAAK;AACZ,SAAA;IACL,WAAAD;IACA,IAAIA,EAAU,IAAK;IACnB,KAAK,CAACD,MAAmB;AACvBC,QAAU,IAAID,CAAE;IAClB;EAAA;AAEJ;ACMaG,IAAAA,IAA0CC,CAAAA,MAAmC;AACxF,QAAMC,IAAO,OAAOD,EAAQE,MAAO,WAAWC,iBAAiBH,EAAQE,EAAE,IAAIF,EAAQE;AACrF,SAAAE,YAAAH,GAAiBD,EAAQK,OAAgBL,EAAQM,KAAK;AACxD;AAHaP,IAKAQ,IAAqCA,CAACC,GAAIC,MAC9CV,EAAkBS,EAAGE,QAAQD,CAAI,CAAC;AAN9BV,IAcAY,IAA+BA,CAACH,GAAIC,GAAMG,MAA6B;AAClF,QAAMC,IAAc,CAAA,GAEdC,IAAiBN,EAAGO,YACpBC,IAAcP,EAAKI;AACrBC,OAAkB,QAAQE,MACxBA,KAAAA,QAAAA,EAAQC,QAAOD,KAAAA,QAAAA,EAAQE,QACzBL,EAAOC,CAAc,IAAIE,EAAOC,IAAG,GACnCJ,EAAO,YAAYC,CAAc,EAAE,IAAKK,CAAAA,MAAc;AACpDH,MAAOE,IAAIC,CAAK,GAChBH,EAAOI,YAAYJ,EAAOI,SAASD,CAAK;EAAA,KAEjCH,EAAOrB,OAAOqB,EAAOK,OAC9BR,EAAOC,CAAc,IAAIG,YAAID,EAAOrB,KAAKqB,EAAOK,GAAG,GACnDR,EAAO,YAAYC,CAAc,EAAE,IAAKK,CAAAA,MAAc;AACpDD,gBAAIF,EAAOrB,KAAKqB,EAAOK,KAAKF,CAAK,GACjCH,EAAOI,YAAYJ,EAAOI,SAASD,CAAK;EAAA,KAG1CG,QAAQC,KAAK,eAAef,GAAIQ,CAAM;AAI1C,QAAMQ,IAAc;IAClBtB,IAAIO,EAAKP,MAAMM,EAAGiB;IAClBpB,OAAOI,EAAKJ;IACZC,OAAOG,EAAKH;EAAAA,GAGRoB,IAAiBd,aAAmBe,WAAWf,EAAO,IAAKA;AASjE,SARegB,cACb;IACEvB,OAAOQ;EACR,GACDa,GACAF,CAAW;AAIf;AArDazB,IA4DA8B,IAA8BA,CAAerB,GAAiBI,IAA4B,CAAA,MAAS;AAC9G,QAAMkB,IAAgB;IAAE,GAAGtB;EAAAA;AAC3BsB,SAAAA,EAASC,SAAUtB,CAAAA,MACVF,EAAgBuB,GAAUrB,CAAI,GAElCqB,EAASpB,YACZoB,EAASpB,UAAWD,CAAAA,MACXE,EAAamB,GAAUrB,GAAMG,CAAO,IAG/CkB,EAASE,aAAcvB,CAAAA,MACdqB,EAASpB,QAAQD,CAAI,EAAEJ,OAEhCyB,EAASG,kBAAmBxB,CAAAA,MACnByB,SAAS,MACPJ,EAASpB,QAAQD,CAAI,CAC7B,GAEIqB;AACT;SAEgBK,IAAW;AACzB,SAAO;IACLN,SAAAA;IACA9B,mBAAAA;IACAQ,iBAAAA;IACAI,cAAAA;EAAAA;AAEJ;AChGA,IAAeyB,IAAAA,gBAAgB;EAC7BX,MAAM;EACNpB,OAAO;IACLgC,UAAU;MACRC,MAAMX;MACNY,UAAU;AACR,eAAO,MACE;MAEX;IACF;EACD;EACDC,MAAMnC,GAAsB;AAC1B,WAAO,MACEA,EAAMgC,SAAAA;EAEjB;AACF,CAAC;", "names": ["UiContext", "ref", "ui", "uiContext", "useUi", "doRenderComponent", "binding", "comp", "is", "resolveComponent", "_createVNode", "props", "slots", "renderComponent", "ci", "opts", "builder", "buildBinding", "special", "vModel", "modelValueName", "modelValue", "mvConf", "get", "set", "value", "onChange", "key", "console", "warn", "userBinding", "name", "specialBinding", "Function", "merge", "creator", "extendCI", "render", "buildProps", "builderComputed", "computed", "useUiRender", "defineComponent", "renderFn", "type", "default", "setup"]}