import{d as p,M as C,a as R,R as k,r as I,b as n,c as T,e as B,f as l,w as u,l as Q,k as M,t as N,I as O}from"./vue.zNq9Glab.js";import{J as b,Z as A}from"./index.GuQX7xXE.js";import{_ as H}from"./_plugin-vue_export-helper.DlAUqK2U.js";const U={class:"layout-search-dialog"},$=p({name:"layoutBreadcrumbSearch"}),j=p({...$,setup(D,{expose:f}){const _=b(),{tagsViewRoutes:h}=C(_),m=R(),{t:w}=A.useI18n(),d=k(),o=I({isShowSearch:!1,menuQuery:"",tagsViewList:[]}),S=()=>{o.menuQuery="",o.isShowSearch=!0,v(),O(()=>{setTimeout(()=>{m.value.focus()})})},V=()=>{o.isShowSearch=!1},g=(e,t)=>{let s=e?o.tagsViewList.filter(L(e)):o.tagsViewList;t(s)},L=e=>t=>t.path.toLowerCase().indexOf(e.toLowerCase())>-1||t.meta.title.toLowerCase().indexOf(e.toLowerCase())>-1||w(t.meta.title).indexOf(e.toLowerCase())>-1,v=()=>{if(o.tagsViewList.length>0)return!1;h.value.map(e=>{var t;(t=e.meta)!=null&&t.isHide||o.tagsViewList.push({...e})})},x=e=>{var c,r,i;let{path:t,redirect:s}=e;(c=e.meta)!=null&&c.isLink&&!((r=e.meta)!=null&&r.isIframe)?window.open((i=e.meta)==null?void 0:i.isLink):s?d.push(s):d.push(t),V()};return f({openSearch:S}),(e,t)=>{const s=n("ele-Search"),c=n("el-icon"),r=n("SvgIcon"),i=n("el-autocomplete"),y=n("el-dialog");return B(),T("div",U,[l(y,{modelValue:o.isShowSearch,"onUpdate:modelValue":t[1]||(t[1]=a=>o.isShowSearch=a),"destroy-on-close":"","show-close":!1},{footer:u(()=>[l(i,{modelValue:o.menuQuery,"onUpdate:modelValue":t[0]||(t[0]=a=>o.menuQuery=a),"fetch-suggestions":g,placeholder:e.$t("message.user.searchPlaceholder"),ref_key:"layoutMenuAutocompleteRef",ref:m,onSelect:x,"fit-input-width":!0},{prefix:u(()=>[l(c,{class:"el-input__icon"},{default:u(()=>[l(s)]),_:1})]),default:u(({item:a})=>[Q("div",null,[l(r,{name:a.meta.icon,class:"mr5"},null,8,["name"]),M(" "+N(e.$t(a.meta.title)),1)])]),_:1},8,["modelValue","placeholder"])]),_:1},8,["modelValue"])])}}}),P=H(j,[["__scopeId","data-v-0acf9122"]]);export{P as default};
