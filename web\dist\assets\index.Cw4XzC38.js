import{X as R,E as g,y as V}from"./index.GuQX7xXE.js";import B from"./index.Bcq1VDBE.js";import N from"./index.BoqVFZhq.js";import F from"./index.BJCGJ7bg.js";import{G as M,D as A}from"./api.BsLvXT84.js";import{d as y,a as l,o as E,b as d,g as v,e as C,w as c,f as s,l as x,u as n,$ as G,h as X}from"./vue.zNq9Glab.js";import{_ as j}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./index.es.DmevZXPX.js";import"./md5.DLPczxzP.js";import"./crud.B7-32-wT.js";import"./dictionary.DBJS--kg.js";import"./authFunction.BcROZVTX.js";import"./index.vue_vue_type_script_setup_true_name_importExcel_lang.BmTOIKpl.js";import"./echarts.D5sl-F-p.js";const z={class:"dept-box dept-left"},I={class:"dept-box dept-table"},K=y({name:"dept"}),L=y({...K,setup(O){let p=l([]),_=l([]),o=l(!1),i=l({}),f=l(null),D=l(null);const m=async()=>{let e=await M({});if((e==null?void 0:e.code)===2e3&&Array.isArray(e.data)){const t=R.toArrayTree(e.data,{parentKey:"parent",children:"children"});p.value=t}},T=e=>{var t;(t=f.value)==null||t.handleDoRefreshUser(e.id)},U=(e,t)=>{g.confirm("您确认删除该部门吗?","温馨提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{var r;const a=await A(e);t(),(a==null?void 0:a.code)===2e3&&(V(a.msg),m(),(r=f.value)==null||r.handleDoRefreshUser(""))})},b=(e,t)=>{var a,r;if(e==="update"&&t){const u=((r=(a=D.value)==null?void 0:a.treeRef)==null?void 0:r.currentNode.parent.data)||{};_.value=[u],i.value=t}o.value=!0},h=e=>{e==="submit"&&m(),o.value=!1,i.value={}};return E(()=>{m()}),(e,t)=>{const a=d("el-col"),r=d("el-row"),u=d("el-drawer"),k=d("fs-page");return C(),v(k,null,{default:c(()=>[s(r,{class:"dept-el-row"},{default:c(()=>[s(a,{span:6},{default:c(()=>[x("div",z,[s(B,{ref_key:"deptTreeRef",ref:D,treeData:n(p),onTreeClick:T,onUpdateDept:b,onDeleteDept:U},null,8,["treeData"])])]),_:1}),s(a,{span:18},{default:c(()=>[x("div",I,[s(F,{ref_key:"deptUserRef",ref:f},null,512)])]),_:1})]),_:1}),s(u,{modelValue:n(o),"onUpdate:modelValue":t[0]||(t[0]=w=>G(o)?o.value=w:o=w),title:"部门配置",direction:"rtl",size:"500px","close-on-click-modal":!1,"before-close":h},{default:c(()=>[n(o)?(C(),v(N,{key:0,initFormData:n(i),treeData:n(p),cacheData:n(_),onDrawerClose:h},null,8,["initFormData","treeData","cacheData"])):X("",!0)]),_:1},8,["modelValue"])]),_:1})}}}),re=j(L,[["__scopeId","data-v-9e848274"]]);export{re as default};
