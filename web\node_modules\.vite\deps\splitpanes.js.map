{"version": 3, "sources": ["../../splitpanes/dist/splitpanes.es.js"], "sourcesContent": ["import { useSlots as ue, ref as E, computed as p, watch as C, onBeforeUnmount as H, onMounted as j, provide as g, createBlock as ce, openBlock as G, resolveDynamicComponent as me, nextTick as T, h as ve, inject as M, getCurrentInstance as de, createElement<PERSON><PERSON> as fe, normalize<PERSON><PERSON><PERSON> as ze, unref as he, renderSlot as pe } from \"vue\";\nconst Se = {\n  __name: \"splitpanes\",\n  props: {\n    horizontal: { type: <PERSON>olean },\n    pushOtherPanes: { type: Boolean, default: !0 },\n    dblClickSplitter: { type: Boolean, default: !0 },\n    rtl: { type: Boolean, default: !1 },\n    // Right to left direction.\n    firstSplitter: { type: <PERSON>olean }\n  },\n  emits: [\n    \"ready\",\n    \"resize\",\n    \"resized\",\n    \"pane-click\",\n    \"pane-maximize\",\n    \"pane-add\",\n    \"pane-remove\",\n    \"splitter-click\"\n  ],\n  setup(B, { emit: x }) {\n    const v = x, u = B, w = ue(), a = E([]), k = p(() => a.value.reduce((e, n) => (e[~~n.id] = n) && e, {})), d = p(() => a.value.length), h = E(null), S = E(!1), c = E({\n      mouseDown: !1,\n      dragging: !1,\n      activeSplitter: null,\n      cursorOffset: 0\n      // Cursor offset within the splitter.\n    }), f = E({\n      // Used to detect double click on touch devices.\n      splitter: null,\n      timeoutId: null\n    }), _ = p(() => ({\n      [`splitpanes splitpanes--${u.horizontal ? \"horizontal\" : \"vertical\"}`]: !0,\n      \"splitpanes--dragging\": c.value.dragging\n    })), y = () => {\n      document.addEventListener(\"mousemove\", r, { passive: !1 }), document.addEventListener(\"mouseup\", P), \"ontouchstart\" in window && (document.addEventListener(\"touchmove\", r, { passive: !1 }), document.addEventListener(\"touchend\", P));\n    }, D = () => {\n      document.removeEventListener(\"mousemove\", r, { passive: !1 }), document.removeEventListener(\"mouseup\", P), \"ontouchstart\" in window && (document.removeEventListener(\"touchmove\", r, { passive: !1 }), document.removeEventListener(\"touchend\", P));\n    }, R = (e, n) => {\n      const t = e.target.closest(\".splitpanes__splitter\");\n      if (t) {\n        const { left: i, top: s } = t.getBoundingClientRect(), { clientX: l, clientY: o } = \"ontouchstart\" in window && e.touches ? e.touches[0] : e;\n        c.value.cursorOffset = u.horizontal ? o - s : l - i;\n      }\n      y(), c.value.mouseDown = !0, c.value.activeSplitter = n;\n    }, r = (e) => {\n      c.value.mouseDown && (e.preventDefault(), c.value.dragging = !0, requestAnimationFrame(() => {\n        J(Y(e)), v(\"resize\", a.value.map((n) => ({ min: n.min, max: n.max, size: n.size })));\n      }));\n    }, P = () => {\n      c.value.dragging && v(\"resized\", a.value.map((e) => ({ min: e.min, max: e.max, size: e.size }))), c.value.mouseDown = !1, setTimeout(() => {\n        c.value.dragging = !1, D();\n      }, 100);\n    }, O = (e, n) => {\n      \"ontouchstart\" in window && (e.preventDefault(), u.dblClickSplitter && (f.value.splitter === n ? (clearTimeout(f.value.timeoutId), f.value.timeoutId = null, A(e, n), f.value.splitter = null) : (f.value.splitter = n, f.value.timeoutId = setTimeout(() => f.value.splitter = null, 500)))), c.value.dragging || v(\"splitter-click\", a.value[n]);\n    }, A = (e, n) => {\n      let t = 0;\n      a.value = a.value.map((i, s) => (i.size = s === n ? i.max : i.min, s !== n && (t += i.min), i)), a.value[n].size -= t, v(\"pane-maximize\", a.value[n]), v(\"resized\", a.value.map((i) => ({ min: i.min, max: i.max, size: i.size })));\n    }, X = (e, n) => {\n      v(\"pane-click\", k.value[n]);\n    }, Y = (e) => {\n      const n = h.value.getBoundingClientRect(), { clientX: t, clientY: i } = \"ontouchstart\" in window && e.touches ? e.touches[0] : e;\n      return {\n        x: t - (u.horizontal ? 0 : c.value.cursorOffset) - n.left,\n        y: i - (u.horizontal ? c.value.cursorOffset : 0) - n.top\n      };\n    }, W = (e) => {\n      e = e[u.horizontal ? \"y\" : \"x\"];\n      const n = h.value[u.horizontal ? \"clientHeight\" : \"clientWidth\"];\n      return u.rtl && !u.horizontal && (e = n - e), e * 100 / n;\n    }, J = (e) => {\n      const n = c.value.activeSplitter;\n      let t = {\n        prevPanesSize: U(n),\n        nextPanesSize: b(n),\n        prevReachedMinPanes: 0,\n        nextReachedMinPanes: 0\n      };\n      const i = 0 + (u.pushOtherPanes ? 0 : t.prevPanesSize), s = 100 - (u.pushOtherPanes ? 0 : t.nextPanesSize), l = Math.max(Math.min(W(e), s), i);\n      let o = [n, n + 1], m = a.value[o[0]] || null, z = a.value[o[1]] || null;\n      const F = m.max < 100 && l >= m.max + t.prevPanesSize, re = z.max < 100 && l <= 100 - (z.max + b(n + 1));\n      if (F || re) {\n        F ? (m.size = m.max, z.size = Math.max(100 - m.max - t.prevPanesSize - t.nextPanesSize, 0)) : (m.size = Math.max(100 - z.max - t.prevPanesSize - b(n + 1), 0), z.size = z.max);\n        return;\n      }\n      if (u.pushOtherPanes) {\n        const I = K(t, l);\n        if (!I) return;\n        ({ sums: t, panesToResize: o } = I), m = a.value[o[0]] || null, z = a.value[o[1]] || null;\n      }\n      m !== null && (m.size = Math.min(Math.max(l - t.prevPanesSize - t.prevReachedMinPanes, m.min), m.max)), z !== null && (z.size = Math.min(Math.max(100 - l - t.nextPanesSize - t.nextReachedMinPanes, z.min), z.max));\n    }, K = (e, n) => {\n      const t = c.value.activeSplitter, i = [t, t + 1];\n      return n < e.prevPanesSize + a.value[i[0]].min && (i[0] = Q(t).index, e.prevReachedMinPanes = 0, i[0] < t && a.value.forEach((s, l) => {\n        l > i[0] && l <= t && (s.size = s.min, e.prevReachedMinPanes += s.min);\n      }), e.prevPanesSize = U(i[0]), i[0] === void 0) ? (e.prevReachedMinPanes = 0, a.value[0].size = a.value[0].min, a.value.forEach((s, l) => {\n        l > 0 && l <= t && (s.size = s.min, e.prevReachedMinPanes += s.min);\n      }), a.value[i[1]].size = 100 - e.prevReachedMinPanes - a.value[0].min - e.prevPanesSize - e.nextPanesSize, null) : n > 100 - e.nextPanesSize - a.value[i[1]].min && (i[1] = V(t).index, e.nextReachedMinPanes = 0, i[1] > t + 1 && a.value.forEach((s, l) => {\n        l > t && l < i[1] && (s.size = s.min, e.nextReachedMinPanes += s.min);\n      }), e.nextPanesSize = b(i[1] - 1), i[1] === void 0) ? (e.nextReachedMinPanes = 0, a.value.forEach((s, l) => {\n        l < d.value - 1 && l >= t + 1 && (s.size = s.min, e.nextReachedMinPanes += s.min);\n      }), a.value[i[0]].size = 100 - e.prevPanesSize - b(i[0] - 1), null) : { sums: e, panesToResize: i };\n    }, U = (e) => a.value.reduce((n, t, i) => n + (i < e ? t.size : 0), 0), b = (e) => a.value.reduce((n, t, i) => n + (i > e + 1 ? t.size : 0), 0), Q = (e) => [...a.value].reverse().find((t) => t.index < e && t.size > t.min) || {}, V = (e) => a.value.find((t) => t.index > e + 1 && t.size > t.min) || {}, Z = () => {\n      var n;\n      Array.from(((n = h.value) == null ? void 0 : n.children) || []).forEach((t) => {\n        const i = t.classList.contains(\"splitpanes__pane\"), s = t.classList.contains(\"splitpanes__splitter\");\n        !i && !s && (t.remove(), console.warn(\"Splitpanes: Only <pane> elements are allowed at the root of <splitpanes>. One of your DOM nodes was removed.\"));\n      });\n    }, $ = (e, n, t = !1) => {\n      const i = e - 1, s = document.createElement(\"div\");\n      s.classList.add(\"splitpanes__splitter\"), t || (s.onmousedown = (l) => R(l, i), typeof window < \"u\" && \"ontouchstart\" in window && (s.ontouchstart = (l) => R(l, i)), s.onclick = (l) => O(l, i + 1)), u.dblClickSplitter && (s.ondblclick = (l) => A(l, i + 1)), n.parentNode.insertBefore(s, n);\n    }, ee = (e) => {\n      e.onmousedown = void 0, e.onclick = void 0, e.ondblclick = void 0, e.remove();\n    }, N = () => {\n      var t;\n      const e = Array.from(((t = h.value) == null ? void 0 : t.children) || []);\n      e.forEach((i) => {\n        i.className.includes(\"splitpanes__splitter\") && ee(i);\n      });\n      let n = 0;\n      e.forEach((i) => {\n        i.className.includes(\"splitpanes__pane\") && (!n && u.firstSplitter ? $(n, i, !0) : n && $(n, i), n++);\n      });\n    }, ie = ({ uid: e, ...n }) => {\n      const t = k.value[e];\n      Object.entries(n).forEach(([i, s]) => t[i] = s);\n    }, ne = (e) => {\n      var t;\n      let n = -1;\n      Array.from(((t = h.value) == null ? void 0 : t.children) || []).some((i) => (i.className.includes(\"splitpanes__pane\") && n++, i.isSameNode(e.el))), a.value.splice(n, 0, { ...e, index: n }), a.value.forEach((i, s) => i.index = s), S.value && T(() => {\n        N(), q({ addedPane: a.value[n] }), v(\"pane-add\", { index: n, panes: a.value.map((i) => ({ min: i.min, max: i.max, size: i.size })) });\n      });\n    }, te = (e) => {\n      const n = a.value.findIndex((i) => i.id === e), t = a.value.splice(n, 1)[0];\n      a.value.forEach((i, s) => i.index = s), T(() => {\n        N(), q({ removedPane: { ...t } }), v(\"pane-remove\", { removed: t, panes: a.value.map((i) => ({ min: i.min, max: i.max, size: i.size })) });\n      });\n    }, q = (e = {}) => {\n      !e.addedPane && !e.removedPane ? ae() : a.value.some((n) => n.givenSize !== null || n.min || n.max < 100) ? le(e) : se(), S.value && v(\"resized\", a.value.map((n) => ({ min: n.min, max: n.max, size: n.size })));\n    }, se = () => {\n      const e = 100 / d.value;\n      let n = 0;\n      const t = [], i = [];\n      a.value.forEach((s) => {\n        s.size = Math.max(Math.min(e, s.max), s.min), n -= s.size, s.size >= s.max && t.push(s.id), s.size <= s.min && i.push(s.id);\n      }), n > 0.1 && L(n, t, i);\n    }, ae = () => {\n      let e = 100;\n      const n = [], t = [];\n      let i = 0;\n      a.value.forEach((l) => {\n        e -= l.size, l.givenSize !== null && i++, l.size >= l.max && n.push(l.id), l.size <= l.min && t.push(l.id);\n      });\n      let s = 100;\n      e > 0.1 && (a.value.forEach((l) => {\n        l.givenSize === null && (l.size = Math.max(Math.min(e / (d.value - i), l.max), l.min)), s -= l.size;\n      }), s > 0.1 && L(s, n, t));\n    }, le = ({ addedPane: e, removedPane: n } = {}) => {\n      let t = 100 / d.value, i = 0;\n      const s = [], l = [];\n      ((e == null ? void 0 : e.givenSize) ?? null) !== null && (t = (100 - e.givenSize) / (d.value - 1).value), a.value.forEach((o) => {\n        i -= o.size, o.size >= o.max && s.push(o.id), o.size <= o.min && l.push(o.id);\n      }), !(Math.abs(i) < 0.1) && (a.value.forEach((o) => {\n        (e == null ? void 0 : e.givenSize) !== null && (e == null ? void 0 : e.id) === o.id || (o.size = Math.max(Math.min(t, o.max), o.min)), i -= o.size, o.size >= o.max && s.push(o.id), o.size <= o.min && l.push(o.id);\n      }), i > 0.1 && L(i, s, l));\n    }, L = (e, n, t) => {\n      let i;\n      e > 0 ? i = e / (d.value - n.length) : i = e / (d.value - t.length), a.value.forEach((s, l) => {\n        if (e > 0 && !n.includes(s.id)) {\n          const o = Math.max(Math.min(s.size + i, s.max), s.min), m = o - s.size;\n          e -= m, s.size = o;\n        } else if (!t.includes(s.id)) {\n          const o = Math.max(Math.min(s.size + i, s.max), s.min), m = o - s.size;\n          e -= m, s.size = o;\n        }\n      }), Math.abs(e) > 0.1 && T(() => {\n        S.value && console.warn(\"Splitpanes: Could not resize panes correctly due to their constraints.\");\n      });\n    };\n    C(() => u.firstSplitter, () => N()), C(() => u.dblClickSplitter, (e) => {\n      [...h.value.querySelectorAll(\".splitpanes__splitter\")].forEach((t, i) => {\n        t.ondblclick = e ? (s) => A(s, i) : void 0;\n      });\n    }), H(() => S.value = !1), j(() => {\n      Z(), N(), q(), v(\"ready\"), S.value = !0;\n    });\n    const oe = () => {\n      var e;\n      return ve(\n        \"div\",\n        { ref: h, class: _.value },\n        (e = w.default) == null ? void 0 : e.call(w)\n      );\n    };\n    return g(\"panes\", a), g(\"indexedPanes\", k), g(\"horizontal\", p(() => u.horizontal)), g(\"requestUpdate\", ie), g(\"onPaneAdd\", ne), g(\"onPaneRemove\", te), g(\"onPaneClick\", X), (e, n) => (G(), ce(me(oe)));\n  }\n}, Pe = {\n  __name: \"pane\",\n  props: {\n    size: { type: [Number, String] },\n    minSize: { type: [Number, String], default: 0 },\n    maxSize: { type: [Number, String], default: 100 }\n  },\n  setup(B) {\n    var R;\n    const x = B, v = M(\"requestUpdate\"), u = M(\"onPaneAdd\"), w = M(\"horizontal\"), a = M(\"onPaneRemove\"), k = M(\"onPaneClick\"), d = (R = de()) == null ? void 0 : R.uid, h = M(\"indexedPanes\"), S = p(() => h.value[d]), c = E(null), f = p(() => {\n      const r = isNaN(x.size) || x.size === void 0 ? 0 : parseFloat(x.size);\n      return Math.max(Math.min(r, y.value), _.value);\n    }), _ = p(() => {\n      const r = parseFloat(x.minSize);\n      return isNaN(r) ? 0 : r;\n    }), y = p(() => {\n      const r = parseFloat(x.maxSize);\n      return isNaN(r) ? 100 : r;\n    }), D = p(() => {\n      var r;\n      return `${w.value ? \"height\" : \"width\"}: ${(r = S.value) == null ? void 0 : r.size}%`;\n    });\n    return j(() => {\n      u({\n        id: d,\n        el: c.value,\n        min: _.value,\n        max: y.value,\n        // The given size (useful to know the user intention).\n        givenSize: x.size === void 0 ? null : f.value,\n        size: f.value\n        // The computed current size at any time.\n      });\n    }), C(() => f.value, (r) => v({ uid: d, size: r })), C(() => _.value, (r) => v({ uid: d, min: r })), C(() => y.value, (r) => v({ uid: d, max: r })), H(() => a(d)), (r, P) => (G(), fe(\"div\", {\n      ref_key: \"paneEl\",\n      ref: c,\n      class: \"splitpanes__pane\",\n      onClick: P[0] || (P[0] = (O) => he(k)(O, r._.uid)),\n      style: ze(D.value)\n    }, [\n      pe(r.$slots, \"default\")\n    ], 4));\n  }\n};\nexport {\n  Pe as Pane,\n  Se as Splitpanes\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAM,KAAK;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,YAAY,EAAE,MAAM,QAAQ;AAAA,IAC5B,gBAAgB,EAAE,MAAM,SAAS,SAAS,KAAG;AAAA,IAC7C,kBAAkB,EAAE,MAAM,SAAS,SAAS,KAAG;AAAA,IAC/C,KAAK,EAAE,MAAM,SAAS,SAAS,MAAG;AAAA;AAAA,IAElC,eAAe,EAAE,MAAM,QAAQ;AAAA,EACjC;AAAA,EACA,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,GAAG,IAAI,GAAG,IAAI,SAAG,GAAG,IAAI,IAAE,CAAC,CAAC,GAAG,IAAI,SAAE,MAAM,EAAE,MAAM,OAAO,CAAC,GAAG,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,SAAE,MAAM,EAAE,MAAM,MAAM,GAAGA,KAAI,IAAE,IAAI,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE;AAAA,MACnK,WAAW;AAAA,MACX,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,cAAc;AAAA;AAAA,IAEhB,CAAC,GAAG,IAAI,IAAE;AAAA;AAAA,MAER,UAAU;AAAA,MACV,WAAW;AAAA,IACb,CAAC,GAAG,IAAI,SAAE,OAAO;AAAA,MACf,CAAC,0BAA0B,EAAE,aAAa,eAAe,UAAU,EAAE,GAAG;AAAA,MACxE,wBAAwB,EAAE,MAAM;AAAA,IAClC,EAAE,GAAG,IAAI,MAAM;AACb,eAAS,iBAAiB,aAAa,GAAG,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,iBAAiB,WAAW,CAAC,GAAG,kBAAkB,WAAW,SAAS,iBAAiB,aAAa,GAAG,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,iBAAiB,YAAY,CAAC;AAAA,IACvO,GAAG,IAAI,MAAM;AACX,eAAS,oBAAoB,aAAa,GAAG,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,oBAAoB,WAAW,CAAC,GAAG,kBAAkB,WAAW,SAAS,oBAAoB,aAAa,GAAG,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,oBAAoB,YAAY,CAAC;AAAA,IACnP,GAAG,IAAI,CAAC,GAAG,MAAM;AACf,YAAM,IAAI,EAAE,OAAO,QAAQ,uBAAuB;AAClD,UAAI,GAAG;AACL,cAAM,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,EAAE,sBAAsB,GAAG,EAAE,SAAS,GAAG,SAAS,EAAE,IAAI,kBAAkB,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AAC3I,UAAE,MAAM,eAAe,EAAE,aAAa,IAAI,IAAI,IAAI;AAAA,MACpD;AACA,QAAE,GAAG,EAAE,MAAM,YAAY,MAAI,EAAE,MAAM,iBAAiB;AAAA,IACxD,GAAG,IAAI,CAAC,MAAM;AACZ,QAAE,MAAM,cAAc,EAAE,eAAe,GAAG,EAAE,MAAM,WAAW,MAAI,sBAAsB,MAAM;AAC3F,UAAE,EAAE,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,MACrF,CAAC;AAAA,IACH,GAAG,IAAI,MAAM;AACX,QAAE,MAAM,YAAY,EAAE,WAAW,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,YAAY,OAAI,WAAW,MAAM;AACzI,UAAE,MAAM,WAAW,OAAI,EAAE;AAAA,MAC3B,GAAG,GAAG;AAAA,IACR,GAAG,IAAI,CAAC,GAAG,MAAM;AACf,wBAAkB,WAAW,EAAE,eAAe,GAAG,EAAE,qBAAqB,EAAE,MAAM,aAAa,KAAK,aAAa,EAAE,MAAM,SAAS,GAAG,EAAE,MAAM,YAAY,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,WAAW,SAAS,EAAE,MAAM,WAAW,GAAG,EAAE,MAAM,YAAY,WAAW,MAAM,EAAE,MAAM,WAAW,MAAM,GAAG,MAAM,EAAE,MAAM,YAAY,EAAE,kBAAkB,EAAE,MAAM,CAAC,CAAC;AAAA,IACnV,GAAG,IAAI,CAAC,GAAG,MAAM;AACf,UAAI,IAAI;AACR,QAAE,QAAQ,EAAE,MAAM,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,MAAM,IAAI,EAAE,MAAM,EAAE,KAAK,MAAM,MAAM,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,EAAE,iBAAiB,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IACpO,GAAG,IAAI,CAAC,GAAG,MAAM;AACf,QAAE,cAAc,EAAE,MAAM,CAAC,CAAC;AAAA,IAC5B,GAAG,IAAI,CAAC,MAAM;AACZ,YAAM,IAAIA,GAAE,MAAM,sBAAsB,GAAG,EAAE,SAAS,GAAG,SAAS,EAAE,IAAI,kBAAkB,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AAC/H,aAAO;AAAA,QACL,GAAG,KAAK,EAAE,aAAa,IAAI,EAAE,MAAM,gBAAgB,EAAE;AAAA,QACrD,GAAG,KAAK,EAAE,aAAa,EAAE,MAAM,eAAe,KAAK,EAAE;AAAA,MACvD;AAAA,IACF,GAAG,IAAI,CAAC,MAAM;AACZ,UAAI,EAAE,EAAE,aAAa,MAAM,GAAG;AAC9B,YAAM,IAAIA,GAAE,MAAM,EAAE,aAAa,iBAAiB,aAAa;AAC/D,aAAO,EAAE,OAAO,CAAC,EAAE,eAAe,IAAI,IAAI,IAAI,IAAI,MAAM;AAAA,IAC1D,GAAG,IAAI,CAAC,MAAM;AACZ,YAAM,IAAI,EAAE,MAAM;AAClB,UAAI,IAAI;AAAA,QACN,eAAe,EAAE,CAAC;AAAA,QAClB,eAAe,EAAE,CAAC;AAAA,QAClB,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB;AACA,YAAM,IAAI,KAAK,EAAE,iBAAiB,IAAI,EAAE,gBAAgB,IAAI,OAAO,EAAE,iBAAiB,IAAI,EAAE,gBAAgB,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;AAC7I,UAAI,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,MAAM,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK;AACpE,YAAM,IAAI,EAAE,MAAM,OAAO,KAAK,EAAE,MAAM,EAAE,eAAe,KAAK,EAAE,MAAM,OAAO,KAAK,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC;AACtG,UAAI,KAAK,IAAI;AACX,aAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,KAAK,IAAI,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,eAAe,CAAC,MAAM,EAAE,OAAO,KAAK,IAAI,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE;AAC1K;AAAA,MACF;AACA,UAAI,EAAE,gBAAgB;AACpB,cAAM,IAAI,EAAE,GAAG,CAAC;AAChB,YAAI,CAAC,EAAG;AACR,SAAC,EAAE,MAAM,GAAG,eAAe,EAAE,IAAI,IAAI,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,MAAM,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK;AAAA,MACvF;AACA,YAAM,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,MAAM,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,IAAI,MAAM,IAAI,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,GAAG,GAAG,EAAE,GAAG;AAAA,IACpN,GAAG,IAAI,CAAC,GAAG,MAAM;AACf,YAAM,IAAI,EAAE,MAAM,gBAAgB,IAAI,CAAC,GAAG,IAAI,CAAC;AAC/C,aAAO,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,sBAAsB,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM;AACrI,YAAI,EAAE,CAAC,KAAK,KAAK,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MACpE,CAAC,GAAG,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,WAAW,EAAE,sBAAsB,GAAG,EAAE,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM;AACxI,YAAI,KAAK,KAAK,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MACjE,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE,sBAAsB,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,eAAe,QAAQ,IAAI,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,sBAAsB,GAAG,EAAE,CAAC,IAAI,IAAI,KAAK,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC3P,YAAI,KAAK,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MACnE,CAAC,GAAG,EAAE,gBAAgB,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,WAAW,EAAE,sBAAsB,GAAG,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC1G,YAAI,EAAE,QAAQ,KAAK,KAAK,IAAI,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MAC/E,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE,gBAAgB,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,QAAQ,EAAE,MAAM,GAAG,eAAe,EAAE;AAAA,IACpG,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,KAAK,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI,MAAM;AACtT,UAAI;AACJ,YAAM,OAAO,IAAIA,GAAE,UAAU,OAAO,SAAS,EAAE,aAAa,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM;AAC7E,cAAM,IAAI,EAAE,UAAU,SAAS,kBAAkB,GAAG,IAAI,EAAE,UAAU,SAAS,sBAAsB;AACnG,SAAC,KAAK,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,KAAK,8GAA8G;AAAA,MACtJ,CAAC;AAAA,IACH,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,UAAO;AACvB,YAAM,IAAI,IAAI,GAAG,IAAI,SAAS,cAAc,KAAK;AACjD,QAAE,UAAU,IAAI,sBAAsB,GAAG,MAAM,EAAE,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,SAAS,OAAO,kBAAkB,WAAW,EAAE,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,qBAAqB,EAAE,aAAa,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,WAAW,aAAa,GAAG,CAAC;AAAA,IACjS,GAAG,KAAK,CAAC,MAAM;AACb,QAAE,cAAc,QAAQ,EAAE,UAAU,QAAQ,EAAE,aAAa,QAAQ,EAAE,OAAO;AAAA,IAC9E,GAAG,IAAI,MAAM;AACX,UAAI;AACJ,YAAM,IAAI,MAAM,OAAO,IAAIA,GAAE,UAAU,OAAO,SAAS,EAAE,aAAa,CAAC,CAAC;AACxE,QAAE,QAAQ,CAAC,MAAM;AACf,UAAE,UAAU,SAAS,sBAAsB,KAAK,GAAG,CAAC;AAAA,MACtD,CAAC;AACD,UAAI,IAAI;AACR,QAAE,QAAQ,CAAC,MAAM;AACf,UAAE,UAAU,SAAS,kBAAkB,MAAM,CAAC,KAAK,EAAE,gBAAgB,EAAE,GAAG,GAAG,IAAE,IAAI,KAAK,EAAE,GAAG,CAAC,GAAG;AAAA,MACnG,CAAC;AAAA,IACH,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG,GAAG,EAAE,MAAM;AAC5B,YAAM,IAAI,EAAE,MAAM,CAAC;AACnB,aAAO,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC;AAAA,IAChD,GAAG,KAAK,CAAC,MAAM;AACb,UAAI;AACJ,UAAI,IAAI;AACR,YAAM,OAAO,IAAIA,GAAE,UAAU,OAAO,SAAS,EAAE,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,EAAE,UAAU,SAAS,kBAAkB,KAAK,KAAK,EAAE,WAAW,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,OAAO,GAAG,GAAG,EAAE,GAAG,GAAG,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC,GAAG,EAAE,SAAS,SAAE,MAAM;AACvP,UAAE,GAAG,EAAE,EAAE,WAAW,EAAE,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,YAAY,EAAE,OAAO,GAAG,OAAO,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;AAAA,MACtI,CAAC;AAAA,IACH,GAAG,KAAK,CAAC,MAAM;AACb,YAAM,IAAI,EAAE,MAAM,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,IAAI,EAAE,MAAM,OAAO,GAAG,CAAC,EAAE,CAAC;AAC1E,QAAE,MAAM,QAAQ,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC,GAAG,SAAE,MAAM;AAC9C,UAAE,GAAG,EAAE,EAAE,aAAa,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,eAAe,EAAE,SAAS,GAAG,OAAO,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;AAAA,MAC3I,CAAC;AAAA,IACH,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM;AACjB,OAAC,EAAE,aAAa,CAAC,EAAE,cAAc,GAAG,IAAI,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,cAAc,QAAQ,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IAClN,GAAG,KAAK,MAAM;AACZ,YAAM,IAAI,MAAM,EAAE;AAClB,UAAI,IAAI;AACR,YAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AACnB,QAAE,MAAM,QAAQ,CAAC,MAAM;AACrB,UAAE,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AAAA,MAC5H,CAAC,GAAG,IAAI,OAAO,EAAE,GAAG,GAAG,CAAC;AAAA,IAC1B,GAAG,KAAK,MAAM;AACZ,UAAI,IAAI;AACR,YAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AACnB,UAAI,IAAI;AACR,QAAE,MAAM,QAAQ,CAAC,MAAM;AACrB,aAAK,EAAE,MAAM,EAAE,cAAc,QAAQ,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AAAA,MAC3G,CAAC;AACD,UAAI,IAAI;AACR,UAAI,QAAQ,EAAE,MAAM,QAAQ,CAAC,MAAM;AACjC,UAAE,cAAc,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,QAAQ,IAAI,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,KAAK,EAAE;AAAA,MACjG,CAAC,GAAG,IAAI,OAAO,EAAE,GAAG,GAAG,CAAC;AAAA,IAC1B,GAAG,KAAK,CAAC,EAAE,WAAW,GAAG,aAAa,EAAE,IAAI,CAAC,MAAM;AACjD,UAAI,IAAI,MAAM,EAAE,OAAO,IAAI;AAC3B,YAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AACnB,QAAE,KAAK,OAAO,SAAS,EAAE,cAAc,UAAU,SAAS,KAAK,MAAM,EAAE,cAAc,EAAE,QAAQ,GAAG,QAAQ,EAAE,MAAM,QAAQ,CAAC,MAAM;AAC/H,aAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AAAA,MAC9E,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,SAAS,EAAE,MAAM,QAAQ,CAAC,MAAM;AAClD,SAAC,KAAK,OAAO,SAAS,EAAE,eAAe,SAAS,KAAK,OAAO,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AAAA,MACrN,CAAC,GAAG,IAAI,OAAO,EAAE,GAAG,GAAG,CAAC;AAAA,IAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM;AAClB,UAAI;AACJ,UAAI,IAAI,IAAI,KAAK,EAAE,QAAQ,EAAE,UAAU,IAAI,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC7F,YAAI,IAAI,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,GAAG;AAC9B,gBAAM,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,OAAO,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,IAAI,EAAE;AAClE,eAAK,GAAG,EAAE,OAAO;AAAA,QACnB,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,GAAG;AAC5B,gBAAM,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,OAAO,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,IAAI,EAAE;AAClE,eAAK,GAAG,EAAE,OAAO;AAAA,QACnB;AAAA,MACF,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,OAAO,SAAE,MAAM;AAC/B,UAAE,SAAS,QAAQ,KAAK,wEAAwE;AAAA,MAClG,CAAC;AAAA,IACH;AACA,UAAE,MAAM,EAAE,eAAe,MAAM,EAAE,CAAC,GAAG,MAAE,MAAM,EAAE,kBAAkB,CAAC,MAAM;AACtE,OAAC,GAAGA,GAAE,MAAM,iBAAiB,uBAAuB,CAAC,EAAE,QAAQ,CAAC,GAAG,MAAM;AACvE,UAAE,aAAa,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI;AAAA,MACtC,CAAC;AAAA,IACH,CAAC,GAAG,gBAAE,MAAM,EAAE,QAAQ,KAAE,GAAG,UAAE,MAAM;AACjC,QAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,GAAG,EAAE,QAAQ;AAAA,IACvC,CAAC;AACD,UAAM,KAAK,MAAM;AACf,UAAI;AACJ,aAAO;AAAA,QACL;AAAA,QACA,EAAE,KAAKA,IAAG,OAAO,EAAE,MAAM;AAAA,SACxB,IAAI,EAAE,YAAY,OAAO,SAAS,EAAE,KAAK,CAAC;AAAA,MAC7C;AAAA,IACF;AACA,WAAO,QAAE,SAAS,CAAC,GAAG,QAAE,gBAAgB,CAAC,GAAG,QAAE,cAAc,SAAE,MAAM,EAAE,UAAU,CAAC,GAAG,QAAE,iBAAiB,EAAE,GAAG,QAAE,aAAa,EAAE,GAAG,QAAE,gBAAgB,EAAE,GAAG,QAAE,eAAe,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,YAAG,wBAAG,EAAE,CAAC;AAAA,EACvM;AACF;AApMA,IAoMG,KAAK;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,MAAM,EAAE,MAAM,CAAC,QAAQ,MAAM,EAAE;AAAA,IAC/B,SAAS,EAAE,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,EAAE;AAAA,IAC9C,SAAS,EAAE,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,IAAI;AAAA,EAClD;AAAA,EACA,MAAM,GAAG;AACP,QAAI;AACJ,UAAM,IAAI,GAAG,IAAI,OAAE,eAAe,GAAG,IAAI,OAAE,WAAW,GAAG,IAAI,OAAE,YAAY,GAAG,IAAI,OAAE,cAAc,GAAG,IAAI,OAAE,aAAa,GAAG,KAAK,IAAI,mBAAG,MAAM,OAAO,SAAS,EAAE,KAAKA,KAAI,OAAE,cAAc,GAAG,IAAI,SAAE,MAAMA,GAAE,MAAM,CAAC,CAAC,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,SAAE,MAAM;AAC3O,YAAM,IAAI,MAAM,EAAE,IAAI,KAAK,EAAE,SAAS,SAAS,IAAI,WAAW,EAAE,IAAI;AACpE,aAAO,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,KAAK,GAAG,EAAE,KAAK;AAAA,IAC/C,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,YAAM,IAAI,WAAW,EAAE,OAAO;AAC9B,aAAO,MAAM,CAAC,IAAI,IAAI;AAAA,IACxB,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,YAAM,IAAI,WAAW,EAAE,OAAO;AAC9B,aAAO,MAAM,CAAC,IAAI,MAAM;AAAA,IAC1B,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,UAAI;AACJ,aAAO,GAAG,EAAE,QAAQ,WAAW,OAAO,MAAM,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,IAAI;AAAA,IACpF,CAAC;AACD,WAAO,UAAE,MAAM;AACb,QAAE;AAAA,QACA,IAAI;AAAA,QACJ,IAAI,EAAE;AAAA,QACN,KAAK,EAAE;AAAA,QACP,KAAK,EAAE;AAAA;AAAA,QAEP,WAAW,EAAE,SAAS,SAAS,OAAO,EAAE;AAAA,QACxC,MAAM,EAAE;AAAA;AAAA,MAEV,CAAC;AAAA,IACH,CAAC,GAAG,MAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,GAAG,MAAM,EAAE,CAAC,CAAC,GAAG,MAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,MAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,gBAAE,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAG,OAAO;AAAA,MAC5L,SAAS;AAAA,MACT,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,MAAG,CAAC,EAAE,GAAG,EAAE,EAAE,GAAG;AAAA,MAChD,OAAO,eAAG,EAAE,KAAK;AAAA,IACnB,GAAG;AAAA,MACD,WAAG,EAAE,QAAQ,SAAS;AAAA,IACxB,GAAG,CAAC;AAAA,EACN;AACF;", "names": ["h"]}