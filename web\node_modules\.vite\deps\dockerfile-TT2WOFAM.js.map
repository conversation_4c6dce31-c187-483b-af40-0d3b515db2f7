{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/dockerfile/dockerfile.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".dockerfile\",\n  variable: /\\${?[\\w]+}?/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      [/(ONBUILD)(\\s+)/, [\"keyword\", \"\"]],\n      [/(ENV)(\\s+)([\\w]+)/, [\"keyword\", \"\", { token: \"variable\", next: \"@arguments\" }]],\n      [\n        /(FROM|MAINTAINER|RUN|EXPOSE|ENV|ADD|ARG|VOLUME|LABEL|USER|WORKDIR|COPY|CMD|STOPSIGNAL|SHELL|HEALTHCHECK|ENTRYPOINT)/,\n        { token: \"keyword\", next: \"@arguments\" }\n      ]\n    ],\n    arguments: [\n      { include: \"@whitespace\" },\n      { include: \"@strings\" },\n      [\n        /(@variable)/,\n        {\n          cases: {\n            \"@eos\": { token: \"variable\", next: \"@popall\" },\n            \"@default\": \"variable\"\n          }\n        }\n      ],\n      [\n        /\\\\/,\n        {\n          cases: {\n            \"@eos\": \"\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [\n        /./,\n        {\n          cases: {\n            \"@eos\": { token: \"\", next: \"@popall\" },\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    // Deal with white space, including comments\n    whitespace: [\n      [\n        /\\s+/,\n        {\n          cases: {\n            \"@eos\": { token: \"\", next: \"@popall\" },\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    comment: [[/(^#.*$)/, \"comment\", \"@popall\"]],\n    // Recognize strings, including those broken across lines with \\ (but not without)\n    strings: [\n      [/\\\\'$/, \"\", \"@popall\"],\n      // \\' leaves @arguments at eol\n      [/\\\\'/, \"\"],\n      // \\' is not a string\n      [/'$/, \"string\", \"@popall\"],\n      [/'/, \"string\", \"@stringBody\"],\n      [/\"$/, \"string\", \"@popall\"],\n      [/\"/, \"string\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [\n        /[^\\\\\\$']/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/\\\\./, \"string.escape\"],\n      [/'$/, \"string\", \"@popall\"],\n      [/'/, \"string\", \"@pop\"],\n      [/(@variable)/, \"variable\"],\n      [/\\\\$/, \"string\"],\n      [/$/, \"string\", \"@popall\"]\n    ],\n    dblStringBody: [\n      [\n        /[^\\\\\\$\"]/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/\\\\./, \"string.escape\"],\n      [/\"$/, \"string\", \"@popall\"],\n      [/\"/, \"string\", \"@pop\"],\n      [/(@variable)/, \"variable\"],\n      [/\\\\$/, \"string\"],\n      [/$/, \"string\", \"@popall\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,EACV,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;AAAA,MAClC,CAAC,qBAAqB,CAAC,WAAW,IAAI,EAAE,OAAO,YAAY,MAAM,aAAa,CAAC,CAAC;AAAA,MAChF;AAAA,QACE;AAAA,QACA,EAAE,OAAO,WAAW,MAAM,aAAa;AAAA,MACzC;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,YAAY,MAAM,UAAU;AAAA,YAC7C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ;AAAA,YACR,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,IAAI,MAAM,UAAU;AAAA,YACrC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,YAAY;AAAA,MACV;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,IAAI,MAAM,UAAU;AAAA,YACrC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS,CAAC,CAAC,WAAW,WAAW,SAAS,CAAC;AAAA;AAAA,IAE3C,SAAS;AAAA,MACP,CAAC,QAAQ,IAAI,SAAS;AAAA;AAAA,MAEtB,CAAC,OAAO,EAAE;AAAA;AAAA,MAEV,CAAC,MAAM,UAAU,SAAS;AAAA,MAC1B,CAAC,KAAK,UAAU,aAAa;AAAA,MAC7B,CAAC,MAAM,UAAU,SAAS;AAAA,MAC1B,CAAC,KAAK,UAAU,gBAAgB;AAAA,IAClC;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,MAAM,UAAU,SAAS;AAAA,MAC1B,CAAC,KAAK,UAAU,MAAM;AAAA,MACtB,CAAC,eAAe,UAAU;AAAA,MAC1B,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,KAAK,UAAU,SAAS;AAAA,IAC3B;AAAA,IACA,eAAe;AAAA,MACb;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,MAAM,UAAU,SAAS;AAAA,MAC1B,CAAC,KAAK,UAAU,MAAM;AAAA,MACtB,CAAC,eAAe,UAAU;AAAA,MAC1B,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,KAAK,UAAU,SAAS;AAAA,IAC3B;AAAA,EACF;AACF;", "names": []}