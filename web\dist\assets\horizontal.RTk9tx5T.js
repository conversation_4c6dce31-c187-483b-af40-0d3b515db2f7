const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/subItem.LfngTZ58.js","assets/vue.zNq9Glab.js","assets/index.GuQX7xXE.js","assets/index.WGbL7eFL.css"])))=>i.map(i=>d[i]);
import{I as H,u as N,_ as V,O as j,l as q}from"./index.GuQX7xXE.js";import{d as b,R as Q,r as W,a as y,M as S,Q as Y,p as Z,N as G,o as J,b as u,c as d,e as a,f as r,w as c,F as R,j as K,g as v,u as U,P as X,l as M,t as h,Y as ee,k as T,Z as te,I as ne}from"./vue.zNq9Glab.js";import{_ as se}from"./_plugin-vue_export-helper.DlAUqK2U.js";const ie={class:"el-menu-horizontal-warp"},oe=["onClick"],le=b({name:"navMenuHorizontal"}),ae=b({...le,props:{menuList:{type:Array,default:()=>[]}},setup($){const m=Q(),w=X(()=>V(()=>import("./subItem.LfngTZ58.js"),__vite__mapDeps([0,1,2,3])));W({menuList:[],clientWidth:0});const f=$;y();const z=H(),A=N(),{routesList:p}=S(z),{themeConfig:_}=S(A),k=Y(),o=y(""),C=Z(()=>(f.menuList.shift(),f.menuList)),g=(s,i)=>{for(let e=0;e<s.length;e++){const t=s[e];if(t.children&&t.children.length>0&&(t.children.findIndex(n=>n.path===i)!==-1||g(t.children,i)!==null))return e}return null},B=()=>{ne(()=>{if(!document.querySelector(".el-menu.el-menu--horizontal li.is-active"))return!1})},x=s=>s.filter(i=>{var e;return!((e=i.meta)!=null&&e.isHide)}).map(i=>(i=Object.assign({},i),i.children&&(i.children=x(i.children)),i)),D=s=>{const i=s.split("/");let e={children:[]};return x(p.value).map((t,l)=>{t.path===`/${i[1]}`&&(t.k=l,e.item={...t},e.children=[{...t}],t.children&&(e.children=t.children))}),e},O=s=>{const{path:i,meta:e}=s;if(_.value.layout==="classic"){let t=(g(p.value,k.path)||0)-1;o.value=t<0?o.value:C.value[t].path}else{const t=e!=null&&e.isDynamic?e.isDynamicPath.split("/"):i.split("/");t.length>=4&&(e!=null&&e.isHide)?o.value=t.splice(0,3).join("/"):o.value=i}},P=s=>{j.handleOpenLink(s)},L=(s,i)=>{let e=s.children;if(e===void 0&&(o.value=s.path,e=D(s.path).children),e.length>=1){if(e[0].is_catalog){L(e[0]);return}m.push(e[0].path);let{layout:t,isClassicSplitMenu:l}=_.value;t==="classic"&&l&&q.emit("setSendClassicChildren",e[0])}else m.push("/home")};return G(()=>{O(k)}),J(()=>{B()}),(s,i)=>{const e=u("SvgIcon"),t=u("el-sub-menu"),l=u("el-menu-item"),I=u("el-menu");return a(),d("div",ie,[r(I,{"default-active":o.value,"background-color":"transparent",mode:"horizontal"},{default:c(()=>[(a(!0),d(R,null,K(C.value,(n,E)=>(a(),d(R,null,[n.children&&n.children.length>0?(a(),v(t,{index:n.path,key:n.path},{title:c(()=>[r(e,{name:n.meta.icon},null,8,["name"]),M("span",null,h(s.$t(n.meta.title)),1)]),default:c(()=>[r(U(w),{chil:n.children},null,8,["chil"])]),_:2},1032,["index"])):(a(),v(l,{index:n.path,key:n.path,style:{"--el-menu-active-color":"#fff"},onClick:F=>L(n,E)},ee({_:2},[!n.meta.isLink||n.meta.isLink&&n.meta.isIframe?{name:"title",fn:c(()=>[r(e,{name:n.meta.icon},null,8,["name"]),T(" "+h(s.$t(n.meta.title)),1)]),key:"0"}:{name:"title",fn:c(()=>[M("a",{class:"w100",onClick:te(F=>P(n),["prevent"])},[r(e,{name:n.meta.icon},null,8,["name"]),T(" "+h(s.$t(n.meta.title)),1)],8,oe)]),key:"1"}]),1032,["index","onClick"]))],64))),256))]),_:1},8,["default-active"])])}}}),de=se(ae,[["__scopeId","data-v-051a3747"]]);export{de as default};
