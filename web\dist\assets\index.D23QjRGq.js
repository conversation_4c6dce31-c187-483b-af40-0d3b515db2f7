const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.rY0TyIbQ.js","assets/index.GuQX7xXE.js","assets/vue.zNq9Glab.js","assets/index.WGbL7eFL.css","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/index.DAOEvW-k.css","assets/tagsView.DP7TjeKr.js","assets/tagsView.B9nwrDkZ.css"])))=>i.map(i=>d[i]);
import{u as d,_ as t}from"./index.GuQX7xXE.js";import{d as r,M as f,p,c as l,e as s,f as v,g as T,h as g,u as a,P as n}from"./vue.zNq9Glab.js";import{_ as h}from"./_plugin-vue_export-helper.DlAUqK2U.js";const C={class:"layout-navbars-container"},V=r({name:"layoutNavBars"}),x=r({...V,setup(y){const c=n(()=>t(()=>import("./index.rY0TyIbQ.js"),__vite__mapDeps([0,1,2,3,4,5]))),_=n(()=>t(()=>import("./tagsView.DP7TjeKr.js"),__vite__mapDeps([6,1,2,3,4,7]))),i=d(),{themeConfig:m}=f(i),u=p(()=>{let{layout:e,isTagsview:o}=m.value;return e!=="classic"&&o});return(e,o)=>(s(),l("div",C,[v(a(c)),u.value?(s(),T(a(_),{key:0})):g("",!0)]))}}),w=h(x,[["__scopeId","data-v-1de146f5"]]);export{w as default};
