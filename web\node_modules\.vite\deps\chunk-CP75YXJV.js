import {
  Za,
  lp,
  ot,
  vt,
  vu,
  xl
} from "./chunk-TJTCSOX4.js";
import {
  B,
  i
} from "./chunk-TGOZU523.js";
import {
  cloneDeep_default,
  merge_default
} from "./chunk-LK7GAOJV.js";
import {
  Fragment,
  computed,
  createBaseVNode,
  createBlock,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineComponent,
  guardReactiveProps,
  mergeProps,
  normalizeProps,
  openBlock,
  ref,
  renderList,
  resolveComponent,
  resolveDynamicComponent,
  unref,
  watch,
  withCtx
} from "./chunk-WEJJSMSC.js";

// node_modules/@fast-crud/fast-extends/dist/index-a0aeea35.mjs
var ge = defineComponent({
  name: "FsImagesFormat",
  inheritAttrs: false,
  props: {
    //包裹image的组件配置，antdv是preview-group，element是div
    wrapper: {
      type: Object,
      default: null
    },
    // 图片的url
    // 'url' 或 ['url','url'] 或 [{url:'url'}] 或 {url:'url'}
    modelValue: {
      type: [String, Array, Object],
      require: true
    },
    /**
     * 构建好的图片链接，如果此处传值，则不走buildUrl方法
     * 'url' 或 ['url1','url2'] 或 {url,previewUrl} 或 [{url,previewUrl}]
     */
    urls: {
      type: [String, Object, Array]
    },
    /**
     * 加载错误时显示的图片
     */
    error: {
      default: 'data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="24" height="24" viewBox="0 0 24 24"%3E%3Cpath fill="%23888" d="M5 21q-.825 0-1.413-.588T3 19v-6.6l3 3l4-4l4 4l4-4l3 3V19q0 .825-.588 1.413T19 21H5ZM5 3h14q.825 0 1.413.588T21 5v6.575l-3-3l-4 4l-4-4l-4 4l-3-3V5q0-.825.588-1.413T5 3Z"%2F%3E%3C%2Fsvg%3E'
    },
    /**
     * 从value构建图片下载url的方法
     * 支持异步
     * (value)=>Promise<string>
     */
    buildUrl: {
      type: Function,
      default: function(e) {
        return e;
      }
    },
    /**
     * (values)=>Promise<string[]>
     */
    buildUrls: {
      type: Function,
      default: null
    },
    /**
     * 从value或url构建预览大图的方法
     * (url,value)=>Promise<string>
     */
    buildPreviewUrl: {
      type: Function,
      default: function({ url: e, value: t, index: n }) {
        return e;
      }
    },
    /**
     * (list,values)=>Promise<string[]>
     */
    buildPreviewUrls: {
      type: Function,
      default: null
    }
  },
  setup(e, t) {
    const { ui: n } = B(), i2 = ref([]), r = computed(() => merge_default({ style: {} }, { style: t.attrs.style })), o = computed(() => {
      const l = [];
      if (e.modelValue == null || e.modelValue === "")
        return l;
      if (typeof e.modelValue == "string")
        l.push(e.modelValue);
      else if (Array.isArray(e.modelValue))
        for (const s of e.modelValue)
          s != null && (s.url != null ? s.previewUrl != null ? l.push(s) : l.push(s.url) : l.push(s));
      else
        e.modelValue.url != null ? e.modelValue.previewUrl != null ? l.push(e.modelValue) : l.push(e.modelValue.url) : l.push(e.modelValue);
      return l;
    });
    function a(l) {
      const s = [], p = [];
      for (let v = 0; v < l.length; v++) {
        const T = l[v];
        s.push(T.url), p.push(T.previewUrl);
      }
      const m = [];
      for (let v = 0; v < l.length; v++) {
        const T = l[v], k = T.url, S = T.previewUrl || T.url, U = n.image.buildPreviewBind({
          url: k,
          urls: s,
          previewUrl: S,
          previewUrls: p,
          index: v
        });
        m.push({
          fit: "contain",
          src: k,
          [n.image.fallback]: e.error,
          ...t.attrs,
          ...U
        });
      }
      return m;
    }
    async function f(l) {
      if (e.buildUrls) {
        const s = l.map((v) => v.value), p = await e.buildUrls(s);
        for (let v = 0; v < l.length; v++)
          l[v].url = p[v];
        let m = p;
        e.buildPreviewUrls && (m = await e.buildPreviewUrls(l, s));
        for (let v = 0; v < l.length; v++)
          l[v].previewUrl = m[v];
      } else if (e.buildUrl)
        for (let s of l)
          s.url = await e.buildUrl(s.value), e.buildPreviewUrl ? s.previewUrl = await e.buildPreviewUrl(s, s.value) : s.previewUrl = s.previewUrl || s.url;
      else
        for (let s = 0; s < l.length; s++)
          l[s].url = l[s].value, l[s].previewUrl = l[s].value;
    }
    async function g(l) {
      const s = [];
      for (let p = 0; p < l.length; p++) {
        let m = l[p];
        s.push({
          value: m,
          index: p
        });
      }
      return await f(s), a(s);
    }
    async function C(l) {
      const s = [];
      if (typeof l == "string")
        s.push({
          value: l,
          url: l,
          index: 0,
          previewUrl: l
        });
      else if (l instanceof Array) {
        if (l.length > 0)
          if (typeof l[0] == "string")
            for (let p = 0; p < l.length; p++) {
              const m = l[p];
              s.push({
                value: m,
                url: m,
                previewUrl: m,
                index: p
              });
            }
          else
            for (let p = 0; p < l.length; p++) {
              const m = l[p];
              s.push({
                value: m.url,
                ...m,
                index: p
              });
            }
      } else
        s.push({
          value: l.url,
          ...l,
          index: 0
        });
      return a(s);
    }
    return watch(
      () => o.value,
      async (l) => {
        e.urls || (i2.value = await g(l));
      },
      {
        immediate: true
      }
    ), watch(
      () => e.urls,
      async (l) => {
        l && (i2.value = await C(l));
      },
      {
        immediate: true
      }
    ), { imageListRef: i2, ui: n, errorBinding: r };
  }
});
var he = (e, t) => {
  const n = e.__vccOpts || e;
  for (const [i2, r] of t)
    n[i2] = r;
  return n;
};
var ve = { class: "fs-image-format" };
var be = { class: "fs-image-slot" };
var Ce = { class: "fs-image-slot" };
var we = ["src"];
function Ee(e, t, n, i2, r, o) {
  const a = resolveComponent("fs-loading");
  return openBlock(), createElementBlock("div", ve, [
    (openBlock(), createBlock(resolveDynamicComponent(e.ui.imageGroup.name), normalizeProps(guardReactiveProps(e.wrapper)), {
      default: withCtx(() => [
        (openBlock(true), createElementBlock(Fragment, null, renderList(e.imageListRef, (f) => (openBlock(), createBlock(resolveDynamicComponent(e.ui.image.name), mergeProps({
          key: f.src,
          class: "fs-image-item",
          ref_for: true
        }, f), {
          placeholder: withCtx(() => [
            createBaseVNode("div", be, [
              createVNode(a, mergeProps({
                loading: true,
                ref_for: true
              }, e.errorBinding), null, 16)
            ])
          ]),
          error: withCtx(() => [
            createBaseVNode("div", Ce, [
              createBaseVNode("img", mergeProps({
                src: e.error,
                ref_for: true
              }, e.errorBinding), null, 16, we)
            ])
          ]),
          _: 2
        }, 1040))), 128))
      ]),
      _: 1
    }, 16))
  ]);
}
var Te = he(ge, [["render", Ee]]);
var Q = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: Te
}, Symbol.toStringTag, { value: "Module" }));
var _e = (e) => async (t, n) => {
  const i2 = await e(t.fullField, true);
  if (i2 && i2.hasUploading())
    throw new Error("还有未上传完成的文件");
  return true;
};
var F = () => xl(({ getComponentRef: e }) => _e(e));
var ht = (e, t) => (e == null && (e = []), e.push({
  //@ts-ignore
  validator: F(),
  message: t || "还有文件正在上传，请稍候"
  // trigger: "blur" // <-------注意使用blur事件，否则会闪现
}), e);
function xe() {
  const {
    t: e
  } = ot(), t = i.get();
  return {
    "image-uploader": {
      form: {
        component: {
          name: "fs-file-uploader",
          listType: t.upload.typeImageCard,
          accept: ".png,.jpeg,.jpg,.ico,.bmp,.gif,.webp,.svg"
        },
        [t.formItem.rules]: [{
          validator: F(),
          message: e("fs.extends.fileUploader.hasUploading"),
          trigger: "blur"
        }]
      },
      column: {
        component: {
          name: "fs-images-format",
          style: "width:30px",
          previewTeleported: true,
          // @ts-ignore
          previewMask: () => createVNode("div", {
            class: "ant-mask-info"
          }, [createVNode(resolveComponent("fs-icon"), {
            icon: t.icons.eye
          }, null)])
        }
      },
      viewForm: {
        component: {
          height: 100,
          width: 100
        }
      }
    },
    "avatar-uploader": {
      form: {
        [t.formItem.rules]: [{
          validator: F(),
          message: e("fs.extends.fileUploader.hasUploading"),
          trigger: "blur"
        }],
        component: {
          name: "fs-file-uploader",
          limit: 1,
          listType: t.upload.typeImageCard,
          accept: ".png,.jpeg,.jpg,.ico,.bmp,.gif,.webp,.svg"
        }
      },
      column: {
        align: "center",
        component: {
          name: "fs-images-format",
          style: "width:30px",
          previewTeleported: true
        }
      },
      viewForm: {
        component: {
          height: 100,
          width: 100
        }
      },
      valueResolve({
        row: n,
        key: i2
      }) {
        const r = n[i2];
        r != null && r instanceof Array && (r.length >= 0 ? n[i2] = r[0].url : n[i2] = null);
      }
    },
    "file-uploader": {
      form: {
        component: {
          name: "fs-file-uploader",
          listType: "text"
        },
        [t.formItem.rules]: [{
          validator: F(),
          message: e("fs.extends.fileUploader.hasUploading"),
          trigger: "blur"
        }]
      },
      column: {
        component: {
          name: "fs-files-format"
        }
      }
    },
    "cropper-uploader": {
      form: {
        component: {
          name: "fs-cropper-uploader",
          accept: ".png,.jpeg,.jpg,.ico,.bmp,.gif,.svg,.webp",
          cropper: {
            aspectRatio: 1,
            autoCropArea: 1,
            viewMode: 0
          }
        },
        [t.formItem.rules]: [{
          validator: F(),
          message: e("fs.extends.fileUploader.hasUploading"),
          trigger: "blur"
        }]
      },
      column: {
        align: "center",
        component: {
          name: "fs-images-format",
          style: "width:30px",
          previewTeleported: true
        }
      },
      viewForm: {
        component: {
          height: 100,
          width: 100
        }
      }
    }
  };
}
var ke = {
  defaultType: "cos",
  cos: {
    // 腾讯云 cos 的配置
    domain: "https://d2p-demo-1251260344.cos.ap-guangzhou.myqcloud.com",
    bucket: "d2p-demo-1251260344",
    region: "",
    secretId: "",
    secretKey: "",
    async getAuthorization(e) {
      throw new Error("请配置config.cos.getAuthorization 或 uploader.getAuthorization");
    }
  },
  alioss: {
    domain: "https://d2p-demo.oss-cn-shenzhen.aliyuncs.com",
    bucket: "d2p-demo",
    region: "oss-cn-shenzhen",
    accessKeyId: "",
    accessKeySecret: "",
    getAuthorization(e) {
      return new Promise((t, n) => {
        n(new Error("请实现config.alioss.getAuthorization，返回Promise获取临时授权token"));
      });
    },
    keepName: false,
    sdkOpts: {
      // sdk配置
      // secure: false // 默认为非https上传,为了安全，你可以设置为true
    }
  },
  qiniu: {
    bucket: "d2p-demo",
    async getToken(e) {
      throw new Error("请实现config.qiniu.getToken方法，返回Promise获取七牛的授权token{token:xxx,expires:xxx}");
    },
    domain: "http://pzrsldiu3.bkt.clouddn.com"
  },
  s3: {
    bucket: "fast-crud",
    sdkOpts: {
      region: "us-east-1",
      forcePathStyle: true,
      endpoint: "https://play.min.io",
      credentials: {
        accessKeyId: "",
        secretAccessKey: ""
        //访问密码
      }
    }
  },
  form: {
    successHandle(e) {
      return e;
    },
    action: void 0,
    name: "file",
    headers: {},
    data: {}
    // async uploadRequest({ file, action }) {
    //   自定义文件上传请求
    //   return await axios.request();
    // }
  },
  async buildKey(e) {
    const { fileName: t } = e, n = /* @__PURE__ */ new Date(), i2 = e.fileType ?? "file", r = e.keepName ?? false;
    let o = "";
    return r ? o = "/" + t : t.lastIndexOf(".") >= 0 && (o = t.substring(t.lastIndexOf("."))), i2 + "/" + n.getFullYear() + "/" + (n.getMonth() + 1) + "/" + n.getDate() + "/" + Math.floor(Math.random() * 1e14) + o;
  }
};
var P = cloneDeep_default(ke);
function Se(e, t) {
  merge_default(P, t);
}
var vt2 = F;
var Ae = {
  install(e, t) {
    const n = xe(), { addTypes: i2 } = lp();
    i2(n), Se(e, t);
  }
};
async function Fe(e) {
  let t = null;
  return e === "alioss" ? t = await import("./uploader-alioss-25319fb1-WC23CSMF.js") : e === "cos" ? t = await import("./uploader-cos-b55311de-IK7L2HYT.js") : e === "form" ? t = await import("./uploader-form-0f9c1e02-TTJAJB5A.js") : e === "qiniu" ? t = await import("./uploader-qiniu-dcdce4b8-UQ4ZOIG4.js") : e === "s3" ? t = await import("./uploader-s3-5b227774-LL6APY7I.js") : console.error(`未找到${e}的上传实现`), t;
}
async function bt(e, t, n) {
  return n.buildKey({
    fileName: t,
    file: e,
    ...n
  });
}
function Ct() {
  function e() {
    const i2 = P;
    return i2 == null ? void 0 : i2.defaultType;
  }
  function t(i2) {
    i2 == null && (i2 = e());
    const r = P, o = P[i2];
    return o.buildKey == null && (o.buildKey = r.buildKey), o;
  }
  async function n(i2) {
    return await Fe(i2 || e());
  }
  return {
    getConfig: t,
    getDefaultType: e,
    getUploaderImpl: n
  };
}
var Ve = Object.assign({ "./components/fs-cropper-uploader.vue": () => import("./fs-cropper-uploader-576c7bbb-EG567HMK.js"), "./components/fs-cropper.vue": () => import("./fs-cropper-c1cf1841-657YII2Q.js"), "./components/fs-file-uploader.vue": () => import("./fs-file-uploader-ae4f64cb-XM4PBYY4.js"), "./components/fs-files-format.vue": () => import("./fs-files-format-eef409c4-DJENUKJX.js"), "./components/fs-images-format.vue": () => Promise.resolve().then(() => Q), "./components/fs-uploader.vue": () => import("./fs-uploader-a3e4cc9f-G4KTI4T3.js") });
var Ne = Object.assign({ "./components/fs-images-format.vue": Q });
var { registerMergeColumnPlugin: Ue } = Za();
Ue({
  name: "uploader-merge-plugin",
  order: 5,
  handle: (e = {}, t = {}) => {
    if (typeof e.type == "string" && e.type.endsWith("uploader")) {
      const n = e.buildUrl, i2 = e.buildUrls;
      merge_default(e, {
        form: {
          component: {
            buildUrl: n,
            buildUrls: i2
          }
        },
        column: {
          component: {
            buildUrl: n,
            buildUrls: i2
          }
        }
      });
    }
    return e;
  }
});
var Oe = {
  install(e) {
    vt.vite.installAsyncComponents(e, Ve, ["FsImagesFormat"], null, null), vt.vite.installSyncComponents(e, Ne, null, null, null);
  }
};
var wt = {
  install(e, t) {
    e.use(Ae, t), e.use(Oe);
  }
};
function je() {
  return {
    "editor-wang": {
      form: { component: { name: "fs-editor-wang" } }
    },
    "editor-wang5": {
      form: { component: { name: "fs-editor-wang5", style: { zIndex: 800 }, toolbarAttrs: { style: { zIndex: 800 } } } }
    },
    "editor-code": {
      form: { component: { name: "fs-editor-code" }, col: { span: 24 } }
    }
  };
}
var Me = {
  wangEditor: {},
  wangEditor5: {
    editorConfig: {},
    toolbarConfig: {}
  }
};
function Ie(e, t) {
  e.config.globalProperties.$fs_editor_config = merge_default(Me, t);
}
var Le = {
  install(e, t) {
    const n = je(), { addTypes: i2 } = lp();
    i2(n), Ie(e, t);
  }
};
async function Pe() {
  return await import("./js-yaml-GB3GHL7H.js");
}
async function Et() {
  await import("./yaml.contribution-SDJFZYU2.js");
}
async function Tt() {
  await import("./monaco.contribution-RNW5VDQK.js");
}
async function _t() {
  await import("./javascript.contribution-5FSON6KP.js");
}
async function xt() {
  return await import("./monaco-yaml-2X7QSZGH.js");
}
var $e = {
  validator: async (e, t) => {
    if (t)
      try {
        JSON.parse(t);
      } catch (n) {
        throw console.error(n), new Error("json格式错误:" + n.message);
      }
  },
  message: "json格式错误"
};
var Be = {
  validator: async (e, t) => {
    if (t)
      try {
        const n = await Pe();
        n.load(t, { schema: n.JSON_SCHEMA });
      } catch (n) {
        throw console.error(n), new Error("yaml格式错误:" + n.message);
      }
  },
  message: "yaml格式错误"
};
var kt = {
  jsonRule: $e,
  yamlRule: Be
};
var ee = [];
function St(e, t) {
  ee.push({
    worker: t,
    match: (n) => {
      if (typeof e == "string")
        return e === n;
      for (const i2 of e)
        if (i2 === "*" || i2 === n)
          return true;
      return false;
    }
  });
}
function At() {
  window.MonacoEnvironment || (window.MonacoEnvironment = {
    //@ts-ignore
    getWorker(e, t) {
      for (const n of ee)
        if (n.match(t))
          return new n.worker();
    }
  });
}
var Re = Object.assign({ "./components/fs-editor-code/index.vue": () => import("./index-0c021a52-MNRKFB43.js"), "./components/fs-editor-wang/index.vue": () => import("./index-c6de1b82-IIBFN64O.js"), "./components/fs-editor-wang5/index.vue": () => import("./index-25a29aef-O7RSMNED.js") });
var ze = {
  install(e) {
    vt.vite.installAsyncComponents(e, Re, null, /^.*\/([^\/]+)\/.*.vue$/, null);
  }
};
var Ft = {
  install(e, t) {
    e.use(Le, t), e.use(ze);
  }
};
function He() {
  return {
    json: {
      form: {
        component: {
          name: "fs-json-editor"
        }
      }
    }
  };
}
var qe = {
  install(e) {
    const t = He(), { addTypes: n } = lp();
    n(t);
  }
};
var De = Object.assign({ "./components/fs-json-editor.vue": () => import("./fs-json-editor-1bbe0b24-BCWZC343.js") });
var Ke = {
  install(e) {
    vt.vite.installAsyncComponents(e, De, [], null, null);
  }
};
var Vt = {
  install(e) {
    e.use(qe), e.use(Ke);
  }
};
function Je() {
  return {
    copyable: {
      column: {
        component: {
          name: "fs-copyable",
          vModel: "modelValue"
        }
      }
    }
  };
}
var Ye = {
  install(e) {
    const t = Je(), { addTypes: n } = lp();
    n(t);
  }
};
var We = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
function Ge(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
function Xe(e, t, n) {
  return n = {
    path: t,
    exports: {},
    require: function(i2, r) {
      return Ze(i2, r ?? n.path);
    }
  }, e(n, n.exports), n.exports;
}
function Ze() {
  throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs");
}
var Qe = Xe(function(e, t) {
  (function(i2, r) {
    e.exports = r();
  })(We, function() {
    return (
      /******/
      function(n) {
        var i2 = {};
        function r(o) {
          if (i2[o])
            return i2[o].exports;
          var a = i2[o] = {
            /******/
            i: o,
            /******/
            l: false,
            /******/
            exports: {}
            /******/
          };
          return n[o].call(a.exports, a, a.exports, r), a.l = true, a.exports;
        }
        return r.m = n, r.c = i2, r.d = function(o, a, f) {
          r.o(o, a) || Object.defineProperty(o, a, { enumerable: true, get: f });
        }, r.r = function(o) {
          typeof Symbol < "u" && Symbol.toStringTag && Object.defineProperty(o, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(o, "__esModule", { value: true });
        }, r.t = function(o, a) {
          if (a & 1 && (o = r(o)), a & 8 || a & 4 && typeof o == "object" && o && o.__esModule)
            return o;
          var f = /* @__PURE__ */ Object.create(null);
          if (r.r(f), Object.defineProperty(f, "default", { enumerable: true, value: o }), a & 2 && typeof o != "string")
            for (var g in o)
              r.d(f, g, (function(C) {
                return o[C];
              }).bind(null, g));
          return f;
        }, r.n = function(o) {
          var a = o && o.__esModule ? (
            /******/
            function() {
              return o.default;
            }
          ) : (
            /******/
            function() {
              return o;
            }
          );
          return r.d(a, "a", a), a;
        }, r.o = function(o, a) {
          return Object.prototype.hasOwnProperty.call(o, a);
        }, r.p = "", r(r.s = 6);
      }([
        /* 0 */
        /***/
        function(n, i2) {
          function r(o) {
            var a;
            if (o.nodeName === "SELECT")
              o.focus(), a = o.value;
            else if (o.nodeName === "INPUT" || o.nodeName === "TEXTAREA") {
              var f = o.hasAttribute("readonly");
              f || o.setAttribute("readonly", ""), o.select(), o.setSelectionRange(0, o.value.length), f || o.removeAttribute("readonly"), a = o.value;
            } else {
              o.hasAttribute("contenteditable") && o.focus();
              var g = window.getSelection(), C = document.createRange();
              C.selectNodeContents(o), g.removeAllRanges(), g.addRange(C), a = g.toString();
            }
            return a;
          }
          n.exports = r;
        },
        /* 1 */
        /***/
        function(n, i2) {
          function r() {
          }
          r.prototype = {
            on: function(o, a, f) {
              var g = this.e || (this.e = {});
              return (g[o] || (g[o] = [])).push({
                fn: a,
                ctx: f
              }), this;
            },
            once: function(o, a, f) {
              var g = this;
              function C() {
                g.off(o, C), a.apply(f, arguments);
              }
              return C._ = a, this.on(o, C, f);
            },
            emit: function(o) {
              var a = [].slice.call(arguments, 1), f = ((this.e || (this.e = {}))[o] || []).slice(), g = 0, C = f.length;
              for (g; g < C; g++)
                f[g].fn.apply(f[g].ctx, a);
              return this;
            },
            off: function(o, a) {
              var f = this.e || (this.e = {}), g = f[o], C = [];
              if (g && a)
                for (var l = 0, s = g.length; l < s; l++)
                  g[l].fn !== a && g[l].fn._ !== a && C.push(g[l]);
              return C.length ? f[o] = C : delete f[o], this;
            }
          }, n.exports = r, n.exports.TinyEmitter = r;
        },
        /* 2 */
        /***/
        function(n, i2, r) {
          var o = r(3), a = r(4);
          function f(s, p, m) {
            if (!s && !p && !m)
              throw new Error("Missing required arguments");
            if (!o.string(p))
              throw new TypeError("Second argument must be a String");
            if (!o.fn(m))
              throw new TypeError("Third argument must be a Function");
            if (o.node(s))
              return g(s, p, m);
            if (o.nodeList(s))
              return C(s, p, m);
            if (o.string(s))
              return l(s, p, m);
            throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList");
          }
          function g(s, p, m) {
            return s.addEventListener(p, m), {
              destroy: function() {
                s.removeEventListener(p, m);
              }
            };
          }
          function C(s, p, m) {
            return Array.prototype.forEach.call(s, function(v) {
              v.addEventListener(p, m);
            }), {
              destroy: function() {
                Array.prototype.forEach.call(s, function(v) {
                  v.removeEventListener(p, m);
                });
              }
            };
          }
          function l(s, p, m) {
            return a(document.body, s, p, m);
          }
          n.exports = f;
        },
        /* 3 */
        /***/
        function(n, i2) {
          i2.node = function(r) {
            return r !== void 0 && r instanceof HTMLElement && r.nodeType === 1;
          }, i2.nodeList = function(r) {
            var o = Object.prototype.toString.call(r);
            return r !== void 0 && (o === "[object NodeList]" || o === "[object HTMLCollection]") && "length" in r && (r.length === 0 || i2.node(r[0]));
          }, i2.string = function(r) {
            return typeof r == "string" || r instanceof String;
          }, i2.fn = function(r) {
            var o = Object.prototype.toString.call(r);
            return o === "[object Function]";
          };
        },
        /* 4 */
        /***/
        function(n, i2, r) {
          var o = r(5);
          function a(C, l, s, p, m) {
            var v = g.apply(this, arguments);
            return C.addEventListener(s, v, m), {
              destroy: function() {
                C.removeEventListener(s, v, m);
              }
            };
          }
          function f(C, l, s, p, m) {
            return typeof C.addEventListener == "function" ? a.apply(null, arguments) : typeof s == "function" ? a.bind(null, document).apply(null, arguments) : (typeof C == "string" && (C = document.querySelectorAll(C)), Array.prototype.map.call(C, function(v) {
              return a(v, l, s, p, m);
            }));
          }
          function g(C, l, s, p) {
            return function(m) {
              m.delegateTarget = o(m.target, l), m.delegateTarget && p.call(C, m);
            };
          }
          n.exports = f;
        },
        /* 5 */
        /***/
        function(n, i2) {
          var r = 9;
          if (typeof Element < "u" && !Element.prototype.matches) {
            var o = Element.prototype;
            o.matches = o.matchesSelector || o.mozMatchesSelector || o.msMatchesSelector || o.oMatchesSelector || o.webkitMatchesSelector;
          }
          function a(f, g) {
            for (; f && f.nodeType !== r; ) {
              if (typeof f.matches == "function" && f.matches(g))
                return f;
              f = f.parentNode;
            }
          }
          n.exports = a;
        },
        /* 6 */
        /***/
        function(n, i2, r) {
          r.r(i2);
          var o = r(0), a = r.n(o), f = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(y) {
            return typeof y;
          } : function(y) {
            return y && typeof Symbol == "function" && y.constructor === Symbol && y !== Symbol.prototype ? "symbol" : typeof y;
          }, g = /* @__PURE__ */ function() {
            function y(u, c) {
              for (var d = 0; d < c.length; d++) {
                var w = c[d];
                w.enumerable = w.enumerable || false, w.configurable = true, "value" in w && (w.writable = true), Object.defineProperty(u, w.key, w);
              }
            }
            return function(u, c, d) {
              return c && y(u.prototype, c), d && y(u, d), u;
            };
          }();
          function C(y, u) {
            if (!(y instanceof u))
              throw new TypeError("Cannot call a class as a function");
          }
          var l = function() {
            function y(u) {
              C(this, y), this.resolveOptions(u), this.initSelection();
            }
            return g(y, [{
              key: "resolveOptions",
              value: function() {
                var c = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
                this.action = c.action, this.container = c.container, this.emitter = c.emitter, this.target = c.target, this.text = c.text, this.trigger = c.trigger, this.selectedText = "";
              }
              /**
               * Decides which selection strategy is going to be applied based
               * on the existence of `text` and `target` properties.
               */
            }, {
              key: "initSelection",
              value: function() {
                this.text ? this.selectFake() : this.target && this.selectTarget();
              }
              /**
               * Creates a fake textarea element, sets its value from `text` property,
               * and makes a selection on it.
               */
            }, {
              key: "selectFake",
              value: function() {
                var c = this, d = document.documentElement.getAttribute("dir") == "rtl";
                this.removeFake(), this.fakeHandlerCallback = function() {
                  return c.removeFake();
                }, this.fakeHandler = this.container.addEventListener("click", this.fakeHandlerCallback) || true, this.fakeElem = document.createElement("textarea"), this.fakeElem.style.fontSize = "12pt", this.fakeElem.style.border = "0", this.fakeElem.style.padding = "0", this.fakeElem.style.margin = "0", this.fakeElem.style.position = "absolute", this.fakeElem.style[d ? "right" : "left"] = "-9999px";
                var w = window.pageYOffset || document.documentElement.scrollTop;
                this.fakeElem.style.top = w + "px", this.fakeElem.setAttribute("readonly", ""), this.fakeElem.value = this.text, this.container.appendChild(this.fakeElem), this.selectedText = a()(this.fakeElem), this.copyText();
              }
              /**
               * Only removes the fake element after another click event, that way
               * a user can hit `Ctrl+C` to copy because selection still exists.
               */
            }, {
              key: "removeFake",
              value: function() {
                this.fakeHandler && (this.container.removeEventListener("click", this.fakeHandlerCallback), this.fakeHandler = null, this.fakeHandlerCallback = null), this.fakeElem && (this.container.removeChild(this.fakeElem), this.fakeElem = null);
              }
              /**
               * Selects the content from element passed on `target` property.
               */
            }, {
              key: "selectTarget",
              value: function() {
                this.selectedText = a()(this.target), this.copyText();
              }
              /**
               * Executes the copy operation based on the current selection.
               */
            }, {
              key: "copyText",
              value: function() {
                var c = void 0;
                try {
                  c = document.execCommand(this.action);
                } catch {
                  c = false;
                }
                this.handleResult(c);
              }
              /**
               * Fires an event based on the copy operation result.
               * @param {Boolean} succeeded
               */
            }, {
              key: "handleResult",
              value: function(c) {
                this.emitter.emit(c ? "success" : "error", {
                  action: this.action,
                  text: this.selectedText,
                  trigger: this.trigger,
                  clearSelection: this.clearSelection.bind(this)
                });
              }
              /**
               * Moves focus away from `target` and back to the trigger, removes current selection.
               */
            }, {
              key: "clearSelection",
              value: function() {
                this.trigger && this.trigger.focus(), document.activeElement.blur(), window.getSelection().removeAllRanges();
              }
              /**
               * Sets the `action` to be performed which can be either 'copy' or 'cut'.
               * @param {String} action
               */
            }, {
              key: "destroy",
              /**
               * Destroy lifecycle.
               */
              value: function() {
                this.removeFake();
              }
            }, {
              key: "action",
              set: function() {
                var c = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "copy";
                if (this._action = c, this._action !== "copy" && this._action !== "cut")
                  throw new Error('Invalid "action" value, use either "copy" or "cut"');
              },
              get: function() {
                return this._action;
              }
              /**
               * Sets the `target` property using an element
               * that will be have its content copied.
               * @param {Element} target
               */
            }, {
              key: "target",
              set: function(c) {
                if (c !== void 0)
                  if (c && (typeof c > "u" ? "undefined" : f(c)) === "object" && c.nodeType === 1) {
                    if (this.action === "copy" && c.hasAttribute("disabled"))
                      throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');
                    if (this.action === "cut" && (c.hasAttribute("readonly") || c.hasAttribute("disabled")))
                      throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`);
                    this._target = c;
                  } else
                    throw new Error('Invalid "target" value, use a valid Element');
              },
              get: function() {
                return this._target;
              }
            }]), y;
          }(), s = l, p = r(1), m = r.n(p), v = r(2), T = r.n(v), k = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(y) {
            return typeof y;
          } : function(y) {
            return y && typeof Symbol == "function" && y.constructor === Symbol && y !== Symbol.prototype ? "symbol" : typeof y;
          }, S = /* @__PURE__ */ function() {
            function y(u, c) {
              for (var d = 0; d < c.length; d++) {
                var w = c[d];
                w.enumerable = w.enumerable || false, w.configurable = true, "value" in w && (w.writable = true), Object.defineProperty(u, w.key, w);
              }
            }
            return function(u, c, d) {
              return c && y(u.prototype, c), d && y(u, d), u;
            };
          }();
          function U(y, u) {
            if (!(y instanceof u))
              throw new TypeError("Cannot call a class as a function");
          }
          function h(y, u) {
            if (!y)
              throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
            return u && (typeof u == "object" || typeof u == "function") ? u : y;
          }
          function b(y, u) {
            if (typeof u != "function" && u !== null)
              throw new TypeError("Super expression must either be null or a function, not " + typeof u);
            y.prototype = Object.create(u && u.prototype, { constructor: { value: y, enumerable: false, writable: true, configurable: true } }), u && (Object.setPrototypeOf ? Object.setPrototypeOf(y, u) : y.__proto__ = u);
          }
          var E = function(y) {
            b(u, y);
            function u(c, d) {
              U(this, u);
              var w = h(this, (u.__proto__ || Object.getPrototypeOf(u)).call(this));
              return w.resolveOptions(d), w.listenClick(c), w;
            }
            return S(u, [{
              key: "resolveOptions",
              value: function() {
                var d = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
                this.action = typeof d.action == "function" ? d.action : this.defaultAction, this.target = typeof d.target == "function" ? d.target : this.defaultTarget, this.text = typeof d.text == "function" ? d.text : this.defaultText, this.container = k(d.container) === "object" ? d.container : document.body;
              }
              /**
               * Adds a click event listener to the passed trigger.
               * @param {String|HTMLElement|HTMLCollection|NodeList} trigger
               */
            }, {
              key: "listenClick",
              value: function(d) {
                var w = this;
                this.listener = T()(d, "click", function(O) {
                  return w.onClick(O);
                });
              }
              /**
               * Defines a new `ClipboardAction` on each click event.
               * @param {Event} e
               */
            }, {
              key: "onClick",
              value: function(d) {
                var w = d.delegateTarget || d.currentTarget;
                this.clipboardAction && (this.clipboardAction = null), this.clipboardAction = new s({
                  action: this.action(w),
                  target: this.target(w),
                  text: this.text(w),
                  container: this.container,
                  trigger: w,
                  emitter: this
                });
              }
              /**
               * Default `action` lookup function.
               * @param {Element} trigger
               */
            }, {
              key: "defaultAction",
              value: function(d) {
                return _("action", d);
              }
              /**
               * Default `target` lookup function.
               * @param {Element} trigger
               */
            }, {
              key: "defaultTarget",
              value: function(d) {
                var w = _("target", d);
                if (w)
                  return document.querySelector(w);
              }
              /**
               * Returns the support of the given action, or all actions if no action is
               * given.
               * @param {String} [action]
               */
            }, {
              key: "defaultText",
              /**
               * Default `text` lookup function.
               * @param {Element} trigger
               */
              value: function(d) {
                return _("text", d);
              }
              /**
               * Destroy lifecycle.
               */
            }, {
              key: "destroy",
              value: function() {
                this.listener.destroy(), this.clipboardAction && (this.clipboardAction.destroy(), this.clipboardAction = null);
              }
            }], [{
              key: "isSupported",
              value: function() {
                var d = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : ["copy", "cut"], w = typeof d == "string" ? [d] : d, O = !!document.queryCommandSupported;
                return w.forEach(function(ie) {
                  O = O && !!document.queryCommandSupported(ie);
                }), O;
              }
            }]), u;
          }(m.a);
          function _(y, u) {
            var c = "data-clipboard-" + y;
            if (u.hasAttribute(c))
              return u.getAttribute(c);
          }
          i2.default = E;
        }
        /******/
      ]).default
    );
  });
});
var te = Ge(Qe);
var I = {
  autoSetContainer: false,
  appendToBody: true
};
var et = {
  config: (e) => {
    const { autoSetContainer: t, appendToBody: n } = e;
    I.autoSetContainer = t || false, I.appendToBody = n || true;
  },
  install: (e) => {
    e.config.globalProperties.$vclipboard = G, e.directive("clipboard", {
      beforeMount(t, n) {
        if (n.arg === "success")
          t._vClipboard_success = n.value;
        else if (n.arg === "error")
          t._vClipboard_error = n.value;
        else {
          const i2 = new te(t, {
            text: () => n.value,
            action: () => n.arg === "cut" ? "cut" : "copy",
            container: I.autoSetContainer ? t : void 0
          });
          i2.on("success", (r) => {
            const o = t._vClipboard_success;
            o && o(r);
          }), i2.on("error", (r) => {
            const o = t._vClipboard_error;
            o && o(r);
          }), t._vClipboard = i2;
        }
      },
      updated(t, n) {
        n.arg === "success" ? t._vClipboard_success = n.value : n.arg === "error" ? t._vClipboard_error = n.value : (t._vClipboard.text = () => n.value, t._vClipboard.action = () => n.arg === "cut" ? "cut" : "copy");
      },
      unmounted(t, n) {
        n.arg === "success" ? delete t._vClipboard_success : n.arg === "error" ? delete t._vClipboard_error : (t._vClipboard.destroy(), delete t._vClipboard);
      }
    });
  },
  toClipboard: (e, t) => G(e, t)
};
var G = (e, t = "copy") => new Promise((n, i2) => {
  const r = document.createElement("button"), o = new te(r, {
    text: () => e,
    action: () => t
  });
  o.on("success", (a) => {
    o.destroy(), n(a);
  }), o.on("error", (a) => {
    o.destroy(), i2(a);
  }), I.appendToBody && document.body.appendChild(r), r.click(), I.appendToBody && document.body.removeChild(r);
});
var tt = Object.assign({ "./components/fs-copyable.vue": () => import("./fs-copyable-a0b0f7d7-RBTB7I5U.js") });
var nt = {
  install(e) {
    vt.vite.installAsyncComponents(e, tt, [], null, null);
  }
};
var Nt = {
  install(e) {
    e.use(Ye), e.use(nt), e.use(et);
  }
};
function ot2() {
  return {
    "time-humanize": {
      column: {
        component: {
          name: "fs-time-humanize",
          vModel: "modelValue"
        }
      }
    }
  };
}
var rt = {
  install(e) {
    const t = ot2(), { addTypes: n } = lp();
    n(t);
  }
};
var it = Object.assign({ "./components/fs-time-humanize.vue": () => import("./fs-time-humanize-bce0eda2-UDVYW3M6.js") });
var at = {
  install(e) {
    vt.vite.installAsyncComponents(e, it, [], null, null);
  }
};
var Ut = {
  install(e) {
    e.use(rt), e.use(at);
  }
};
async function J() {
  const e = Object.assign({ "./phoneCodeCountries.ts": () => import("./phoneCodeCountries-923a4b31-OMWNUWLU.js") }), { countries: t } = await e["./phoneCodeCountries.ts"]();
  return t;
}
async function ne(e) {
  const t = await J();
  let n = null;
  return e != null && (e.countryCode != null ? n = t.find((i2) => i2.iso2 === e.countryCode) : e.callingCode != null && (n = t.find((i2) => i2.dialCode === e.callingCode))), n != null && (n = {
    callingCode: n.dialCode,
    countryCode: n.iso2
  }), n;
}
var lt = Object.freeze(Object.defineProperty({
  __proto__: null,
  getCountries: J,
  getCountryByValue: ne
}, Symbol.toStringTag, { value: "Module" }));
var st = { class: "fs-phone-input" };
var Ot = defineComponent({
  __name: "fs-phone-input",
  props: {
    select: {},
    input: {},
    modelValue: {},
    onlyCountries: {},
    ignoredCountries: {},
    priorityCountries: {},
    clearable: { type: Boolean },
    filterable: { type: Boolean },
    defaultCountry: { default: "CN" },
    disabled: { type: Boolean },
    readonly: { type: Boolean }
  },
  emits: ["change", "input", "update:modelValue"],
  setup(e, { emit: t }) {
    const { ui: n } = B(), i2 = n.formItem.injectFormItemContext(), r = e, o = t, a = ref(
      r.modelValue || {
        callingCode: void 0,
        // 电话区号
        countryCode: void 0,
        // 国家代码
        phoneNumber: void 0
        // 电话号码
      }
    ), f = vu({
      value: "iso2",
      label: "label"
    }), g = ref([]);
    async function C() {
      g.value = await J();
    }
    C();
    const l = computed(() => {
      const h = g.value;
      let b = [];
      if (r.onlyCountries != null && r.onlyCountries.length > 0)
        for (let E of h)
          r.onlyCountries.find((_) => _.toLowerCase() === E.iso2.toLowerCase()) && b.push(E);
      else {
        const E = r.priorityCountries || [], _ = r.ignoredCountries || [], y = [], u = [];
        for (let c of h)
          E.find((d) => d.toLowerCase() === c.iso2.toLowerCase()) && y.push(c), _.find((d) => d.toLowerCase() === c.iso2.toLowerCase()) || u.push(c);
        b = y.concat(u);
      }
      return b = b.map((E) => ({
        ...E,
        label: E.name + "(" + E.dialCode + ")"
      })), b;
    }), s = computed(() => {
      const h = {
        placeholder: "请选择",
        [n.select.filterable]: true,
        [n.select.clearable]: true,
        [n.select.modelValue]: a.value.countryCode,
        ["onUpdate:" + n.select.modelValue]: T
      };
      return merge_default(h, r.select);
    }), p = computed(() => {
      const h = {
        placeholder: "请输入",
        [n.select.clearable]: true,
        [n.input.modelValue]: a.value.phoneNumber,
        [`onUpdate:${n.input.modelValue}`]: k
      };
      return merge_default(h, r.input);
    });
    async function m(h) {
      a.value = { callingCode: void 0, countryCode: void 0, phoneNumber: void 0 };
      const b = await v(h);
      b != null && (a.value.callingCode = b.callingCode, a.value.countryCode = b.countryCode), h && h.phoneNumber ? a.value.phoneNumber = h.phoneNumber : a.value.phoneNumber = void 0;
    }
    async function v(h) {
      let b = null;
      return h != null && (h.countryCode != null ? b = l.value.find((E) => E.iso2 === h.countryCode) : h.callingCode != null && (b = l.value.find((E) => E.dialCode === h.callingCode))), b != null && (b = {
        callingCode: b.dialCode,
        countryCode: b.iso2
      }), b == null && (b = await ne({ countryCode: r.defaultCountry })), b;
    }
    async function T(h) {
      await U(h);
      let b = S();
      o("update:modelValue", b), o("input", b), o("change", b), await i2.onChange(), await i2.onBlur();
    }
    async function k(h) {
      if (a.value.phoneNumber = h, a.value.callingCode == null && a.value.countryCode == null) {
        a.value.countryCode = r.defaultCountry;
        const E = await v(a.value);
        E && (a.value.callingCode = E.callingCode);
      }
      let b = S();
      o("update:modelValue", b), o("input", b), o("change", b), await i2.onChange(), await i2.onBlur();
    }
    function S() {
      return {
        countryCode: a.value.countryCode,
        callingCode: a.value.callingCode,
        phoneNumber: a.value.phoneNumber
      };
    }
    async function U(h) {
      h || (a.value.callingCode = void 0), a.value.countryCode = h;
      let b = await v(a.value);
      b && (a.value.callingCode = b.callingCode);
    }
    return watch(
      () => r.modelValue,
      async (h, b) => {
        await m(h), o("change", a.value);
      },
      {
        immediate: true
      }
    ), (h, b) => {
      const E = resolveComponent("fs-dict-select");
      return openBlock(), createElementBlock("div", st, [
        createVNode(E, mergeProps({
          disabled: h.disabled,
          readonly: h.readonly,
          filterable: h.filterable,
          clearable: h.clearable,
          options: l.value,
          dict: unref(f),
          "show-search": true,
          "allow-clear": true
        }, s.value), null, 16, ["disabled", "readonly", "filterable", "clearable", "options", "dict"]),
        (openBlock(), createBlock(resolveDynamicComponent(unref(n).input.name), mergeProps({
          type: "text",
          clearable: h.clearable,
          disabled: h.disabled,
          readonly: h.readonly,
          "allow-clear": true
        }, p.value), null, 16, ["clearable", "disabled", "readonly"]))
      ]);
    };
  }
});
async function oe({ phoneNumber: e, countryCode: t }) {
  const i2 = (await import("./index-1f520acb-4IRZRTP6.js")).parsePhoneNumberFromString, r = e && t ? i2(e, t) : null;
  return {
    phoneNumber: e || null,
    countryCode: t,
    isValid: false,
    ...r ? {
      formattedNumber: r.number,
      nationalNumber: r.nationalNumber,
      isValid: r.isValid(),
      type: r.getType(),
      formatInternational: r.formatInternational(),
      formatNational: r.formatNational(),
      uri: r.getURI(),
      e164: r.format("E.164")
    } : null
  };
}
async function re(e) {
  const t = Object.assign({ "./utils.ts": () => Promise.resolve().then(() => lt) }), { getCountryByValue: n } = await t["./utils.ts"]();
  return n(e);
}
async function jt(e, t) {
  if (!t || t.phoneNumber == null || t.phoneNumber === "")
    return true;
  if (!t.countryCode && t.callingCode) {
    const i2 = await re(t);
    i2 && (t.countryCode = i2.countryCode);
  }
  const n = await oe({
    phoneNumber: t.phoneNumber,
    countryCode: t.countryCode
  });
  if (!n.isValid)
    throw console.warn("parse:", n), new Error("电话号码错误");
  return true;
}
async function ut(e, t, n) {
  if (!t || t.phoneNumber == null || t.phoneNumber === "" || t.countryCode == null || t.countryCode === "")
    return true;
  if (!t.countryCode && t.callingCode) {
    const r = await re(t);
    r && (t.countryCode = r.countryCode);
  }
  const i2 = await oe({
    phoneNumber: t.phoneNumber,
    countryCode: t.countryCode
  });
  if (!i2.isValid || i2.type !== "MOBILE" && i2.type !== "FIXED_LINE_OR_MOBILE")
    throw console.warn("parse:", i2), new Error("手机号错误");
  return true;
}
async function Mt(e, t) {
  const n = t.countryCode == null || t.countryCode === "", i2 = t.callingCode == null || t.callingCode === "", r = t.phoneNumber == null || t.phoneNumber === "";
  if (!t || r || n && i2)
    throw new Error("该项必填");
  return true;
}
function ct() {
  return {
    phone: {
      column: {
        cellRender({
          value: e
        }) {
          return !e || !e.phoneNumber ? "" : createVNode("div", null, [createTextVNode("("), e.callingCode || "86", createTextVNode(")"), e.phoneNumber]);
        }
      },
      form: {
        component: {
          name: "fs-phone-input",
          vModel: "modelValue"
        },
        rules: [{
          validator: ut,
          message: "请填写正确的手机号码"
        }]
      }
    }
  };
}
var dt = {
  install(e) {
    const t = ct(), { addTypes: n } = lp();
    n(t);
  }
};
var ft = Object.assign({ "./components/fs-phone-input/fs-phone-input.vue": () => import("./fs-phone-input-0784a657-5UTGYD27.js") });
var pt = {
  install(e) {
    vt.vite.installAsyncComponents(e, ft, [], null, null);
  }
};
var It = {
  install(e) {
    e.use(dt), e.use(pt);
  }
};

export {
  he,
  _e,
  F,
  ht,
  vt2 as vt,
  Fe,
  bt,
  Ct,
  wt,
  Me,
  Et,
  Tt,
  _t,
  xt,
  kt,
  St,
  At,
  Ft,
  Vt,
  Nt,
  Ut,
  Ot,
  oe,
  jt,
  ut,
  Mt,
  It
};
/*! Bundled license information:

@fast-crud/fast-extends/dist/index-a0aeea35.mjs:
  (*!
    * @soerenmartius/vue3-clipboard v0.1.2
    * (c) 2021 Soeren Martius
    * @license MIT
    *)
  (*!
   * clipboard.js v2.0.6
   * https://clipboardjs.com/
   * 
   * Licensed MIT © Zeno Rocha
   *)
*/
//# sourceMappingURL=chunk-CP75YXJV.js.map
