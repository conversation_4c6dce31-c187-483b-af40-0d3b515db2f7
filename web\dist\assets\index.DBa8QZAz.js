import{g as q}from"./index.es.DmevZXPX.js";import{aa as G,a3 as H,R as C}from"./index.GuQX7xXE.js";import{_ as T}from"./index.vue_vue_type_script_setup_true_name_svgIcon_lang.BRW_FJF_.js";import{m as J,a as K,l as W}from"./api.D3e6WFkQ.js";import{d as X,a as _,m as Y,b as i,y as Z,c as w,e as c,f as a,l as M,u as g,$ as ee,k as b,w as n,t as y,A as v,g as k,F as te,E as ae,x as ne}from"./vue.zNq9Glab.js";import{_ as oe}from"./_plugin-vue_export-helper.DlAUqK2U.js";const le={class:"menu-tree-com"},se={class:"mtc-head"},ie={key:0,class:"text-center font-black font-normal"},re={key:1,class:"text-center font-black text-red-700 font-normal"},ce={class:"mtc-tags"},de=X({__name:"index",props:{treeData:{default:()=>[]}},emits:["treeClick","deleteDept","updateDept"],setup(ue,{expose:E,emit:V}){const B=q(ae),$={children:"children",label:"name",icon:"icon",isLeaf:(t,e)=>!e.data.is_catalog},x=_(),D=V;let u=_(""),f=_(!1),l=_({}),z=_(null);Y(u,t=>{x.value.filter(t)});const R=(t,e)=>t?ne(e).name.indexOf(t)!==-1:!0,U=(t,e)=>{t.level!==0&&W({parent:t.data.id}).then(r=>{e(r.data)})},F=(t,e)=>{l.value=t,z.value=e,D("treeClick",t)},L=t=>{if(t==="update"){if(!l.value.id){C("请选择菜单！");return}D("updateDept",t,l.value)}else D("updateDept",t)},S=()=>{if(!l.value.id){C("请选择菜单！");return}D("deleteDept",l.value.id,()=>{l.value={}})},N=async t=>{var s;if(!l.value.id){C("请选择菜单！");return}if(f.value)return;const e=((s=z.value)==null?void 0:s.parent.childNodes)||[],r=e.findIndex(m=>m.data.id===l.value.id),h=e.find(m=>m.data.id===l.value.id);if(t==="up"){if(r===0)return;e.splice(r-1,0,h),e.splice(r+1,1),f.value=!0,await J({menu_id:l.value.id}),f.value=!1}t==="down"&&(e.splice(r+2,0,h),e.splice(r,1),f.value=!0,await K({menu_id:l.value.id}),f.value=!1)};return E({treeRef:x}),(t,e)=>{const r=i("el-input"),h=i("Menu"),s=i("el-icon"),m=i("QuestionFilled"),d=i("el-tooltip"),I=i("Plus"),P=i("Edit"),Q=i("Top"),A=i("Bottom"),O=i("Delete"),p=Z("auth");return c(),w(te,null,[a(r,{modelValue:g(u),"onUpdate:modelValue":e[0]||(e[0]=o=>ee(u)?u.value=o:u=o),"prefix-icon":g(G),placeholder:"请输入菜单名称"},null,8,["modelValue","prefix-icon"]),M("div",le,[M("div",se,[a(s,{size:"16",color:"#606266",class:"mtc-head-icon"},{default:n(()=>[a(h)]),_:1}),e[6]||(e[6]=b(" 菜单列表 ")),a(d,{effect:"dark",placement:"right",content:"1.红色菜单代表状态禁用; 2.添加菜单，如果是目录，组件地址为空即可; 3.添加根节点菜单，父级ID为空即可; 4.支持拖拽菜单;"},{default:n(()=>[a(s,{size:"16",color:"var(--el-color-primary)",class:"mtc-tooltip"},{default:n(()=>[a(m)]),_:1})]),_:1})]),a(g(H),{ref_key:"treeRef",ref:x,data:t.treeData,props:$,"filter-node-method":R,load:U,lazy:"",indent:45,onNodeClick:F,"highlight-current":""},{default:n(({node:o,data:j})=>[a(g(B),{node:o,showLabelLine:!1,indent:32},{default:n(()=>[j.status?(c(),w("span",ie,[a(T,{name:o.data.icon,color:"var(--el-color-primary)"},null,8,["name"]),b("  "+y(o.label),1)])):(c(),w("span",re,[a(T,{name:o.data.icon},null,8,["name"]),b(" "+y(o.label),1)]))]),_:2},1032,["node"])]),_:1},8,["data"]),M("div",ce,[a(d,{effect:"dark",content:"新增"},{default:n(()=>[v((c(),k(s,{size:"16",onClick:e[1]||(e[1]=o=>L("create")),class:"mtc-tags-icon"},{default:n(()=>[a(I)]),_:1})),[[p,"menu:Create"]])]),_:1}),a(d,{effect:"dark",content:"编辑"},{default:n(()=>[v((c(),k(s,{size:"16",onClick:e[2]||(e[2]=o=>L("update")),class:"mtc-tags-icon"},{default:n(()=>[a(P)]),_:1})),[[p,"menu:Update"]])]),_:1}),a(d,{effect:"dark",content:"上移"},{default:n(()=>[v((c(),k(s,{size:"16",onClick:e[3]||(e[3]=o=>N("up")),class:"mtc-tags-icon"},{default:n(()=>[a(Q)]),_:1})),[[p,"menu:MoveUp"]])]),_:1}),a(d,{effect:"dark",content:"下移"},{default:n(()=>[v((c(),k(s,{size:"16",onClick:e[4]||(e[4]=o=>N("down")),class:"mtc-tags-icon"},{default:n(()=>[a(A)]),_:1})),[[p,"menu:MoveDown"]])]),_:1}),a(d,{effect:"dark",content:"删除"},{default:n(()=>[v((c(),k(s,{size:"16",onClick:e[5]||(e[5]=o=>S()),class:"mtc-tags-icon"},{default:n(()=>[a(O)]),_:1})),[[p,"menu:Delete"]])]),_:1})])])],64)}}}),De=oe(de,[["__scopeId","data-v-3648c119"]]);export{De as default};
