import{d as L,M as k,Q as F,R,r as $,p as E,o as M,X as j,b as m,c as i,h as d,e as r,f as b,u as p,w as f,T as z,F as D,j as G,g as h,t as _,Z as O,k as Q}from"./vue.zNq9Glab.js";import{I as U,u as X,O as Z,L as y}from"./index.GuQX7xXE.js";import{_ as q}from"./_plugin-vue_export-helper.DlAUqK2U.js";const A={key:0,class:"layout-navbars-breadcrumb"},H={key:0,class:"layout-navbars-breadcrumb-span"},J={key:1},K={key:2},P=["onClick"],W=L({name:"layoutBreadcrumb"}),Y=L({...W,setup(ee){const B=U(),I=X(),{themeConfig:o}=k(I),{routesList:g}=k(B),c=F(),C=R(),e=$({breadcrumbList:[],routeSplit:[],routeSplitFirst:"",routeSplitIndex:1}),w=E(()=>{u(c.path);const{layout:t,isBreadcrumb:a}=o.value;return t==="classic"||t==="transverse"?!1:!!a}),N=t=>{const{redirect:a,path:n}=t;a?C.push(a):C.push(n)},T=()=>{o.value.isCollapse=!o.value.isCollapse,V()},V=()=>{y.remove("themeConfig"),y.set("themeConfig",o.value)},v=t=>{t.forEach(a=>{e.routeSplit.forEach((n,S,l)=>{e.routeSplitFirst===a.path&&(e.routeSplitFirst+=`/${l[e.routeSplitIndex]}`,e.breadcrumbList.push(a),e.routeSplitIndex++,a.children&&v(a.children))})})},u=t=>{if(!o.value.isBreadcrumb)return!1;e.breadcrumbList=[g.value[0]],e.routeSplit=t.split("/"),e.routeSplit.shift(),e.routeSplitFirst=`/${e.routeSplit[0]}`,e.routeSplitIndex=1,v(g.value),(c.name==="home"||c.name==="notFound"&&e.breadcrumbList.length>1)&&e.breadcrumbList.shift(),e.breadcrumbList.length>0&&(e.breadcrumbList[e.breadcrumbList.length-1].meta.tagsViewName=Z.setTagsViewNameI18n(c))};return M(()=>{u(c.path)}),j(t=>{u(t.path)}),(t,a)=>{const n=m("SvgIcon"),S=m("el-breadcrumb-item"),l=m("el-breadcrumb");return w.value?(r(),i("div",A,[b(n,{class:"layout-navbars-breadcrumb-icon",name:p(o).isCollapse?"ele-Expand":"ele-Fold",size:16,onClick:T},null,8,["name"]),b(l,{class:"layout-navbars-breadcrumb-hide"},{default:f(()=>[b(z,{name:"breadcrumb"},{default:f(()=>[(r(!0),i(D,null,G(e.breadcrumbList,(s,x)=>(r(),h(S,{key:s.meta.tagsViewName?s.meta.tagsViewName:s.meta.title},{default:f(()=>[x===e.breadcrumbList.length-1?(r(),i("span",H,[p(o).isBreadcrumbIcon?(r(),h(n,{key:0,name:s.meta.icon,class:"layout-navbars-breadcrumb-iconfont"},null,8,["name"])):d("",!0),s.meta.tagsViewName?(r(),i("div",K,_(s.meta.tagsViewName),1)):(r(),i("div",J,_(t.$t(s.meta.title)),1))])):(r(),i("a",{key:1,onClick:O(te=>N(s),["prevent"])},[p(o).isBreadcrumbIcon?(r(),h(n,{key:0,name:s.meta.icon,class:"layout-navbars-breadcrumb-iconfont"},null,8,["name"])):d("",!0),Q(_(t.$t(s.meta.title)),1)],8,P))]),_:2},1024))),128))]),_:1})]),_:1})])):d("",!0)}}}),oe=q(Y,[["__scopeId","data-v-6ee1e737"]]);export{oe as default};
