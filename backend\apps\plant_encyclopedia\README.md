# 植物百科系统

基于huabaike_spider.py爬虫数据重新设计的植物百科功能完整后端系统。

## 功能特性

- 🌱 **完整的植物数据管理**: 支持植物的增删改查操作
- 🔍 **强大的搜索功能**: 支持关键词搜索、分类筛选、状态筛选
- 📊 **统计分析**: 提供分类统计、热门植物、最新植物等接口
- 🖼️ **图片管理**: 支持多图片存储和主图设置
- 🏷️ **标签系统**: 支持植物标签和搜索关键词
- 📱 **移动端友好**: RESTful API设计，支持前端和小程序调用
- 🛠️ **管理后台**: 完善的Django Admin管理界面
- 📥 **数据导入**: 支持从爬虫JSON数据批量导入

## 数据库设计

### PlantEncyclopedia 模型字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| name | CharField | 植物名称 |
| scientific_name | CharField | 学名 |
| category | CharField | 植物分类（草本/木本/藤本等） |
| family | CharField | 科 |
| genus | CharField | 属 |
| description | TextField | 植物描述（HTML格式） |
| care_tips | TextField | 养护技巧 |
| growth_habit | TextField | 生长习性 |
| flowering_period | CharField | 花期 |
| images | JSONField | 图片URL列表 |
| main_image | URLField | 主图URL |
| source_url | URLField | 来源URL |
| source_site | CharField | 来源网站 |
| status | IntegerField | 状态（草稿/已发布/已下架） |
| view_count | PositiveIntegerField | 浏览次数 |
| tags | JSONField | 标签列表 |
| search_keywords | CharField | 搜索关键词 |

## API接口

### 基础CRUD接口

```
GET    /api/plant-encyclopedia/plants/          # 获取植物列表
POST   /api/plant-encyclopedia/plants/          # 创建植物
GET    /api/plant-encyclopedia/plants/{id}/     # 获取植物详情
PUT    /api/plant-encyclopedia/plants/{id}/     # 更新植物
DELETE /api/plant-encyclopedia/plants/{id}/     # 删除植物
```

### 扩展功能接口

```
POST   /api/plant-encyclopedia/plants/search/           # 植物搜索
GET    /api/plant-encyclopedia/plants/category-stats/   # 分类统计
GET    /api/plant-encyclopedia/plants/popular/          # 热门植物
GET    /api/plant-encyclopedia/plants/latest/           # 最新植物
```

### 搜索接口示例

```bash
curl -X POST "http://localhost:8000/api/plant-encyclopedia/plants/search/" \
  -H "Content-Type: application/json" \
  -d '{
    "q": "绿萝",
    "category": "藤本植物",
    "status": 1,
    "ordering": "-view_count"
  }'
```

### 响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "count": 1,
    "results": [
      {
        "id": 1,
        "name": "绿萝",
        "scientific_name": "Epipremnum aureum",
        "category": "藤本植物",
        "category_display": "藤本植物",
        "short_description": "绿萝是一种常绿藤本植物，叶片心形，翠绿有光泽...",
        "main_image_url": "https://example.com/images/lvluo1.jpg",
        "status": 1,
        "status_display": "已发布",
        "view_count": 0,
        "create_datetime": "2025-07-03T19:00:00Z"
      }
    ]
  }
}
```

## 数据导入

### 从爬虫数据导入

```bash
# 导入新数据
python manage.py import_plant_data huabaike_flowers.json

# 更新已存在的数据
python manage.py import_plant_data huabaike_flowers.json --update

# 指定批量处理大小
python manage.py import_plant_data huabaike_flowers.json --batch-size 50
```

### 数据格式要求

JSON文件应包含植物数据数组，每个植物对象包含以下字段：

```json
{
  "name": "植物名称",
  "scientific_name": "学名",
  "category": "植物分类",
  "family": "科",
  "genus": "属",
  "description": "描述（HTML格式）",
  "care_tips": "养护技巧",
  "growth_habit": "生长习性",
  "flowering_period": "花期",
  "images": ["图片URL1", "图片URL2"],
  "url": "来源URL"
}
```

## 管理后台

访问 `/admin/` 进入Django管理后台，可以进行以下操作：

- 植物数据的增删改查
- 批量发布/下架植物
- 查看植物图片预览
- 按分类、状态筛选
- 搜索植物名称、学名等

## 部署说明

1. 确保已安装所有依赖
2. 运行数据库迁移：`python manage.py migrate`
3. 创建超级用户：`python manage.py createsuperuser`
4. 导入初始数据：`python manage.py import_plant_data sample_data.json`
5. 启动服务：`python manage.py runserver`

## 扩展功能

系统设计时考虑了扩展性，可以轻松添加以下功能：

- 植物收藏功能
- 用户评论和评分
- 植物养护记录
- 图片上传和处理
- 全文搜索引擎集成
- 缓存优化
- API限流和权限控制
