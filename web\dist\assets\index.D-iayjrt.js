import{a as m}from"./index.GuQX7xXE.js";import{G as u,c as f}from"./crud.CQT7nsYU.js";import{h as d}from"./columnPermission.6pZgyu0_.js";import{d as s,o as _,b as o,g as l,e as C,w as g,f as h,n as w,u as x}from"./vue.zNq9Glab.js";import"./dictionary.DBJS--kg.js";import"./authFunction.BcROZVTX.js";import"./index.NzcGb9Oc.js";import"./_plugin-vue_export-helper.DlAUqK2U.js";const O=s({name:"areas"}),N=s({...O,setup(k){const{crudBinding:r,crudRef:t,crudExpose:n,crudOptions:a,resetCrudOptions:c}=m({createCrudOptions:f});return _(async()=>{const e=await d(u,a);c(e),n.doRefresh()}),(e,B)=>{const p=o("fs-crud"),i=o("fs-page");return C(),l(i,null,{default:g(()=>[h(p,w({ref_key:"crudRef",ref:t},x(r)),null,16)]),_:1})}}});export{N as default};
