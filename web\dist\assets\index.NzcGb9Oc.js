import{r as U,X as B}from"./index.GuQX7xXE.js";import{d as N,a as p,r as P,o as A,b as i,g as C,e as g,w as _,l as j,f as c,h as D,c as E,F,j as L}from"./vue.zNq9Glab.js";import{_ as R}from"./_plugin-vue_export-helper.DlAUqK2U.js";const X={class:"option"},q=N({__name:"index",props:{modelValue:{type:Array||String||Number,default:()=>[]},tableConfig:{type:Object,default:{url:null,label:null,value:null,isTree:!1,lazy:!0,size:"default",load:()=>{},data:[],isMultiple:!1,collapseTags:!1,treeProps:{children:"children",hasChildren:"hasChildren"},columns:[]}},displayLabel:{}},emits:["update:modelValue"],setup(v,{emit:V}){const e=v;console.log(e.tableConfig);const y=V,x=p(),m=p();p();const b=p(void 0),d=p([]),a=P({page:1,limit:10,total:0}),f=l=>{const{tableConfig:t}=e,s=l.map(n=>n[t.value]);m.value=l.map(n=>n[t.label]),y("update:modelValue",s)},w=l=>{const{tableConfig:t}=e;!t.isMultiple&&l&&y("update:modelValue",l[t.value])},u=async()=>{const l=e.tableConfig.url;console.log(l);const t={page:a.page,limit:a.limit,search:b.value},{data:s,page:n,limit:r,total:h}=await U({url:l,params:t});a.page=n,a.limit=r,a.total=h,e.tableConfig.data===void 0||e.tableConfig.data.length===0?e.tableConfig.isTree?d.value=B.toArrayTree(s,{parentKey:"parent",key:"id",children:"children"}):d.value=s:d.value=e.tableConfig.data},z=l=>{l&&u()},k=l=>{a.page=l,u()};return A(()=>{}),(l,t)=>{const s=i("el-button"),n=i("el-input"),r=i("el-table-column"),h=i("el-table"),S=i("el-pagination"),T=i("el-select");return g(),C(T,{"popper-class":"popperClass",class:"tableSelector",multiple:"",collapseTags:e.tableConfig.collapseTags,modelValue:m.value,"onUpdate:modelValue":t[3]||(t[3]=o=>m.value=o),placeholder:"请选择",onVisibleChange:z},{empty:_(()=>[j("div",X,[c(n,{style:{"margin-bottom":"10px"},modelValue:b.value,"onUpdate:modelValue":t[0]||(t[0]=o=>b.value=o),clearable:"",placeholder:"请输入关键词",onChange:u,onClear:u},{append:_(()=>[c(s,{type:"primary",icon:"Search"})]),_:1},8,["modelValue"]),c(h,{ref_key:"tableRef",ref:x,data:d.value,size:e.tableConfig.size,border:"","row-key":"id",lazy:e.tableConfig.lazy,load:e.tableConfig.load,"tree-props":e.tableConfig.treeProps,style:{width:"600px"},"max-height":"200",height:"200","highlight-current-row":!e.tableConfig.isMultiple,onSelectionChange:f,onSelect:f,onSelectAll:f,onCurrentChange:w},{default:_(()=>[e.tableConfig.isMultiple?(g(),C(r,{key:0,fixed:"",type:"selection","reserve-selection":"",width:"55"})):D("",!0),c(r,{fixed:"",type:"index",label:"#",width:"50"}),(g(!0),E(F,null,L(e.tableConfig.columns,(o,M)=>(g(),C(r,{prop:o.prop,label:o.label,width:o.width,key:M},null,8,["prop","label","width"]))),128))]),_:1},8,["data","size","lazy","load","tree-props","highlight-current-row"]),c(S,{style:{"margin-top":"10px"},background:"","current-page":a.page,"onUpdate:currentPage":t[1]||(t[1]=o=>a.page=o),"page-size":a.limit,"onUpdate:pageSize":t[2]||(t[2]=o=>a.limit=o),layout:"prev, pager, next",total:a.total,onCurrentChange:k},null,8,["current-page","page-size","total"])])]),_:1},8,["collapseTags","modelValue"])}}}),G=R(q,[["__scopeId","data-v-76ad58f8"]]);export{G as t};
