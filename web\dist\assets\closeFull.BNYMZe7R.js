import{d as s,M as n,b as c,c as r,h as u,u as _,e as i,l as m,f}from"./vue.zNq9Glab.js";import{J as d}from"./index.GuQX7xXE.js";import{_ as p}from"./_plugin-vue_export-helper.DlAUqK2U.js";const C={key:0,class:"layout-navbars-close-full"},v={class:"layout-navbars-close-full-icon"},g=s({name:"layoutCloseFull"}),F=s({...g,setup(V){const e=d(),{isTagsViewCurrenFull:o}=n(e),t=()=>{e.setCurrenFullscreen(!1)};return(l,b)=>{const a=c("SvgIcon");return _(o)?(i(),r("div",C,[m("div",v,[f(a,{name:"ele-Close",title:l.$t("message.tagsView.closeFullscreen"),onClick:t},null,8,["title"])])])):u("",!0)}}}),w=p(F,[["__scopeId","data-v-83ab04b1"]]);export{w as default};
