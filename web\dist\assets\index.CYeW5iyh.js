const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.CW1UNz27.js","assets/index.GuQX7xXE.js","assets/vue.zNq9Glab.js","assets/index.WGbL7eFL.css","assets/authFunction.BcROZVTX.js","assets/crud.DkvPVLeS.js","assets/RoleUserStores.qMOBT--c.js","assets/RoleDrawer.Sf_cwtiB.js","assets/api.Bbrb5cXl.js","assets/RoleUsersStores.DOHNBxdW.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/RoleDrawer.DWSu5Noa.css"])))=>i.map(i=>d[i]);
import{a as w,_ as s}from"./index.GuQX7xXE.js";import{createCrudOptions as C}from"./crud.lXCGt1Vx.js";import{R as D}from"./api.Bbrb5cXl.js";import{R as P}from"./RoleMenuBtnStores.Dix3hi05.js";import{R as x}from"./RoleMenuFieldStores.BbCdho5b.js";import{R as B}from"./RoleUsersStores.DOHNBxdW.js";import{R as E}from"./RoleUserStores.qMOBT--c.js";import{d as a,a as M,o as S,b as t,g as k,e as v,w as y,f as e,n as h,u as o,P as n}from"./vue.zNq9Glab.js";import"./dictionary.DBJS--kg.js";import"./authFunction.BcROZVTX.js";const A=a({name:"role"}),J=a({...A,setup(O){const _=n(()=>s(()=>import("./index.CW1UNz27.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),r=M(),c=n(()=>s(()=>import("./RoleDrawer.Sf_cwtiB.js"),__vite__mapDeps([7,1,2,3,8,9,10,11]))),l=D(),f=P(),m=x(),p=B(),i=E(),{crudBinding:u,crudRef:R,crudExpose:d}=w({createCrudOptions:C,context:{RoleDrawer:l,RoleMenuBtn:f,RoleMenuField:m,RoleUserDrawer:i,RoleUserRef:r}});return S(async()=>{d.doRefresh(),p.get_all_users()}),(V,F)=>{const U=t("fs-crud"),g=t("fs-page");return v(),k(g,null,{default:y(()=>[e(U,h({ref_key:"crudRef",ref:R},o(u)),null,16),e(o(c)),e(o(_),{ref_key:"RoleUserRef",ref:r},null,512)]),_:1})}}});export{J as default};
