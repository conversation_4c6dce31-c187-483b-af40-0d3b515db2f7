const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/parent.C6PQc-w_.js","assets/index.GuQX7xXE.js","assets/vue.zNq9Glab.js","assets/index.WGbL7eFL.css","assets/index.BNcob5p7.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/index.Cj0yML7S.css"])))=>i.map(i=>d[i]);
import{J as M,u as H,N as L,_ as c}from"./index.GuQX7xXE.js";import{d as f,a as P,Q as E,M as u,p as a,o as N,b as o,g as i,e as _,w as m,f as t,h as A,u as p,P as d,q as B}from"./vue.zNq9Glab.js";const I=f({name:"layoutMain"}),$=f({...I,setup(S,{expose:y}){const v=d(()=>c(()=>import("./parent.C6PQc-w_.js"),__vite__mapDeps([0,1,2,3]))),g=d(()=>c(()=>import("./index.BNcob5p7.js"),__vite__mapDeps([4,2,5,6]))),s=P(),x=E(),b=M(),h=H(),{themeConfig:e}=u(h),{isTagsViewCurrenFull:w}=u(b),k=a(()=>e.value.isFooter&&!x.meta.isIframe),C=a(()=>e.value.isFixedHeader),T=a(()=>e.value.isFixedHeader?".layout-backtop-header-fixed .el-scrollbar__wrap":".layout-backtop .el-scrollbar__wrap"),r=a(()=>{if(w.value)return"0px";const{isTagsview:l,layout:n}=e.value;return l&&n!=="classic"?"85px":"51px"});return N(()=>{L.done(600)}),y({layoutMainScrollbarRef:s}),(l,n)=>{const R=o("el-scrollbar"),V=o("el-backtop"),F=o("el-main");return _(),i(F,{class:"layout-main",style:B(C.value?`height: calc(100% - ${r.value})`:`minHeight: calc(100% - ${r.value})`)},{default:m(()=>[t(R,{ref_key:"layoutMainScrollbarRef",ref:s,class:"layout-main-scroll layout-backtop-header-fixed","wrap-class":"layout-main-scroll","view-class":"layout-main-scroll"},{default:m(()=>[t(p(v)),k.value?(_(),i(p(g),{key:0})):A("",!0)]),_:1},512),t(V,{target:T.value},null,8,["target"])]),_:1},8,["style"])}}});export{$ as default};
