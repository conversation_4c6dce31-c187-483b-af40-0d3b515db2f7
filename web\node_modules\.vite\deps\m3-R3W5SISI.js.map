{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/m3/m3.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/m3/m3.ts\nvar conf = {\n  comments: {\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"(*\", close: \"*)\" },\n    { open: \"<*\", close: \"*>\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".m3\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\n    \"AND\",\n    \"ANY\",\n    \"ARRAY\",\n    \"AS\",\n    \"BEGIN\",\n    \"BITS\",\n    \"BRANDED\",\n    \"BY\",\n    \"CASE\",\n    \"CONST\",\n    \"DIV\",\n    \"DO\",\n    \"ELSE\",\n    \"ELSIF\",\n    \"END\",\n    \"EVAL\",\n    \"EXCEPT\",\n    \"EXCEPTION\",\n    \"EXIT\",\n    \"EXPORTS\",\n    \"FINALLY\",\n    \"FOR\",\n    \"FROM\",\n    \"GENERIC\",\n    \"IF\",\n    \"IMPORT\",\n    \"IN\",\n    \"INTERFACE\",\n    \"LOCK\",\n    \"LOOP\",\n    \"METHODS\",\n    \"MOD\",\n    \"MODULE\",\n    \"NOT\",\n    \"OBJECT\",\n    \"OF\",\n    \"OR\",\n    \"OVERRIDES\",\n    \"PROCEDURE\",\n    \"RAISE\",\n    \"RAISES\",\n    \"READONLY\",\n    \"RECORD\",\n    \"REF\",\n    \"REPEAT\",\n    \"RETURN\",\n    \"REVEAL\",\n    \"SET\",\n    \"THEN\",\n    \"TO\",\n    \"TRY\",\n    \"TYPE\",\n    \"TYPECASE\",\n    \"UNSAFE\",\n    \"UNTIL\",\n    \"UNTRACED\",\n    \"VALUE\",\n    \"VAR\",\n    \"WHILE\",\n    \"WITH\"\n  ],\n  reservedConstNames: [\n    \"ABS\",\n    \"ADR\",\n    \"ADRSIZE\",\n    \"BITSIZE\",\n    \"BYTESIZE\",\n    \"CEILING\",\n    \"DEC\",\n    \"DISPOSE\",\n    \"FALSE\",\n    \"FIRST\",\n    \"FLOAT\",\n    \"FLOOR\",\n    \"INC\",\n    \"ISTYPE\",\n    \"LAST\",\n    \"LOOPHOLE\",\n    \"MAX\",\n    \"MIN\",\n    \"NARROW\",\n    \"NEW\",\n    \"NIL\",\n    \"NUMBER\",\n    \"ORD\",\n    \"ROUND\",\n    \"SUBARRAY\",\n    \"TRUE\",\n    \"TRUNC\",\n    \"TYPECODE\",\n    \"VAL\"\n  ],\n  reservedTypeNames: [\n    \"ADDRESS\",\n    \"ANY\",\n    \"BOOLEAN\",\n    \"CARDINAL\",\n    \"CHAR\",\n    \"EXTENDED\",\n    \"INTEGER\",\n    \"LONGCARD\",\n    \"LONGINT\",\n    \"LONGREAL\",\n    \"MUTEX\",\n    \"NULL\",\n    \"REAL\",\n    \"REFANY\",\n    \"ROOT\",\n    \"TEXT\"\n  ],\n  operators: [\"+\", \"-\", \"*\", \"/\", \"&\", \"^\", \".\"],\n  relations: [\"=\", \"#\", \"<\", \"<=\", \">\", \">=\", \"<:\", \":\"],\n  delimiters: [\"|\", \"..\", \"=>\", \",\", \";\", \":=\"],\n  symbols: /[>=<#.,:;+\\-*/&^]+/,\n  escapes: /\\\\(?:[\\\\fnrt\"']|[0-7]{3})/,\n  tokenizer: {\n    root: [\n      // Identifiers and keywords\n      [/_\\w*/, \"invalid\"],\n      [\n        /[a-zA-Z][a-zA-Z0-9_]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@reservedConstNames\": { token: \"constant.reserved.$0\" },\n            \"@reservedTypeNames\": { token: \"type.reserved.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Whitespace\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      // Integer- and real literals\n      [/[0-9]+\\.[0-9]+(?:[DdEeXx][\\+\\-]?[0-9]+)?/, \"number.float\"],\n      [/[0-9]+(?:\\_[0-9a-fA-F]+)?L?/, \"number\"],\n      // Operators, relations, and delimiters\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operators\",\n            \"@relations\": \"operators\",\n            \"@delimiters\": \"delimiter\",\n            \"@default\": \"invalid\"\n          }\n        }\n      ],\n      // Character literals\n      [/'[^\\\\']'/, \"string.char\"],\n      [/(')(@escapes)(')/, [\"string.char\", \"string.escape\", \"string.char\"]],\n      [/'/, \"invalid\"],\n      // Text literals\n      [/\"([^\"\\\\]|\\\\.)*$/, \"invalid\"],\n      [/\"/, \"string.text\", \"@text\"]\n    ],\n    text: [\n      [/[^\\\\\"]+/, \"string.text\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"invalid\"],\n      [/\"/, \"string.text\", \"@pop\"]\n    ],\n    comment: [\n      [/\\(\\*/, \"comment\", \"@push\"],\n      [/\\*\\)/, \"comment\", \"@pop\"],\n      [/./, \"comment\"]\n    ],\n    pragma: [\n      [/<\\*/, \"keyword.pragma\", \"@push\"],\n      [/\\*>/, \"keyword.pragma\", \"@pop\"],\n      [/./, \"keyword.pragma\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\(\\*/, \"comment\", \"@comment\"],\n      [/<\\*/, \"keyword.pragma\", \"@pragma\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,IAC1B,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,IAC1B,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,IAClD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,IACxD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,EACrD;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,oBAAoB;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,mBAAmB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC7C,WAAW,CAAC,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM,MAAM,GAAG;AAAA,EACrD,YAAY,CAAC,KAAK,MAAM,MAAM,KAAK,KAAK,IAAI;AAAA,EAC5C,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ,CAAC,QAAQ,SAAS;AAAA,MAClB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,uBAAuB,EAAE,OAAO,uBAAuB;AAAA,YACvD,sBAAsB,EAAE,OAAO,mBAAmB;AAAA,YAClD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,cAAc,WAAW;AAAA;AAAA,MAE1B,CAAC,4CAA4C,cAAc;AAAA,MAC3D,CAAC,+BAA+B,QAAQ;AAAA;AAAA,MAExC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,cAAc;AAAA,YACd,eAAe;AAAA,YACf,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,YAAY,aAAa;AAAA,MAC1B,CAAC,oBAAoB,CAAC,eAAe,iBAAiB,aAAa,CAAC;AAAA,MACpE,CAAC,KAAK,SAAS;AAAA;AAAA,MAEf,CAAC,mBAAmB,SAAS;AAAA,MAC7B,CAAC,KAAK,eAAe,OAAO;AAAA,IAC9B;AAAA,IACA,MAAM;AAAA,MACJ,CAAC,WAAW,aAAa;AAAA,MACzB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,SAAS;AAAA,MACjB,CAAC,KAAK,eAAe,MAAM;AAAA,IAC7B;AAAA,IACA,SAAS;AAAA,MACP,CAAC,QAAQ,WAAW,OAAO;AAAA,MAC3B,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,OAAO,kBAAkB,OAAO;AAAA,MACjC,CAAC,OAAO,kBAAkB,MAAM;AAAA,MAChC,CAAC,KAAK,gBAAgB;AAAA,IACxB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,OAAO,kBAAkB,SAAS;AAAA,IACrC;AAAA,EACF;AACF;", "names": []}