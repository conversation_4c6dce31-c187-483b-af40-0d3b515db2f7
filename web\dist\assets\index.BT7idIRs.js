import{a as c}from"./index.GuQX7xXE.js";import{createCrudOptions as p}from"./crud.CWXEXgUo.js";import{d as o,o as f,b as e,g as u,e as _,w as d,f as m,n as i,u as l}from"./vue.zNq9Glab.js";import"./dictionary.DBJS--kg.js";import"./authFunction.BcROZVTX.js";const g=o({name:"whiteList"}),v=o({...g,setup(h){const{crudBinding:r,crudRef:t,crudExpose:s}=c({createCrudOptions:p});return f(()=>{s.doRefresh()}),(x,C)=>{const n=e("fs-crud"),a=e("fs-page");return _(),u(a,null,{default:d(()=>[m(n,i({ref_key:"crudRef",ref:t},l(r)),null,16)]),_:1})}}});export{v as default};
