import{a as N,c as T,X as f,y as U}from"./index.GuQX7xXE.js";import{a as E,c as X}from"./crud.3b27k8HF.js";import{d as q,a as z,p as O,b as s,g as P,e as $,w as e,l as b,f as t,n as j,u as m,k as _,t as A}from"./vue.zNq9Glab.js";const G={style:{height:"500px"}},L=q({__name:"index",props:{refreshCallback:{type:Function,required:!0}},setup(h,{expose:k}){const y=h,r=z(!1),i=y.refreshCallback,d=()=>{r.value=!1,a.value=[]},C=async()=>{a.value.length!==0&&(await E(g.value.getSearchFormData().role_id,f.pluck(a.value,"id")).then(l=>{U(l.msg)}),i&&i(),d())},{crudBinding:x,crudRef:g,crudExpose:u,selectedRows:a}=N({createCrudOptions:X,context:{}}),{setSearchFormData:R,doRefresh:w}=u,v=O(()=>a.value.length),D=l=>{const o=u.getBaseTableRef(),n=u.getTableData();f.pluck(n,"id").includes(l.id)?o.toggleRowSelection(l,!1):a.value=f.remove(a.value,c=>c.id!==l.id)};return k({dialog:r,setSearchFormData:R,doRefresh:w,parentRefreshCallbackFunc:i}),(l,o)=>{const n=s("el-button"),c=s("el-table-column"),V=s("el-table"),B=s("el-popover"),S=s("fs-crud"),F=s("el-dialog");return $(),P(F,{modelValue:r.value,"onUpdate:modelValue":o[0]||(o[0]=p=>r.value=p),title:"添加授权用户",direction:"rtl","destroy-on-close":"","before-close":d},{footer:e(()=>[b("div",null,[t(n,{type:"primary",onClick:C},{default:e(()=>o[1]||(o[1]=[_(" 确定")])),_:1,__:[1]}),t(n,{onClick:d},{default:e(()=>o[2]||(o[2]=[_(" 取消")])),_:1,__:[2]})])]),default:e(()=>[b("div",G,[t(S,j({ref_key:"crudRef",ref:g},m(x)),{"pagination-right":e(()=>[t(B,{placement:"top",width:200,trigger:"click"},{reference:e(()=>[t(n,{text:"",type:v.value>0?"primary":""},{default:e(()=>[_("已选中"+A(v.value)+"条数据",1)]),_:1},8,["type"])]),default:e(()=>[t(V,{data:m(a),size:"small","max-height":500},{default:e(()=>[t(c,{width:"100",property:"name",label:"用户名"}),t(c,{fixed:"right",label:"操作","min-width":"50"},{default:e(p=>[t(n,{text:"",type:"info",icon:m(T),onClick:H=>D(p.row),circle:""},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},16)])]),_:1},8,["modelValue"])}}});export{L as default};
