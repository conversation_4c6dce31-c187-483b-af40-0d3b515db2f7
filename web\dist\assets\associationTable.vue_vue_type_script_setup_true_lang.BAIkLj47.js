import"./index.GuQX7xXE.js";import{d as S,r as q,a as b,b as m,c as f,e as r,f as s,u as l,w as a,F as g,j as h,g as y,l as d,t as u}from"./vue.zNq9Glab.js";const z={style:{float:"left"}},B={style:{float:"right",color:"#8492a6","font-size":"13px"}},N={style:{float:"left"}},O={style:{float:"right",color:"#8492a6","font-size":"13px"}},U={style:{float:"left"}},j={style:{float:"right",color:"#8492a6","font-size":"13px"}},w={style:{float:"left"}},R={style:{float:"right",color:"#8492a6","font-size":"13px"}},A=S({__name:"associationTable",props:["value"],emits:["updateVal"],setup(D,{emit:E}){let o=q({table:null,primarykey:null,field:null,searchField:null,oldSearchField:null});b("");let k=b([]),i=b([]);const v=b(),F=p=>{const{tableFields:t}=k.find(n=>n.table===p);i=t},x=p=>{const t=i.filter(n=>p.indexOf(n.field)>-1);o.searchField=t};return(p,t)=>{const n=m("el-option"),_=m("el-select"),c=m("el-form-item"),C=m("el-form");return r(),f("div",null,[s(C,{model:l(o),ref_key:"associationRef",ref:v},{default:a(()=>[s(c,{label:"关联表",prop:"table",rules:[{required:!0,message:"必填项",trigger:"blur"}]},{default:a(()=>[s(_,{modelValue:l(o).table,"onUpdate:modelValue":t[0]||(t[0]=e=>l(o).table=e),filterable:"",clearable:"",placeholder:"请选择",onChange:F},{default:a(()=>[(r(!0),f(g,null,h(l(k),e=>(r(),y(n,{key:e.table,label:e.tableName,value:e.table},{default:a(()=>[d("span",z,u(e.tableName),1),d("span",B,u(e.table),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(c,{label:"显示字段",prop:"field",rules:[{required:!0,message:"必填项",trigger:"blur"}]},{default:a(()=>[s(_,{modelValue:l(o).field,"onUpdate:modelValue":t[1]||(t[1]=e=>l(o).field=e),filterable:"",clearable:"",placeholder:"请选择"},{default:a(()=>[(r(!0),f(g,null,h(l(i),e=>(r(),y(n,{key:e.table,label:e.title,value:e.field},{default:a(()=>[d("span",N,u(e.field),1),d("span",O,u(e.title),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(c,{label:"储存字段",prop:"primarykey",rules:[{required:!0,message:"必填项",trigger:"blur"}]},{default:a(()=>[s(_,{modelValue:l(o).primarykey,"onUpdate:modelValue":t[2]||(t[2]=e=>l(o).primarykey=e),filterable:"",clearable:"",placeholder:"请选择"},{default:a(()=>[(r(!0),f(g,null,h(l(i),(e,V)=>(r(),y(n,{key:V,label:e.title,value:e.field},{default:a(()=>[d("span",U,u(e.field),1),d("span",j,u(e.title),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(c,{label:"过滤条件",prop:"oldSearchField",rules:[{required:!0,message:"必填项",trigger:"blur"}]},{default:a(()=>[s(_,{modelValue:l(o).oldSearchField,"onUpdate:modelValue":t[3]||(t[3]=e=>l(o).oldSearchField=e),multiple:"",filterable:"",clearable:"",placeholder:"请选择",onChange:x},{default:a(()=>[(r(!0),f(g,null,h(l(i),(e,V)=>(r(),y(n,{key:V,label:e.title,value:e.field},{default:a(()=>[d("span",w,u(e.field),1),d("span",R,u(e.title),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])}}});export{A as _};
