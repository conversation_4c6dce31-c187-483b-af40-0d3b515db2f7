import{d as m,M as r,p as d,b as f,c as p,e as g,l as e,t as i,f as u,w as _,k as h,q as v}from"./vue.zNq9Glab.js";import{u as w,J as T,S as b}from"./index.GuQX7xXE.js";import{_ as C}from"./_plugin-vue_export-helper.DlAUqK2U.js";const F=m({name:"401",setup(){const s=w(),t=T(),{themeConfig:a}=r(s),{isTagsViewCurrenFull:n}=r(t),l=()=>{b.clear(),window.location.reload()},c=d(()=>{let{isTagsview:o}=a.value;return n.value?"30px":o?"114px":"80px"});return{onSetAuth:l,initTagViewHeight:c}}}),V={class:"error-flex"},$={class:"left"},x={class:"left-item"},y={class:"left-item-animation left-item-title"},A={class:"left-item-animation left-item-msg"},k={class:"left-item-animation left-item-btn"};function S(s,t,a,n,l,c){const o=f("el-button");return g(),p("div",{class:"error layout-view-bg-white",style:v({height:`calc(100vh - ${s.initTagViewHeight}`})},[e("div",V,[e("div",$,[e("div",x,[t[0]||(t[0]=e("div",{class:"left-item-animation left-item-num"},"401",-1)),e("div",y,i(s.$t("message.noAccess.accessTitle")),1),e("div",A,i(s.$t("message.noAccess.accessMsg")),1),e("div",k,[u(o,{type:"primary",round:"",onClick:s.onSetAuth},{default:_(()=>[h(i(s.$t("message.noAccess.accessBtn")),1)]),_:1},8,["onClick"])])])]),t[1]||(t[1]=e("div",{class:"right"},[e("img",{src:"https://img-blog.csdnimg.cn/3333f265772a4fa89287993500ecbf96.png?x-oss-process=image/watermark,type_d3F5LXplbmhlaQ,shadow_50,text_Q1NETiBAbHl0LXRvcA==,size_16,color_FFFFFF,t_70,g_se,x_16"})],-1))])],4)}const H=C(F,[["render",S],["__scopeId","data-v-858f28e6"]]);export{H as default};
