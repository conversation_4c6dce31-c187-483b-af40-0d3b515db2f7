import{d as c,M as m,R as u,p as f,b as p,c as g,e as _,l as e,t as i,f as v,w as h,k as T,q as w}from"./vue.zNq9Glab.js";import{u as C,J as V}from"./index.GuQX7xXE.js";import{_ as $}from"./_plugin-vue_export-helper.DlAUqK2U.js";const y=c({name:"404",setup(){const t=C(),s=V(),{themeConfig:n}=m(t),{isTagsViewCurrenFull:a}=m(s),r=u(),l=()=>{r.push("/")},o=f(()=>{let{isTagsview:d}=n.value;return a.value?"30px":d?"114px":"80px"});return{onGoHome:l,initTagViewHeight:o}}}),b="/assets/img404.DizYJBZK.png",k={class:"error-flex"},B={class:"left"},R={class:"left-item"},x={class:"left-item-animation left-item-title"},F={class:"left-item-animation left-item-msg"},H={class:"left-item-animation left-item-btn"};function N(t,s,n,a,r,l){const o=p("el-button");return _(),g("div",{class:"error layout-view-bg-white",style:w({height:`calc(100vh - ${t.initTagViewHeight}`})},[e("div",k,[e("div",B,[e("div",R,[s[0]||(s[0]=e("div",{class:"left-item-animation left-item-num"},"404",-1)),e("div",x,i(t.$t("message.notFound.foundTitle")),1),e("div",F,i(t.$t("message.notFound.foundMsg")),1),e("div",H,[v(o,{type:"primary",round:"",onClick:t.onGoHome},{default:h(()=>[T(i(t.$t("message.notFound.foundBtn")),1)]),_:1},8,["onClick"])])])]),s[1]||(s[1]=e("div",{class:"right"},[e("img",{src:b})],-1))])],4)}const J=$(y,[["render",N],["__scopeId","data-v-43ed7a98"]]);export{J as default};
