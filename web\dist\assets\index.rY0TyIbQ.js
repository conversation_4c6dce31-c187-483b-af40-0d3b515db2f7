const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/breadcrumb.B4N-frzk.js","assets/vue.zNq9Glab.js","assets/index.GuQX7xXE.js","assets/index.WGbL7eFL.css","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/breadcrumb.CgSM-oQ7.css","assets/user.BfdNh4_2.js","assets/user.BGetWwHS.css","assets/index.Bg8y3cFr.js","assets/logo-mini.DjPeV5ul.js","assets/index.BVXIswWj.css","assets/horizontal.RTk9tx5T.js","assets/horizontal.CwTBSrrv.css"])))=>i.map(i=>d[i]);
import{I as D,u as O,l as d,_ as r}from"./index.GuQX7xXE.js";import{d as C,M as p,Q as V,r as A,p as f,o as F,O as M,c as w,e as _,g as h,h as L,f as v,u as a,P as i}from"./vue.zNq9Glab.js";import{_ as H}from"./_plugin-vue_export-helper.DlAUqK2U.js";const N={class:"layout-navbars-breadcrumb-index"},U=C({name:"layoutBreadcrumbIndex"}),j=C({...U,setup(z){const R=i(()=>r(()=>import("./breadcrumb.B4N-frzk.js"),__vite__mapDeps([0,1,2,3,4,5]))),g=i(()=>r(()=>import("./user.BfdNh4_2.js"),__vite__mapDeps([6,2,1,3,4,7]))),I=i(()=>r(()=>import("./index.Bg8y3cFr.js"),__vite__mapDeps([8,1,2,3,9,4,10]))),y=i(()=>r(()=>import("./horizontal.RTk9tx5T.js"),__vite__mapDeps([11,2,1,3,4,12]))),E=D(),S=O(),{themeConfig:c}=p(S),{routesList:u}=p(E),b=V(),l=A({menuList:[]}),x=f(()=>{let{isShowLogo:t,layout:e}=c.value;return t&&e==="classic"||t&&e==="transverse"}),T=f(()=>{let{layout:t,isClassicSplitMenu:e}=c.value;return t==="transverse"||e&&t==="classic"}),m=()=>{let{layout:t,isClassicSplitMenu:e}=c.value;if(t==="classic"&&e){l.menuList=B(n(u.value));const s=P(b.path);d.emit("setSendClassicChildren",s)}else l.menuList=n(u.value)},B=t=>(t.map(e=>{e.children&&delete e.children}),t),n=t=>t.filter(e=>{var s;return!((s=e.meta)!=null&&s.isHide)}).map(e=>(e=Object.assign({},e),e.children&&(e.children=n(e.children)),e)),P=t=>{const e=t.split("/");let s={children:[]};return n(u.value).map((o,k)=>{o.path===`/${e[1]}`&&(o.k=k,s.item={...o},s.children=[{...o}],o.children&&(s.children=o.children))}),s};return F(()=>{m(),d.on("getBreadcrumbIndexSetFilterRoutes",()=>{m()})}),M(()=>{d.off("getBreadcrumbIndexSetFilterRoutes",()=>{})}),(t,e)=>(_(),w("div",N,[x.value?(_(),h(a(I),{key:0})):L("",!0),v(a(R)),T.value?(_(),h(a(y),{key:1,menuList:l.menuList},null,8,["menuList"])):L("",!0),v(a(g))]))}}),G=H(j,[["__scopeId","data-v-2e3417a1"]]);export{G as default};
