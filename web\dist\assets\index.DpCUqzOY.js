import{a as E}from"./index.GuQX7xXE.js";import C from"./crud.CwzZQ1Ib.js";import{_ as h}from"./index.vue_vue_type_script_setup_true_name_importExcel_lang.BmTOIKpl.js";import{d as v,C as N,o as $,b as r,y as w,g as c,e as p,w as t,f as i,n as B,A as I,k as m,t as M}from"./vue.zNq9Glab.js";import{_ as S}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./authFunction.BcROZVTX.js";const V=v({name:"VIEWSETNAME",components:{importExcel:h},setup(){const e=N(),o={componentName:e==null?void 0:e.type.name},{crudBinding:s,crudRef:n,crudExpose:a,resetCrudOptions:_}=E({createCrudOptions:C,context:o});return $(()=>{a.doRefresh()}),{crudBinding:s,crudRef:n}}});function k(e,o,s,n,a,_){const u=r("el-tag"),d=r("importExcel"),f=r("fs-crud"),l=r("fs-page"),g=w("auth");return p(),c(l,{class:"PageFeatureSearchMulti"},{default:t(()=>[i(f,B({ref:"crudRef"},e.crudBinding),{cell_url:t(x=>[i(u,{size:"small"},{default:t(()=>[m(M(x.row.url),1)]),_:2},1024)]),"actionbar-right":t(()=>[I((p(),c(d,{api:"api/VIEWSETNAME/"},{default:t(()=>o[0]||(o[0]=[m("导入")])),_:1,__:[0]})),[[g,"user:Import"]])]),_:1},16)]),_:1})}const O=S(V,[["render",k]]);export{O as default};
