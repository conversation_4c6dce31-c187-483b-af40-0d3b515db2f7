import{r as l,k as b,v}from"./index.GuQX7xXE.js";import{c as g}from"./commonCrud.DFvADd-j.js";import{d as w,a as s,m as _,b as W,g as x,e as q,J as C,p as R}from"./vue.zNq9Glab.js";import{_ as V}from"./_plugin-vue_export-helper.DlAUqK2U.js";const h="/api/system/login_log/";function k(e){return l({url:h,method:"get",params:e})}function B(e){return l({url:h,method:"post",data:e})}function O(e){return l({url:h+e.id+"/",method:"put",data:e})}function j(e){return l({url:h+e+"/",method:"delete",data:{id:e}})}const D=w({__name:"index",props:{dict:{type:Array,required:!0},modelValue:{}},emits:["update:modelValue"],setup(e,{emit:u}){const o=e,f=u;s([]);const a=s(),p=s([]),c=s({label:"label",value:"value"});_(()=>o.modelValue,n=>{l({url:o.dict.url,params:{id:o.modelValue}}).then(m=>{const i=m.data;console.log(i),i&&i.length===1?a.value=i[0][c.value.label]:a.value=null})},{immediate:!0});const{ui:t}=b(),r=t.formItem.injectFormItemContext();function d(n){f("update:modelValue",n),a.value=n,r.onChange(),r.onBlur()}return o.dict.url instanceof Function?l(o.dict.url).then(n=>{p.value=n.data}):(c.value.label=o.dict.label,c.value.value=o.dict.value,l({url:o.dict.url}).then(n=>{p.value=n.data})),(n,m)=>{const i=W("el-select-v2");return q(),x(i,{modelValue:a.value,"onUpdate:modelValue":m[0]||(m[0]=y=>a.value=y),options:p.value,style:{width:"100%"},clearable:!0,props:c.value,onChange:d},null,8,["modelValue","options","props"])}}}),I=V(D,[["__scopeId","data-v-d3d07a23"]]),L=function({crudExpose:e,isEcharts:u,initChart:o}){return{crudOptions:{request:{pageRequest:async t=>await k(t),addRequest:async({form:t})=>await B(t),editRequest:async({form:t,row:r})=>(t.id=r.id,await O(t)),delRequest:async({row:t})=>await j(t.id)},actionbar:{buttons:{add:{show:!0},showEcharts:{type:"warning",text:R(()=>u.value?"隐藏图表":"显示图表"),click:()=>{u.value=!u.value}}}},rowHandle:{fixed:"right",width:100,buttons:{view:{type:"text"},edit:{show:!1},remove:{show:!1}}},columns:{_index:{title:"序号",form:{show:!1},column:{align:"center",width:"70px",columnSetDisabled:!0,formatter:t=>{let r=t.index??1,d=e.crudBinding.value.pagination;return((d.currentPage??1)-1)*d.pageSize+r+1}}},search:{title:"关键词",column:{show:!1},search:{show:!0,component:{props:{clearable:!0},placeholder:"请输入关键词"}},form:{show:!1,component:{props:{clearable:!0}}}},username:{title:"测试自定义组件",dict:v({url({form:t}){return"/api/system/role/"},label:"name",value:"id"}),form:{component:{name:C(I)}}},ip:{title:"登录ip",search:{disabled:!1},type:"input",column:{minWidth:120},form:{disabled:!0,component:{placeholder:"请输入登录ip"}}},isp:{title:"运营商",search:{disabled:!0},disabled:!0,type:"input",column:{minWidth:120},form:{component:{placeholder:"请输入运营商"}}},continent:{title:"大州",type:"input",column:{minWidth:90},form:{disabled:!0,component:{placeholder:"请输入大州"}},component:{props:{color:"auto"}}},country:{title:"国家",type:"input",column:{minWidth:90},form:{component:{placeholder:"请输入国家"}},component:{props:{color:"auto"}}},province:{title:"省份",type:"input",column:{minWidth:80},form:{component:{placeholder:"请输入省份"}},component:{props:{color:"auto"}}},city:{title:"城市",type:"input",column:{minWidth:80},form:{component:{placeholder:"请输入城市"}},component:{props:{color:"auto"}}},district:{title:"县区",key:"",type:"input",column:{minWidth:80},form:{component:{placeholder:"请输入县区"}},component:{props:{color:"auto"}}},area_code:{title:"区域代码",type:"input",column:{minWidth:90},form:{component:{placeholder:"请输入区域代码"}},component:{props:{color:"auto"}}},country_english:{title:"英文全称",type:"input",column:{minWidth:120},form:{component:{placeholder:"请输入英文全称"}},component:{props:{color:"auto"}}},country_code:{title:"简称",type:"input",column:{minWidth:100},form:{component:{placeholder:"请输入简称"}},component:{props:{color:"auto"}}},longitude:{title:"经度",type:"input",disabled:!0,column:{minWidth:100},form:{component:{placeholder:"请输入经度"}},component:{props:{color:"auto"}}},latitude:{title:"纬度",type:"input",disabled:!0,column:{minWidth:100},form:{component:{placeholder:"请输入纬度"}},component:{props:{color:"auto"}}},login_type:{title:"登录类型",type:"dict-select",search:{disabled:!1},dict:v({data:[{label:"普通登录",value:1},{label:"微信扫码登录",value:2}]}),column:{minWidth:120},form:{component:{placeholder:"请选择登录类型"}}},os:{title:"操作系统",type:"input",column:{minWidth:120},form:{component:{placeholder:"请输入操作系统"}}},browser:{title:"浏览器名",type:"input",column:{minWidth:120},form:{component:{placeholder:"请输入浏览器名"}}},agent:{title:"agent信息",disabled:!0,type:"input",column:{minWidth:120},form:{component:{placeholder:"请输入agent信息"}}},...g({create_datetime:{search:!0}})}}}};export{L as createCrudOptions};
