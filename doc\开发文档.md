# 植物语+ 小程序开发文档

## 目录
1. [技术架构说明](#技术架构说明)
2. [数据库设计文档](#数据库设计文档)
3. [API接口文档](#api接口文档)

---

## 技术架构说明

### 1.1 技术栈选择

基于现有项目框架，推荐以下技术栈：

#### 后端技术栈
- **框架**: Django 4.2 + Django REST Framework
- **数据库**: MySQL 8.0 
- **任务队列**: Celery (已集成)
- **文件存储**: 服务器本地
- **API文档**: drf-yasg (Swagger)

#### 前端技术栈
- **小程序**: 微信小程序原生开发
- **管理后台**: Vue 3 + Element Plus + TypeScript (现有web目录)
- **状态管理**: Pinia
- **构建工具**: Vite

#### 第三方服务
- **植物识别**: Plant.id API
- **微信服务**: 微信小程序API
- **推送服务**: 微信模板消息

### 1.2 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序端   │    │   管理后台Web   │    │   第三方服务     │
│                 │    │                 │    │                 │
│ - 用户界面      │    │ - 内容管理      │    │ - Plant.id API  │
│ - 植物识别      │    │ - 用户管理      │    │ - 微信API       │
│ - 社区分享      │    │ - 数据统计      │    │ - 云存储        │
│ - 养护提醒      │    │ - 系统配置      │    │ - 推送服务      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Django后端    │
                    │                 │
                    │ - REST API      │
                    │ - 业务逻辑      │
                    │ - 数据处理      │
                    │ - 任务调度      │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据存储层    │
                    │                 │
                    │ - MySQL/PG      │    │
                    │ - 文件存储      │
                    └─────────────────┘
```

### 1.3 模块划分

#### 后端模块结构
```
backend/
├── apps/
│   ├── users/          # 用户管理模块
│   ├── plants/         # 植物相关模块
│   ├── identification/ # 植物识别模块
│   ├── reminders/      # 养护提醒模块
│   ├── community/      # 社区分享模块
│   └── wechat/         # 微信集成模块
├── common/             # 公共组件
├── utils/              # 工具函数
└── config/             # 配置文件
```

---

## 数据库设计文档

### 2.1 数据库表结构设计

#### 2.1.1 用户相关表

```sql
-- 用户基础信息表
CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(64) NOT NULL COMMENT '微信openid',
  `unionid` varchar(64) DEFAULT NULL COMMENT '微信unionid',
  `nickname` varchar(50) NOT NULL COMMENT '用户昵称',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `city` varchar(50) DEFAULT NULL COMMENT '所在城市',
  `province` varchar(50) DEFAULT NULL COMMENT '所在省份',
  `country` varchar(50) DEFAULT NULL COMMENT '所在国家',
  `level` int DEFAULT 1 COMMENT '用户等级',
  `points` int DEFAULT 0 COMMENT '积分',
  `status` tinyint DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_unionid` (`unionid`),
  KEY `idx_status` (`status`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';

-- 用户偏好设置表
CREATE TABLE `user_preferences` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `notification_enabled` tinyint DEFAULT 1 COMMENT '是否开启通知：0-关闭，1-开启',
  `reminder_time` json DEFAULT NULL COMMENT '提醒时间设置，JSON格式',
  `interested_categories` json DEFAULT NULL COMMENT '感兴趣的植物分类，JSON数组',
  `privacy_level` tinyint DEFAULT 1 COMMENT '隐私级别：1-公开，2-好友可见，3-仅自己',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  CONSTRAINT `fk_user_preferences_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户偏好设置表';
```

#### 2.1.2 植物相关表

```sql
-- 植物分类表
CREATE TABLE `plant_categories` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `parent_id` int DEFAULT NULL COMMENT '父分类ID',
  `description` text COMMENT '分类描述',
  `icon_url` varchar(255) DEFAULT NULL COMMENT '分类图标URL',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `status` tinyint DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status_sort` (`status`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='植物分类表';




-- 用户植物表
CREATE TABLE `user_plants` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户植物ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `custom_name` varchar(50) NOT NULL COMMENT '自定义名称',
  `plant_name` varchar(100) NOT NULL COMMENT '植物名称',
  `description` text COMMENT '植物描述',
  `image_url` varchar(255) DEFAULT NULL COMMENT '植物图片URL',
  `location` varchar(100) DEFAULT NULL COMMENT '摆放位置',
  `acquisition_date` date DEFAULT NULL COMMENT '获得日期',
  `acquisition_method` varchar(50) DEFAULT NULL COMMENT '获得方式：购买、赠送、自种等',
  `health_status` tinyint DEFAULT 1 COMMENT '健康状态：1-健康，2-一般，3-不佳',
  `growth_stage` varchar(20) DEFAULT NULL COMMENT '生长阶段',
  `notes` text COMMENT '备注',
  `is_public` tinyint DEFAULT 1 COMMENT '是否公开：0-私有，1-公开',
  `status` tinyint DEFAULT 1 COMMENT '状态：0-已删除，1-正常',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_user_plants_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户植物表';
```

#### 2.1.3 植物识别相关表

```sql
-- 植物识别记录表
CREATE TABLE `plant_identifications` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '识别记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `image_url` varchar(255) NOT NULL COMMENT '识别图片URL',
  `identification_type` tinyint NOT NULL COMMENT '识别类型：1-植物识别，2-健康评估',
  `api_request_id` varchar(100) DEFAULT NULL COMMENT 'Plant.id API请求ID',
  `api_response` json DEFAULT NULL COMMENT 'API完整响应，JSON格式',
  `result_confidence` decimal(5,4) DEFAULT NULL COMMENT '识别置信度',
  `identified_plant_name` varchar(100) DEFAULT NULL COMMENT '识别出的植物名称',
  `scientific_name` varchar(100) DEFAULT NULL COMMENT '学名',
  `common_names` json DEFAULT NULL COMMENT '俗名列表，JSON数组',
  `plant_details` json DEFAULT NULL COMMENT '植物详细信息，JSON格式',
  `health_assessment` json DEFAULT NULL COMMENT '健康评估结果，JSON格式',
  `diseases_detected` json DEFAULT NULL COMMENT '检测到的病害，JSON数组',
  `suggestions` json DEFAULT NULL COMMENT '建议，JSON数组',
  `is_correct` tinyint DEFAULT NULL COMMENT '用户反馈识别是否正确：0-错误，1-正确',
  `user_feedback` text COMMENT '用户反馈内容',
  `processing_time` int DEFAULT NULL COMMENT '处理时间（毫秒）',
  `api_cost` decimal(10,6) DEFAULT NULL COMMENT 'API调用成本',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_identification_type` (`identification_type`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_api_request_id` (`api_request_id`),
  CONSTRAINT `fk_plant_identifications_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='植物识别记录表';
```

#### 2.1.4 养护提醒相关表

```sql
-- 提醒类型表
CREATE TABLE `reminder_types` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '提醒类型ID',
  `name` varchar(20) NOT NULL COMMENT '类型名称',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `description` varchar(100) DEFAULT NULL COMMENT '描述',
  `default_frequency` int DEFAULT 7 COMMENT '默认频率（天）',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `status` tinyint DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提醒类型表';

-- 插入默认提醒类型
INSERT INTO `reminder_types` (`name`, `icon`, `description`, `default_frequency`) VALUES
('浇水', 'water', '浇水提醒', 3),
('换水', 'change-water', '换水提醒', 7),
('施肥', 'fertilizer', '施肥提醒', 30);

-- 养护提醒设置表
CREATE TABLE `plant_reminders` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '提醒设置ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_plant_id` bigint NOT NULL COMMENT '用户植物ID',
  `reminder_type_id` int NOT NULL COMMENT '提醒类型ID',
  `frequency_days` int NOT NULL COMMENT '提醒频率（天）',
  `reminder_times` json NOT NULL COMMENT '提醒时间点，JSON数组，格式：["09:00", "18:00"]',
  `last_reminded_time` datetime DEFAULT NULL COMMENT '最后提醒时间',
  `next_reminder_time` datetime DEFAULT NULL COMMENT '下次提醒时间',
  `is_enabled` tinyint DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
  `auto_adjust` tinyint DEFAULT 0 COMMENT '是否自动调整：0-否，1-是',
  `weather_sensitive` tinyint DEFAULT 0 COMMENT '是否受天气影响：0-否，1-是',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_plant_id` (`user_plant_id`),
  KEY `idx_reminder_type_id` (`reminder_type_id`),
  KEY `idx_next_reminder_time` (`next_reminder_time`),
  KEY `idx_is_enabled` (`is_enabled`),
  CONSTRAINT `fk_plant_reminders_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_plant_reminders_user_plant_id` FOREIGN KEY (`user_plant_id`) REFERENCES `user_plants` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_plant_reminders_reminder_type_id` FOREIGN KEY (`reminder_type_id`) REFERENCES `reminder_types` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='养护提醒设置表';

-- 提醒执行记录表
CREATE TABLE `reminder_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '提醒记录ID',
  `reminder_id` bigint NOT NULL COMMENT '提醒设置ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_plant_id` bigint NOT NULL COMMENT '用户植物ID',
  `reminder_type_id` int NOT NULL COMMENT '提醒类型ID',
  `scheduled_time` datetime NOT NULL COMMENT '计划提醒时间',
  `actual_time` datetime DEFAULT NULL COMMENT '实际提醒时间',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态：0-待发送，1-已发送，2-发送失败，3-用户已完成，4-用户跳过',
  `send_method` varchar(20) DEFAULT NULL COMMENT '发送方式：wechat_template, mini_program',
  `template_id` varchar(100) DEFAULT NULL COMMENT '模板消息ID',
  `error_message` text COMMENT '错误信息',
  `user_action` tinyint DEFAULT NULL COMMENT '用户操作：1-已完成，2-跳过，3-延后',
  `user_action_time` datetime DEFAULT NULL COMMENT '用户操作时间',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_reminder_id` (`reminder_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_scheduled_time` (`scheduled_time`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_reminder_logs_reminder_id` FOREIGN KEY (`reminder_id`) REFERENCES `plant_reminders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_reminder_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提醒执行记录表';
```

#### 2.1.5 社区分享相关表

```sql
-- 话题标签表
CREATE TABLE `community_topics` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '话题ID',
  `name` varchar(50) NOT NULL COMMENT '话题名称',
  `description` varchar(200) DEFAULT NULL COMMENT '话题描述',
  `icon_url` varchar(255) DEFAULT NULL COMMENT '话题图标URL',
  `color` varchar(7) DEFAULT '#1890ff' COMMENT '话题颜色',
  `post_count` int DEFAULT 0 COMMENT '帖子数量',
  `follow_count` int DEFAULT 0 COMMENT '关注数量',
  `is_hot` tinyint DEFAULT 0 COMMENT '是否热门：0-否，1-是',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `status` tinyint DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_status_hot` (`status`, `is_hot`),
  KEY `idx_post_count` (`post_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='话题标签表';

-- 社区帖子表
CREATE TABLE `community_posts` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '帖子ID',
  `user_id` bigint NOT NULL COMMENT '发布用户ID',
  `title` varchar(100) DEFAULT NULL COMMENT '帖子标题',
  `content` text NOT NULL COMMENT '帖子内容',
  `content_type` tinyint DEFAULT 1 COMMENT '内容类型：1-图文，2-纯文字，3-视频',
  `images` json DEFAULT NULL COMMENT '图片列表，JSON数组',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频URL',
  `location` varchar(100) DEFAULT NULL COMMENT '发布位置',
  `plant_id` bigint DEFAULT NULL COMMENT '关联的用户植物ID',
  `topics` json DEFAULT NULL COMMENT '话题标签，JSON数组',
  `view_count` int DEFAULT 0 COMMENT '浏览次数',
  `like_count` int DEFAULT 0 COMMENT '点赞数',
  `comment_count` int DEFAULT 0 COMMENT '评论数',
  `share_count` int DEFAULT 0 COMMENT '分享数',
  `is_top` tinyint DEFAULT 0 COMMENT '是否置顶：0-否，1-是',
  `is_hot` tinyint DEFAULT 0 COMMENT '是否热门：0-否，1-是',
  `is_featured` tinyint DEFAULT 0 COMMENT '是否精选：0-否，1-是',
  `status` tinyint DEFAULT 1 COMMENT '状态：0-已删除，1-正常，2-审核中，3-审核不通过',
  `audit_reason` varchar(200) DEFAULT NULL COMMENT '审核原因',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_plant_id` (`plant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_like_count` (`like_count`),
  KEY `idx_is_hot_top` (`is_hot`, `is_top`),
  FULLTEXT KEY `ft_content` (`title`, `content`),
  CONSTRAINT `fk_community_posts_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_community_posts_plant_id` FOREIGN KEY (`plant_id`) REFERENCES `user_plants` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='社区帖子表';

-- 帖子点赞表
CREATE TABLE `post_likes` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `post_id` bigint NOT NULL COMMENT '帖子ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_user` (`post_id`, `user_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_post_likes_post_id` FOREIGN KEY (`post_id`) REFERENCES `community_posts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_post_likes_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帖子点赞表';

-- 帖子评论表
CREATE TABLE `post_comments` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `post_id` bigint NOT NULL COMMENT '帖子ID',
  `user_id` bigint NOT NULL COMMENT '评论用户ID',
  `parent_id` bigint DEFAULT NULL COMMENT '父评论ID',
  `reply_to_user_id` bigint DEFAULT NULL COMMENT '回复的用户ID',
  `content` text NOT NULL COMMENT '评论内容',
  `images` json DEFAULT NULL COMMENT '评论图片，JSON数组',
  `like_count` int DEFAULT 0 COMMENT '点赞数',
  `reply_count` int DEFAULT 0 COMMENT '回复数',
  `status` tinyint DEFAULT 1 COMMENT '状态：0-已删除，1-正常，2-审核中，3-审核不通过',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_time` (`created_time`),
  CONSTRAINT `fk_post_comments_post_id` FOREIGN KEY (`post_id`) REFERENCES `community_posts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_post_comments_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_post_comments_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `post_comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帖子评论表';

-- 用户关注表
CREATE TABLE `user_follows` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关注ID',
  `follower_id` bigint NOT NULL COMMENT '关注者ID',
  `following_id` bigint NOT NULL COMMENT '被关注者ID',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_follower_following` (`follower_id`, `following_id`),
  KEY `idx_following_id` (`following_id`),
  CONSTRAINT `fk_user_follows_follower_id` FOREIGN KEY (`follower_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_follows_following_id` FOREIGN KEY (`following_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注表';
```

#### 2.1.6 系统管理相关表

```sql
-- 系统配置表
CREATE TABLE `system_configs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型：string, number, boolean, json',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `group_name` varchar(50) DEFAULT 'default' COMMENT '配置分组',
  `is_public` tinyint DEFAULT 0 COMMENT '是否公开：0-否，1-是',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_group_name` (`group_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE `operation_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
  `user_type` varchar(20) DEFAULT 'user' COMMENT '用户类型：user-普通用户，admin-管理员',
  `module` varchar(50) NOT NULL COMMENT '操作模块',
  `action` varchar(50) NOT NULL COMMENT '操作动作',
  `description` varchar(200) DEFAULT NULL COMMENT '操作描述',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `request_url` varchar(255) DEFAULT NULL COMMENT '请求URL',
  `request_params` json DEFAULT NULL COMMENT '请求参数',
  `response_data` json DEFAULT NULL COMMENT '响应数据',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `execution_time` int DEFAULT NULL COMMENT '执行时间（毫秒）',
  `status` tinyint DEFAULT 1 COMMENT '状态：0-失败，1-成功',
  `error_message` text COMMENT '错误信息',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module_action` (`module`, `action`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- API调用统计表
CREATE TABLE `api_call_stats` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `date` date NOT NULL COMMENT '统计日期',
  `api_name` varchar(100) NOT NULL COMMENT 'API名称',
  `call_count` int DEFAULT 0 COMMENT '调用次数',
  `success_count` int DEFAULT 0 COMMENT '成功次数',
  `error_count` int DEFAULT 0 COMMENT '错误次数',
  `total_cost` decimal(10,6) DEFAULT 0.000000 COMMENT '总成本',
  `avg_response_time` int DEFAULT 0 COMMENT '平均响应时间（毫秒）',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date_api` (`date`, `api_name`),
  KEY `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API调用统计表';
```

### 2.2 表关联关系说明

#### 核心关联关系
1. **用户中心**: `users` ← `user_preferences`, `user_plants`, `plant_reminders`
2. **植物体系**: `user_plants`
3. **识别系统**: `users` ← `plant_identifications`
4. **提醒系统**: `reminder_types` ← `plant_reminders` ← `reminder_logs`
5. **社区系统**: `users` ← `community_posts` ← `post_comments`, `post_likes`
6. **关注系统**: `users` ← `user_follows` → `users`

#### 索引设计说明
- **主键索引**: 所有表都有自增主键
- **唯一索引**: 防止重复数据（如用户openid、关注关系等）
- **外键索引**: 提高关联查询性能
- **复合索引**: 优化常用查询组合
- **全文索引**: 支持内容搜索功能

---

## API接口文档

### 3.1 接口规范

#### 3.1.1 通用规范

**请求格式**:
- Content-Type: application/json
- 字符编码: UTF-8
- 请求方法: GET, POST, PUT, DELETE

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1640995200000
}
```

**状态码说明**:
- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

**认证方式**:
- 小程序端: JWT Token (Header: Authorization: Bearer {token})
- 管理端: Session + CSRF Token

#### 3.1.2 分页规范

**请求参数**:
```json
{
  "page": 1,
  "page_size": 20,
  "ordering": "-created_time"
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "count": 100,
    "next": "http://api.example.com/posts/?page=2",
    "previous": null,
    "results": []
  }
}
```

### 3.2 用户管理模块

#### 3.2.1 微信登录

**接口**: `POST /api/auth/wechat-login/`

**描述**: 微信小程序登录接口

**请求参数**:
```json
{
  "code": "string",           // 微信登录凭证
  "encrypted_data": "string", // 加密数据
  "iv": "string"             // 初始向量
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "nickname": "植物爱好者",
      "avatar_url": "https://example.com/avatar.jpg",
      "level": 1,
      "points": 100
    }
  }
}
```

#### 3.2.2 获取用户信息

**接口**: `GET /api/users/profile/`

**描述**: 获取当前用户详细信息

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "nickname": "植物爱好者",
    "avatar_url": "https://example.com/avatar.jpg",
    "gender": 1,
    "city": "北京",
    "level": 1,
    "points": 100,
    "plant_count": 5,
    "follow_count": 10,
    "fans_count": 8,
    "preferences": {
      "notification_enabled": true,
      "reminder_time": ["09:00", "18:00"],
      "interested_categories": [1, 2, 3]
    }
  }
}
```

#### 3.2.3 更新用户偏好设置

**接口**: `PUT /api/users/preferences/`

**描述**: 更新用户偏好设置

**请求参数**:
```json
{
  "notification_enabled": true,
  "reminder_time": ["09:00", "18:00"],
  "interested_categories": [1, 2, 3],
  "privacy_level": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "设置更新成功",
  "data": {
    "notification_enabled": true,
    "reminder_time": ["09:00", "18:00"],
    "interested_categories": [1, 2, 3],
    "privacy_level": 1
  }
}
```

### 3.3 植物识别模块

#### 3.3.1 植物识别

**接口**: `POST /api/identification/identify/`

**描述**: 上传图片进行植物识别

**请求参数** (multipart/form-data):
```
image: File                    // 植物图片文件
identification_type: int       // 识别类型：1-植物识别，2-健康评估
```

**响应示例**:
```json
{
  "code": 200,
  "message": "识别成功",
  "data": {
    "id": 123,
    "identification_type": 1,
    "result_confidence": 0.9567,
    "identified_plant_name": "绿萝",
    "scientific_name": "Epipremnum aureum",
    "common_names": ["绿萝", "黄金葛", "魔鬼藤"],
    "plant_details": {
      "family": "天南星科",
      "genus": "麒麟叶属",
      "description": "常绿藤本植物，叶片心形...",
      "care_tips": [
        "喜欢散射光环境",
        "保持土壤微湿",
        "适宜温度18-25℃"
      ]
    },
    "suggestions": [
      "这是一株健康的绿萝",
      "建议每周浇水1-2次",
      "可以适当修剪促进分枝"
    ],
    "image_url": "https://example.com/uploads/plant_123.jpg",
    "created_time": "2024-01-01T10:00:00Z"
  }
}
```

#### 3.3.2 健康评估

**接口**: `POST /api/identification/health-assessment/`

**描述**: 上传图片进行植物健康评估

**请求参数** (multipart/form-data):
```
image: File                    // 植物图片文件
```

**响应示例**:
```json
{
  "code": 200,
  "message": "评估完成",
  "data": {
    "id": 124,
    "identification_type": 2,
    "result_confidence": 0.8234,
    "health_assessment": {
      "overall_health": "良好",
      "health_score": 85,
      "issues_detected": [
        {
          "issue": "轻微叶片发黄",
          "confidence": 0.7,
          "severity": "轻微",
          "description": "部分叶片出现轻微发黄现象"
        }
      ]
    },
    "diseases_detected": [],
    "suggestions": [
      "减少浇水频率",
      "检查排水是否良好",
      "适当增加光照"
    ],
    "image_url": "https://example.com/uploads/health_124.jpg",
    "created_time": "2024-01-01T10:00:00Z"
  }
}
```

#### 3.3.3 识别历史记录

**接口**: `GET /api/identification/history/`

**描述**: 获取用户的识别历史记录

**请求参数**:
```
page: int = 1                  // 页码
page_size: int = 20           // 每页数量
identification_type: int      // 识别类型筛选（可选）
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "count": 50,
    "next": "http://api.example.com/identification/history/?page=2",
    "previous": null,
    "results": [
      {
        "id": 123,
        "identification_type": 1,
        "identified_plant_name": "绿萝",
        "result_confidence": 0.9567,
        "image_url": "https://example.com/uploads/plant_123.jpg",
        "created_time": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

### 3.4 用户植物管理模块

#### 3.4.1 添加植物

**接口**: `POST /api/plants/user-plants/`

**描述**: 用户添加新植物

**请求参数**:
```json
{
  "custom_name": "我的绿萝",
  "plant_name": "绿萝",
  "description": "办公桌上的小绿萝",
  "location": "办公室",
  "acquisition_date": "2024-01-01",
  "acquisition_method": "购买",
  "image_url": "https://example.com/plant.jpg"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "植物添加成功",
  "data": {
    "id": 1,
    "custom_name": "我的绿萝",
    "plant_name": "绿萝",
    "description": "办公桌上的小绿萝",
    "location": "办公室",
    "acquisition_date": "2024-01-01",
    "health_status": 1,
    "image_url": "https://example.com/plant.jpg",
    "created_time": "2024-01-01T10:00:00Z"
  }
}
```

#### 3.4.2 获取用户植物列表

**接口**: `GET /api/plants/user-plants/`

**描述**: 获取当前用户的植物列表

**请求参数**:
```
page: int = 1                  // 页码
page_size: int = 20           // 每页数量
health_status: int            // 健康状态筛选（可选）
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "count": 5,
    "results": [
      {
        "id": 1,
        "custom_name": "我的绿萝",
        "plant_name": "绿萝",
        "health_status": 1,
        "location": "办公室",
        "image_url": "https://example.com/plant.jpg",
        "reminder_count": 3,
        "next_reminder": "2024-01-02T09:00:00Z",
        "created_time": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

### 3.5 养护提醒模块

#### 3.5.1 创建提醒

**接口**: `POST /api/reminders/`

**描述**: 为植物创建养护提醒

**请求参数**:
```json
{
  "user_plant_id": 1,
  "reminder_type_id": 1,
  "frequency_days": 3,
  "reminder_times": ["09:00", "18:00"],
  "auto_adjust": true,
  "weather_sensitive": true
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "提醒创建成功",
  "data": {
    "id": 1,
    "user_plant_id": 1,
    "reminder_type": {
      "id": 1,
      "name": "浇水",
      "icon": "water"
    },
    "frequency_days": 3,
    "reminder_times": ["09:00", "18:00"],
    "next_reminder_time": "2024-01-04T09:00:00Z",
    "is_enabled": true,
    "auto_adjust": true,
    "weather_sensitive": true
  }
}
```

#### 3.5.2 获取提醒列表

**接口**: `GET /api/reminders/`

**描述**: 获取用户的提醒设置列表

**请求参数**:
```
user_plant_id: int            // 植物ID筛选（可选）
reminder_type_id: int         // 提醒类型筛选（可选）
is_enabled: bool              // 启用状态筛选（可选）
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "user_plant": {
        "id": 1,
        "custom_name": "我的绿萝",
        "image_url": "https://example.com/plant.jpg"
      },
      "reminder_type": {
        "id": 1,
        "name": "浇水",
        "icon": "water"
      },
      "frequency_days": 3,
      "reminder_times": ["09:00", "18:00"],
      "next_reminder_time": "2024-01-04T09:00:00Z",
      "is_enabled": true
    }
  ]
}
```

#### 3.5.3 完成提醒任务

**接口**: `POST /api/reminders/{reminder_id}/complete/`

**描述**: 标记提醒任务为已完成

**请求参数**:
```json
{
  "action": 1,                 // 1-已完成，2-跳过，3-延后
  "notes": "已浇水，植物状态良好"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "任务已完成",
  "data": {
    "next_reminder_time": "2024-01-07T09:00:00Z",
    "completed_time": "2024-01-04T09:30:00Z"
  }
}
```

### 3.6 社区分享模块

#### 3.6.1 发布帖子

**接口**: `POST /api/community/posts/`

**描述**: 发布社区帖子

**请求参数** (multipart/form-data):
```
title: string                  // 帖子标题（可选）
content: string               // 帖子内容
images: File[]                // 图片文件列表（可选）
plant_id: int                 // 关联植物ID（可选）
topics: string[]              // 话题标签列表（可选）
location: string              // 发布位置（可选）
```

**响应示例**:
```json
{
  "code": 200,
  "message": "发布成功",
  "data": {
    "id": 1,
    "title": "我的绿萝长得真好",
    "content": "经过三个月的精心照料，我的绿萝终于长得枝繁叶茂了！",
    "images": [
      "https://example.com/post_image_1.jpg",
      "https://example.com/post_image_2.jpg"
    ],
    "user": {
      "id": 1,
      "nickname": "植物爱好者",
      "avatar_url": "https://example.com/avatar.jpg"
    },
    "plant": {
      "id": 1,
      "custom_name": "我的绿萝",
      "image_url": "https://example.com/plant.jpg"
    },
    "topics": ["绿萝", "养护心得"],
    "location": "北京",
    "like_count": 0,
    "comment_count": 0,
    "created_time": "2024-01-01T10:00:00Z"
  }
}
```

#### 3.6.2 获取帖子列表

**接口**: `GET /api/community/posts/`

**描述**: 获取社区帖子列表

**请求参数**:
```
page: int = 1                  // 页码
page_size: int = 20           // 每页数量
ordering: string = "-created_time"  // 排序方式
topic: string                 // 话题筛选（可选）
user_id: int                  // 用户筛选（可选）
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "count": 100,
    "next": "http://api.example.com/community/posts/?page=2",
    "previous": null,
    "results": [
      {
        "id": 1,
        "title": "我的绿萝长得真好",
        "content": "经过三个月的精心照料...",
        "images": ["https://example.com/post_image_1.jpg"],
        "user": {
          "id": 1,
          "nickname": "植物爱好者",
          "avatar_url": "https://example.com/avatar.jpg"
        },
        "topics": ["绿萝", "养护心得"],
        "like_count": 15,
        "comment_count": 8,
        "is_liked": false,
        "created_time": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

#### 3.6.3 点赞帖子

**接口**: `POST /api/community/posts/{post_id}/like/`

**描述**: 点赞或取消点赞帖子

**响应示例**:
```json
{
  "code": 200,
  "message": "点赞成功",
  "data": {
    "is_liked": true,
    "like_count": 16
  }
}
```

#### 3.6.4 评论帖子

**接口**: `POST /api/community/posts/{post_id}/comments/`

**描述**: 对帖子进行评论

**请求参数**:
```json
{
  "content": "你的绿萝养得真好！有什么秘诀吗？",
  "parent_id": null,           // 父评论ID（回复评论时使用）
  "reply_to_user_id": null     // 回复的用户ID（回复评论时使用）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "评论成功",
  "data": {
    "id": 1,
    "content": "你的绿萝养得真好！有什么秘诀吗？",
    "user": {
      "id": 2,
      "nickname": "新手小白",
      "avatar_url": "https://example.com/avatar2.jpg"
    },
    "parent_id": null,
    "reply_to_user": null,
    "like_count": 0,
    "reply_count": 0,
    "created_time": "2024-01-01T11:00:00Z"
  }
}
```

#### 3.6.5 获取评论列表

**接口**: `GET /api/community/posts/{post_id}/comments/`

**描述**: 获取帖子的评论列表

**请求参数**:
```
page: int = 1                  // 页码
page_size: int = 20           // 每页数量
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "count": 8,
    "results": [
      {
        "id": 1,
        "content": "你的绿萝养得真好！有什么秘诀吗？",
        "user": {
          "id": 2,
          "nickname": "新手小白",
          "avatar_url": "https://example.com/avatar2.jpg"
        },
        "like_count": 2,
        "reply_count": 1,
        "replies": [
          {
            "id": 2,
            "content": "主要是要控制浇水量，不能太多",
            "user": {
              "id": 1,
              "nickname": "植物爱好者",
              "avatar_url": "https://example.com/avatar.jpg"
            },
            "created_time": "2024-01-01T11:30:00Z"
          }
        ],
        "created_time": "2024-01-01T11:00:00Z"
      }
    ]
  }
}
```

### 3.7 后端管理模块

#### 3.8.1 管理员登录

**接口**: `POST /api/admin/login/`

**描述**: 管理员登录接口

**请求参数**:
```json
{
  "username": "admin",
  "password": "password123",
  "captcha": "ABCD"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "name": "系统管理员",
      "permissions": ["user_manage", "content_manage"]
    }
  }
}
```

#### 3.8.2 用户管理

**接口**: `GET /api/admin/users/`

**描述**: 获取用户列表（管理员）

**请求参数**:
```
page: int = 1                  // 页码
page_size: int = 20           // 每页数量
search: string                // 搜索关键词（可选）
status: int                   // 状态筛选（可选）
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "count": 1000,
    "results": [
      {
        "id": 1,
        "nickname": "植物爱好者",
        "avatar_url": "https://example.com/avatar.jpg",
        "phone": "138****8888",
        "city": "北京",
        "level": 1,
        "points": 100,
        "plant_count": 5,
        "post_count": 12,
        "status": 1,
        "last_login_time": "2024-01-01T10:00:00Z",
        "created_time": "2023-12-01T10:00:00Z"
      }
    ]
  }
}
```

#### 3.8.3 内容审核

**接口**: `GET /api/admin/posts/pending/`

**描述**: 获取待审核的帖子列表

**请求参数**:
```
page: int = 1                  // 页码
page_size: int = 20           // 每页数量
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "count": 25,
    "results": [
      {
        "id": 1,
        "title": "我的植物分享",
        "content": "这是我养的植物...",
        "user": {
          "id": 1,
          "nickname": "植物爱好者"
        },
        "images": ["https://example.com/post_1.jpg"],
        "status": 2,
        "created_time": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

**接口**: `PUT /api/admin/posts/{post_id}/audit/`

**描述**: 审核帖子

**请求参数**:
```json
{
  "status": 1,                 // 1-通过，3-拒绝
  "audit_reason": "内容违规"   // 拒绝时的原因
}
```

#### 3.8.4 数据统计

**接口**: `GET /api/admin/statistics/overview/`

**描述**: 获取系统概览统计

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "user_stats": {
      "total_users": 10000,
      "active_users_today": 1500,
      "new_users_today": 50
    },
    "content_stats": {
      "total_posts": 5000,
      "posts_today": 120,
      "pending_audit": 25
    },
    "identification_stats": {
      "total_identifications": 15000,
      "identifications_today": 300,
      "success_rate": 0.92
    },
    "api_stats": {
      "plant_id_calls_today": 500,
      "api_cost_today": 25.50
    }
  }
}
```

#### 3.8.5 系统配置

**接口**: `GET /api/admin/configs/`

**描述**: 获取系统配置列表

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "config_key": "plant_id_api_key",
      "config_value": "your_api_key_here",
      "description": "Plant.id API密钥",
      "group_name": "third_party"
    }
  ]
}
```

**接口**: `PUT /api/admin/configs/{config_id}/`

**描述**: 更新系统配置

**请求参数**:
```json
{
  "config_value": "new_value"
}
```

---

## 4. 开发实施建议

### 4.1 开发阶段规划

#### Phase 1: 核心功能开发 (4-6周)
- 用户认证系统
- 植物识别功能
- 基础的用户植物管理
- 简单的养护提醒

#### Phase 2: 社交功能开发 (3-4周)
- 社区帖子发布
- 评论和点赞功能
- 用户关注系统

#### Phase 3: 完善和优化 (2-3周)
- 智能推荐算法
- 性能优化
- 管理后台完善

### 4.2 技术要点

#### 4.2.1 Plant.id API集成
```python
# 示例代码
import requests

class PlantIdService:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.plant.id/v2"

    def identify_plant(self, image_data):
        headers = {
            "Content-Type": "application/json",
            "Api-Key": self.api_key
        }

        data = {
            "images": [image_data],
            "modifiers": ["crops_fast", "similar_images"],
            "plant_details": ["common_names", "url"]
        }

        response = requests.post(
            f"{self.base_url}/identify",
            json=data,
            headers=headers
        )

        return response.json()
```

#### 4.2.2 微信小程序认证
```python
# 微信登录处理
class WeChatAuthService:
    def __init__(self, app_id, app_secret):
        self.app_id = app_id
        self.app_secret = app_secret

    def get_session_key(self, code):
        url = "https://api.weixin.qq.com/sns/jscode2session"
        params = {
            "appid": self.app_id,
            "secret": self.app_secret,
            "js_code": code,
            "grant_type": "authorization_code"
        }

        response = requests.get(url, params=params)
        return response.json()
```

#### 4.2.3 定时任务配置
```python
# Celery定时任务
from celery import Celery
from celery.schedules import crontab

app = Celery('plant_reminder')

@app.task
def send_plant_reminders():
    """发送植物养护提醒"""
    # 查询需要发送的提醒
    # 发送微信模板消息
    pass

# 定时任务配置
app.conf.beat_schedule = {
    'send-reminders': {
        'task': 'send_plant_reminders',
        'schedule': crontab(minute='*/30'),  # 每30分钟检查一次
    },
}
```

### 4.3 部署建议

#### 4.3.1 服务器配置
- **应用服务器**: 2核4G内存起步
- **数据库服务器**: 建议独立部署
- **Redis缓存**: 1G内存起步
- **文件存储**: 使用云存储服务

#### 4.3.2 监控和日志
- 使用ELK Stack进行日志收集
- 配置应用性能监控(APM)
- 设置关键指标告警

---

## 5. 总结

本开发文档为"植物语+"小程序项目提供了完整的技术实施方案，包括：

1. **完整的数据库设计**：涵盖用户、植物、识别、提醒、社区等所有核心功能
2. **详细的API接口规范**：提供了所有主要功能的接口定义和示例
3. **清晰的技术架构**：基于现有框架的合理技术选型

### 关键特性
- 🔐 **安全认证**：微信小程序授权登录
- 🌱 **智能识别**：Plant.id API集成
- ⏰ **智能提醒**：个性化养护提醒系统
- 👥 **社交互动**：完整的社区分享功能
- 🛠️ **管理后台**：完善的内容和用户管理

### 技术优势
- 基于成熟的Django + Vue技术栈
- 模块化设计，便于扩展
- 完善的API文档和数据库设计
- 考虑了性能优化和扩展性

建议按照阶段性开发计划实施，优先完成核心功能，再逐步完善高级特性。
```
```
```
```
