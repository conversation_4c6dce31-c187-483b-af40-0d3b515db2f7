"""
植物百科管理后台配置
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from .models import PlantEncyclopedia


@admin.register(PlantEncyclopedia)
class PlantEncyclopediaAdmin(admin.ModelAdmin):
    """植物百科管理"""
    
    list_display = [
        'name', 'scientific_name', 'category', 'status_display',
        'view_count', 'image_preview', 'source_site', 'create_datetime'
    ]
    
    list_filter = [
        'category', 'status', 'source_site', 'create_datetime'
    ]
    
    search_fields = [
        'name', 'scientific_name', 'description', 'search_keywords'
    ]
    
    list_editable = ['status']
    
    readonly_fields = [
        'view_count', 'create_datetime', 'update_datetime', 'creator', 'modifier'
    ]
    
    ordering = ['-create_datetime']
    
    list_per_page = 20
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'scientific_name', 'category', 'status')
        }),
        ('分类学信息', {
            'fields': ('family', 'genus'),
            'classes': ('collapse',)
        }),
        ('详细描述', {
            'fields': ('description', 'care_tips', 'growth_habit', 'flowering_period')
        }),
        ('图片信息', {
            'fields': ('main_image', 'images'),
            'classes': ('collapse',)
        }),
        ('来源信息', {
            'fields': ('source_url', 'source_site'),
            'classes': ('collapse',)
        }),
        ('SEO信息', {
            'fields': ('tags', 'search_keywords'),
            'classes': ('collapse',)
        }),
        ('统计信息', {
            'fields': ('view_count', 'create_datetime', 'update_datetime', 'creator', 'modifier'),
            'classes': ('collapse',)
        }),
    )
    
    def status_display(self, obj):
        """状态显示"""
        status_colors = {
            0: 'orange',  # 草稿
            1: 'green',   # 已发布
            2: 'red',     # 已下架
        }
        color = status_colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def image_preview(self, obj):
        """图片预览"""
        main_image = obj.get_main_image()
        if main_image:
            return format_html(
                '<img src="{}" width="50" height="50" style="object-fit: cover; border-radius: 4px;" />',
                main_image
            )
        return '无图片'
    image_preview.short_description = '图片预览'
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('creator', 'modifier')
    
    def save_model(self, request, obj, form, change):
        """保存时设置创建者和修改者"""
        if not change:  # 新建
            obj.creator = request.user
        obj.modifier = request.user
        super().save_model(request, obj, form, change)
    
    actions = ['make_published', 'make_draft', 'make_offline']
    
    def make_published(self, request, queryset):
        """批量发布"""
        updated = queryset.update(status=1)
        self.message_user(request, f'成功发布 {updated} 个植物百科条目')
    make_published.short_description = '发布选中的植物百科'
    
    def make_draft(self, request, queryset):
        """批量设为草稿"""
        updated = queryset.update(status=0)
        self.message_user(request, f'成功设置 {updated} 个植物百科条目为草稿')
    make_draft.short_description = '设为草稿'
    
    def make_offline(self, request, queryset):
        """批量下架"""
        updated = queryset.update(status=2)
        self.message_user(request, f'成功下架 {updated} 个植物百科条目')
    make_offline.short_description = '下架选中的植物百科'
