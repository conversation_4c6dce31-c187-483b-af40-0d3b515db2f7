{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/cameligo/cameligo.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/cameligo/cameligo.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' },\n    { open: \"(*\", close: \"*)\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' },\n    { open: \"(*\", close: \"*)\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".cameligo\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  keywords: [\n    \"abs\",\n    \"assert\",\n    \"block\",\n    \"Bytes\",\n    \"case\",\n    \"Crypto\",\n    \"Current\",\n    \"else\",\n    \"failwith\",\n    \"false\",\n    \"for\",\n    \"fun\",\n    \"if\",\n    \"in\",\n    \"let\",\n    \"let%entry\",\n    \"let%init\",\n    \"List\",\n    \"list\",\n    \"Map\",\n    \"map\",\n    \"match\",\n    \"match%nat\",\n    \"mod\",\n    \"not\",\n    \"operation\",\n    \"Operation\",\n    \"of\",\n    \"record\",\n    \"Set\",\n    \"set\",\n    \"sender\",\n    \"skip\",\n    \"source\",\n    \"String\",\n    \"then\",\n    \"to\",\n    \"true\",\n    \"type\",\n    \"with\"\n  ],\n  typeKeywords: [\"int\", \"unit\", \"string\", \"tz\", \"nat\", \"bool\"],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \"<=\",\n    \">=\",\n    \"<>\",\n    \":\",\n    \":=\",\n    \"and\",\n    \"mod\",\n    \"or\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"@\",\n    \"&\",\n    \"^\",\n    \"%\",\n    \"->\",\n    \"<-\",\n    \"&&\",\n    \"||\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><:@\\^&|+\\-*\\/\\^%]+/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /[a-zA-Z_][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\$[0-9a-fA-F]{1,16}/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'/, \"string\", \"@string\"],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/'/, \"string.invalid\"],\n      [/\\#\\d+/, \"string\"]\n    ],\n    /* */\n    comment: [\n      [/[^\\(\\*]+/, \"comment\"],\n      //[/\\(\\*/,    'comment', '@push' ],    // nested comment  not allowed :-(\n      [/\\*\\)/, \"comment\", \"@pop\"],\n      [/\\(\\*/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\']+/, \"string\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\(\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,EAC5B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACpD;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc,CAAC,OAAO,QAAQ,UAAU,MAAM,OAAO,MAAM;AAAA,EAC3D,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,uBAAuB,YAAY;AAAA,MACpC,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,MAEpC,CAAC,KAAK,UAAU,SAAS;AAAA;AAAA,MAEzB,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,SAAS,QAAQ;AAAA,IACpB;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,CAAC,YAAY,SAAS;AAAA;AAAA,MAEtB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,QAAQ,SAAS;AAAA,IACpB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,EACF;AACF;", "names": []}