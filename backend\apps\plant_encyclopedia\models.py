"""
植物百科模型
"""
from django.db import models
from django.contrib.auth import get_user_model
from dvadmin.utils.models import CoreModel

User = get_user_model()


class PlantEncyclopedia(CoreModel):
    """
    植物百科模型 - 基于huabaike_spider.py爬取的数据结构设计
    """
    
    # 基本信息
    name = models.CharField(max_length=100, verbose_name='植物名称', help_text='植物名称')
    scientific_name = models.CharField(max_length=150, blank=True, verbose_name='学名', help_text='植物学名')
    
    # 分类信息
    CATEGORY_CHOICES = [
        ('草本植物', '草本植物'),
        ('木本植物', '木本植物'),
        ('藤本植物', '藤本植物'),
        ('兰科植物', '兰科植物'),
        ('水生植物', '水生植物'),
        ('球根植物', '球根植物'),
        ('宿根植物', '宿根植物'),
    ]
    category = models.CharField(
        max_length=20, 
        choices=CATEGORY_CHOICES, 
        verbose_name='植物分类',
        help_text='植物分类'
    )
    
    # 分类学信息
    family = models.CharField(max_length=50, blank=True, verbose_name='科', help_text='植物科名')
    genus = models.CharField(max_length=50, blank=True, verbose_name='属', help_text='植物属名')
    
    # 详细描述 - 存储HTML格式的内容
    description = models.TextField(blank=True, verbose_name='植物描述', help_text='植物详细描述，支持HTML格式')
    care_tips = models.TextField(blank=True, verbose_name='养护技巧', help_text='植物养护技巧')
    growth_habit = models.TextField(blank=True, verbose_name='生长习性', help_text='植物生长习性')
    flowering_period = models.CharField(max_length=100, blank=True, verbose_name='花期', help_text='植物花期')
    
    # 图片信息 - 存储JSON格式的图片URL列表
    images = models.JSONField(default=list, blank=True, verbose_name='图片列表', help_text='植物图片URL列表')
    main_image = models.URLField(blank=True, verbose_name='主图', help_text='植物主图URL')
    
    # 来源信息
    source_url = models.URLField(blank=True, verbose_name='来源URL', help_text='数据来源URL')
    source_site = models.CharField(max_length=50, default='花百科', verbose_name='来源网站', help_text='数据来源网站')
    
    # 状态和统计
    STATUS_CHOICES = [
        (0, '草稿'),
        (1, '已发布'),
        (2, '已下架'),
    ]
    status = models.IntegerField(choices=STATUS_CHOICES, default=1, verbose_name='状态', help_text='发布状态')
    view_count = models.PositiveIntegerField(default=0, verbose_name='浏览次数', help_text='浏览次数')
    
    # 搜索和SEO
    tags = models.JSONField(default=list, blank=True, verbose_name='标签', help_text='植物标签列表')
    search_keywords = models.CharField(max_length=200, blank=True, verbose_name='搜索关键词', help_text='搜索关键词')
    
    class Meta:
        db_table = 'plant_encyclopedia'
        verbose_name = '植物百科'
        verbose_name_plural = '植物百科'
        ordering = ['-create_datetime']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['category']),
            models.Index(fields=['status']),
            models.Index(fields=['view_count']),
            models.Index(fields=['create_datetime']),
        ]
    
    def __str__(self):
        return self.name
    
    def increment_view_count(self):
        """增加浏览次数"""
        self.view_count += 1
        self.save(update_fields=['view_count'])
    
    def get_main_image(self):
        """获取主图URL"""
        if self.main_image:
            return self.main_image
        elif self.images and len(self.images) > 0:
            return self.images[0]
        return None
    
    def get_short_description(self, length=100):
        """获取简短描述（去除HTML标签）"""
        if not self.description:
            return ''
        
        # 简单的HTML标签去除
        import re
        clean_text = re.sub(r'<[^>]+>', '', self.description)
        if len(clean_text) <= length:
            return clean_text
        return clean_text[:length] + '...'
    
    @property
    def image_count(self):
        """图片数量"""
        return len(self.images) if self.images else 0
