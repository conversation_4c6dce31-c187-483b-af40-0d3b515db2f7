import{a as i,E as _}from"./index.GuQX7xXE.js";import{createCrudOptions as x}from"./crud.CBfqHRU6.js";import{d as h,a as w,o as B,b as n,g,e as C,w as R,f as V,n as k,u as v}from"./vue.zNq9Glab.js";import"./dictionary.DBJS--kg.js";const z=h({__name:"index",setup(y,{expose:s}){const e=w(!1),a=r=>{_.confirm("您确定要关闭?",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{r()}).catch(()=>{})},{crudBinding:c,crudRef:d,crudExpose:o}=i({createCrudOptions:x,context:{}}),{setSearchFormData:l,doRefresh:u}=o;return s({drawer:e,setSearchFormData:l,doRefresh:u}),B(()=>{o.doRefresh()}),(r,t)=>{const f=n("fs-crud"),m=n("el-drawer");return C(),g(m,{size:"70%",modelValue:e.value,"onUpdate:modelValue":t[0]||(t[0]=p=>e.value=p),direction:"rtl","destroy-on-close":"","before-close":a},{default:R(()=>[V(f,k({ref_key:"crudRef",ref:d},v(c)),null,16)]),_:1},8,["modelValue"])}}});export{z as default};
