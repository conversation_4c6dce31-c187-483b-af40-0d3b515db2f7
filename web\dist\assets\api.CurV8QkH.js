import{r as e}from"./index.GuQX7xXE.js";import"./vue.zNq9Glab.js";const r="/api/dvadmin_celery/task_detail/";function a(t){return e({url:r,method:"get",params:t})}function d(t){return e({url:r+t,method:"get"})}function o(t){return e({url:r,method:"post",data:t})}function i(t){return e({url:r+t.id+"/",method:"put",data:t})}function m(t){return e({url:r+t+"/",method:"delete",data:{id:t}})}export{o as AddObj,m as DelObj,a as GetList,d as GetObj,i as UpdateObj,r as apiPrefix};
