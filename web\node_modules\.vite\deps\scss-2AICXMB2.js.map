{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/scss/scss.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scss/scss.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|([@$#!.:]?[\\w-?]+%?)|[@#!.]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"],\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".scss\",\n  ws: \"[ \t\\n\\r\\f]*\",\n  // whitespaces (referenced in several rules)\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [{ include: \"@selector\" }],\n    selector: [\n      { include: \"@comments\" },\n      { include: \"@import\" },\n      { include: \"@variabledeclaration\" },\n      { include: \"@warndebug\" },\n      // sass: log statements\n      [\"[@](include)\", { token: \"keyword\", next: \"@includedeclaration\" }],\n      // sass: include statement\n      [\n        \"[@](keyframes|-webkit-keyframes|-moz-keyframes|-o-keyframes)\",\n        { token: \"keyword\", next: \"@keyframedeclaration\" }\n      ],\n      [\"[@](page|content|font-face|-moz-document)\", { token: \"keyword\" }],\n      // sass: placeholder for includes\n      [\"[@](charset|namespace)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\"[@](function)\", { token: \"keyword\", next: \"@functiondeclaration\" }],\n      [\"[@](mixin)\", { token: \"keyword\", next: \"@mixindeclaration\" }],\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"meta\", next: \"@urldeclaration\" }],\n      { include: \"@controlstatement\" },\n      // sass control statements\n      { include: \"@selectorname\" },\n      [\"[&\\\\*]\", \"tag\"],\n      // selector symbols\n      [\"[>\\\\+,]\", \"delimiter\"],\n      // selector operators\n      [\"\\\\[\", { token: \"delimiter.bracket\", next: \"@selectorattribute\" }],\n      [\"{\", { token: \"delimiter.curly\", next: \"@selectorbody\" }]\n    ],\n    selectorbody: [\n      [\"[*_]?@identifier@ws:(?=(\\\\s|\\\\d|[^{;}]*[;}]))\", \"attribute.name\", \"@rulevalue\"],\n      // rule definition: to distinguish from a nested selector check for whitespace, number or a semicolon\n      { include: \"@selector\" },\n      // sass: nested selectors\n      [\"[@](extend)\", { token: \"keyword\", next: \"@extendbody\" }],\n      // sass: extend other selectors\n      [\"[@](return)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    selectorname: [\n      [\"#{\", { token: \"meta\", next: \"@variableinterpolation\" }],\n      // sass: interpolation\n      [\"(\\\\.|#(?=[^{])|%|(@identifier)|:)+\", \"tag\"]\n      // selector (.foo, div, ...)\n    ],\n    selectorattribute: [{ include: \"@term\" }, [\"]\", { token: \"delimiter.bracket\", next: \"@pop\" }]],\n    term: [\n      { include: \"@comments\" },\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"meta\", next: \"@urldeclaration\" }],\n      { include: \"@functioninvocation\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@variablereference\" },\n      [\"(and\\\\b|or\\\\b|not\\\\b)\", \"operator\"],\n      { include: \"@name\" },\n      [\"([<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,])\", \"operator\"],\n      [\",\", \"delimiter\"],\n      [\"!default\", \"literal\"],\n      [\"\\\\(\", { token: \"delimiter.parenthesis\", next: \"@parenthizedterm\" }]\n    ],\n    rulevalue: [\n      { include: \"@term\" },\n      [\"!important\", \"literal\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@nestedproperty\" }],\n      // sass: nested properties\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    nestedproperty: [\n      [\"[*_]?@identifier@ws:\", \"attribute.name\", \"@rulevalue\"],\n      { include: \"@comments\" },\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    warndebug: [[\"[@](warn|debug)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    import: [[\"[@](import)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    variabledeclaration: [\n      // sass variables\n      [\"\\\\$@identifier@ws:\", \"variable.decl\", \"@declarationbody\"]\n    ],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    parenthizedterm: [\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    declarationbody: [\n      { include: \"@term\" },\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    extendbody: [\n      { include: \"@selectorname\" },\n      [\"!optional\", \"literal\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    variablereference: [\n      // sass variable reference\n      [\"\\\\$@identifier\", \"variable.ref\"],\n      [\"\\\\.\\\\.\\\\.\", \"operator\"],\n      // var args in reference\n      [\"#{\", { token: \"meta\", next: \"@variableinterpolation\" }]\n      // sass var resolve\n    ],\n    variableinterpolation: [\n      { include: \"@variablereference\" },\n      [\"}\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    name: [[\"@identifier\", \"attribute.value\"]],\n    numbers: [\n      [\"(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"number.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"number\",\n        \"@pop\"\n      ]\n    ],\n    functiondeclaration: [\n      [\"@identifier@ws\\\\(\", { token: \"meta\", next: \"@parameterdeclaration\" }],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@functionbody\" }]\n    ],\n    mixindeclaration: [\n      // mixin with parameters\n      [\"@identifier@ws\\\\(\", { token: \"meta\", next: \"@parameterdeclaration\" }],\n      // mixin without parameters\n      [\"@identifier\", \"meta\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    parameterdeclaration: [\n      [\"\\\\$@identifier@ws:\", \"variable.decl\"],\n      [\"\\\\.\\\\.\\\\.\", \"operator\"],\n      // var args in declaration\n      [\",\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    includedeclaration: [\n      { include: \"@functioninvocation\" },\n      [\"@identifier\", \"meta\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }],\n      // missing semicolon\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    keyframedeclaration: [\n      [\"@identifier\", \"meta\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@keyframebody\" }]\n    ],\n    keyframebody: [\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.curly\", next: \"@selectorbody\" }],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    controlstatement: [\n      [\n        \"[@](if|else|for|while|each|media)\",\n        { token: \"keyword.flow\", next: \"@controlstatementdeclaration\" }\n      ]\n    ],\n    controlstatementdeclaration: [\n      [\"(in|from|through|if|to)\\\\b\", { token: \"keyword.flow\" }],\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    functionbody: [\n      [\"[@](return)\", { token: \"keyword\" }],\n      { include: \"@variabledeclaration\" },\n      { include: \"@term\" },\n      { include: \"@controlstatement\" },\n      [\";\", \"delimiter\"],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    functioninvocation: [[\"@identifier\\\\(\", { token: \"meta\", next: \"@functionarguments\" }]],\n    functionarguments: [\n      [\"\\\\$@identifier@ws:\", \"attribute.name\"],\n      [\"[,]\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    strings: [\n      ['~?\"', { token: \"string.delimiter\", next: \"@stringenddoublequote\" }],\n      [\"~?'\", { token: \"string.delimiter\", next: \"@stringendquote\" }]\n    ],\n    stringenddoublequote: [\n      [\"\\\\\\\\.\", \"string\"],\n      ['\"', { token: \"string.delimiter\", next: \"@pop\" }],\n      [\".\", \"string\"]\n    ],\n    stringendquote: [\n      [\"\\\\\\\\.\", \"string\"],\n      [\"'\", { token: \"string.delimiter\", next: \"@pop\" }],\n      [\".\", \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,cAAc,CAAC,MAAM,IAAI;AAAA,IACzB,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,8CAA8C;AAAA,MAChE,KAAK,IAAI,OAAO,sCAAsC;AAAA,IACxD;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,IAAI;AAAA;AAAA,EAEJ,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,IACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACpD;AAAA,EACA,WAAW;AAAA,IACT,MAAM,CAAC,EAAE,SAAS,YAAY,CAAC;AAAA,IAC/B,UAAU;AAAA,MACR,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,uBAAuB;AAAA,MAClC,EAAE,SAAS,aAAa;AAAA;AAAA,MAExB,CAAC,gBAAgB,EAAE,OAAO,WAAW,MAAM,sBAAsB,CAAC;AAAA;AAAA,MAElE;AAAA,QACE;AAAA,QACA,EAAE,OAAO,WAAW,MAAM,uBAAuB;AAAA,MACnD;AAAA,MACA,CAAC,6CAA6C,EAAE,OAAO,UAAU,CAAC;AAAA;AAAA,MAElE,CAAC,0BAA0B,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC;AAAA,MACzE,CAAC,iBAAiB,EAAE,OAAO,WAAW,MAAM,uBAAuB,CAAC;AAAA,MACpE,CAAC,cAAc,EAAE,OAAO,WAAW,MAAM,oBAAoB,CAAC;AAAA,MAC9D,CAAC,sBAAsB,EAAE,OAAO,QAAQ,MAAM,kBAAkB,CAAC;AAAA,MACjE,EAAE,SAAS,oBAAoB;AAAA;AAAA,MAE/B,EAAE,SAAS,gBAAgB;AAAA,MAC3B,CAAC,UAAU,KAAK;AAAA;AAAA,MAEhB,CAAC,WAAW,WAAW;AAAA;AAAA,MAEvB,CAAC,OAAO,EAAE,OAAO,qBAAqB,MAAM,qBAAqB,CAAC;AAAA,MAClE,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,gBAAgB,CAAC;AAAA,IAC3D;AAAA,IACA,cAAc;AAAA,MACZ,CAAC,iDAAiD,kBAAkB,YAAY;AAAA;AAAA,MAEhF,EAAE,SAAS,YAAY;AAAA;AAAA,MAEvB,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,cAAc,CAAC;AAAA;AAAA,MAEzD,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC;AAAA,MAC9D,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,IAClD;AAAA,IACA,cAAc;AAAA,MACZ,CAAC,MAAM,EAAE,OAAO,QAAQ,MAAM,yBAAyB,CAAC;AAAA;AAAA,MAExD,CAAC,sCAAsC,KAAK;AAAA;AAAA,IAE9C;AAAA,IACA,mBAAmB,CAAC,EAAE,SAAS,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,OAAO,CAAC,CAAC;AAAA,IAC7F,MAAM;AAAA,MACJ,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,sBAAsB,EAAE,OAAO,QAAQ,MAAM,kBAAkB,CAAC;AAAA,MACjE,EAAE,SAAS,sBAAsB;AAAA,MACjC,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,qBAAqB;AAAA,MAChC,CAAC,yBAAyB,UAAU;AAAA,MACpC,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,iCAAiC,UAAU;AAAA,MAC5C,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY,SAAS;AAAA,MACtB,CAAC,OAAO,EAAE,OAAO,yBAAyB,MAAM,mBAAmB,CAAC;AAAA,IACtE;AAAA,IACA,WAAW;AAAA,MACT,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,cAAc,SAAS;AAAA,MACxB,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,kBAAkB,CAAC;AAAA;AAAA,MAE/D,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,IAEvC;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,wBAAwB,kBAAkB,YAAY;AAAA,MACvD,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,IAClD;AAAA,IACA,WAAW,CAAC,CAAC,mBAAmB,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC,CAAC;AAAA,IAC/E,QAAQ,CAAC,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC,CAAC;AAAA,IACxE,qBAAqB;AAAA;AAAA,MAEnB,CAAC,sBAAsB,iBAAiB,kBAAkB;AAAA,IAC5D;AAAA,IACA,gBAAgB;AAAA,MACd,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,IACzC;AAAA,IACA,iBAAiB;AAAA,MACf,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,yBAAyB,MAAM,OAAO,CAAC;AAAA,IAC1D;AAAA,IACA,iBAAiB;AAAA,MACf,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,IAEvC;AAAA,IACA,YAAY;AAAA,MACV,EAAE,SAAS,gBAAgB;AAAA,MAC3B,CAAC,aAAa,SAAS;AAAA,MACvB,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,IAEvC;AAAA,IACA,mBAAmB;AAAA;AAAA,MAEjB,CAAC,kBAAkB,cAAc;AAAA,MACjC,CAAC,aAAa,UAAU;AAAA;AAAA,MAExB,CAAC,MAAM,EAAE,OAAO,QAAQ,MAAM,yBAAyB,CAAC;AAAA;AAAA,IAE1D;AAAA,IACA,uBAAuB;AAAA,MACrB,EAAE,SAAS,qBAAqB;AAAA,MAChC,CAAC,KAAK,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,IACvC;AAAA,IACA,UAAU;AAAA,MACR,CAAC,UAAU,WAAW,UAAU;AAAA,MAChC,CAAC,aAAa,SAAS;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,UAAU,WAAW,MAAM;AAAA,MAC5B,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,MAAM,CAAC,CAAC,eAAe,iBAAiB,CAAC;AAAA,IACzC,SAAS;AAAA,MACP,CAAC,oCAAoC,EAAE,OAAO,UAAU,MAAM,SAAS,CAAC;AAAA,MACxE,CAAC,yBAAyB,YAAY;AAAA,IACxC;AAAA,IACA,OAAO;AAAA,MACL;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB,CAAC,qBAAqB,EAAE,OAAO,QAAQ,MAAM,wBAAwB,CAAC;AAAA,MACtE,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,IAC/D;AAAA,IACA,kBAAkB;AAAA;AAAA,MAEhB,CAAC,qBAAqB,EAAE,OAAO,QAAQ,MAAM,wBAAwB,CAAC;AAAA;AAAA,MAEtE,CAAC,eAAe,MAAM;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,IAC/D;AAAA,IACA,sBAAsB;AAAA,MACpB,CAAC,sBAAsB,eAAe;AAAA,MACtC,CAAC,aAAa,UAAU;AAAA;AAAA,MAExB,CAAC,KAAK,WAAW;AAAA,MACjB,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,IACzC;AAAA,IACA,oBAAoB;AAAA,MAClB,EAAE,SAAS,sBAAsB;AAAA,MACjC,CAAC,eAAe,MAAM;AAAA,MACtB,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,MAErC,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,IAC/D;AAAA,IACA,qBAAqB;AAAA,MACnB,CAAC,eAAe,MAAM;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,IAC/D;AAAA,IACA,cAAc;AAAA,MACZ,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,gBAAgB,CAAC;AAAA,MACzD,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,IAClD;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE;AAAA,QACA,EAAE,OAAO,gBAAgB,MAAM,+BAA+B;AAAA,MAChE;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B,CAAC,8BAA8B,EAAE,OAAO,eAAe,CAAC;AAAA,MACxD,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,IAC/D;AAAA,IACA,cAAc;AAAA,MACZ,CAAC,eAAe,EAAE,OAAO,UAAU,CAAC;AAAA,MACpC,EAAE,SAAS,uBAAuB;AAAA,MAClC,EAAE,SAAS,QAAQ;AAAA,MACnB,EAAE,SAAS,oBAAoB;AAAA,MAC/B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,IAClD;AAAA,IACA,oBAAoB,CAAC,CAAC,kBAAkB,EAAE,OAAO,QAAQ,MAAM,qBAAqB,CAAC,CAAC;AAAA,IACtF,mBAAmB;AAAA,MACjB,CAAC,sBAAsB,gBAAgB;AAAA,MACvC,CAAC,OAAO,WAAW;AAAA,MACnB,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,IACzC;AAAA,IACA,SAAS;AAAA,MACP,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,wBAAwB,CAAC;AAAA,MACpE,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,kBAAkB,CAAC;AAAA,IAChE;AAAA,IACA,sBAAsB;AAAA,MACpB,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,EACF;AACF;", "names": []}