const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.Bg8y3cFr.js","assets/vue.zNq9Glab.js","assets/index.GuQX7xXE.js","assets/index.WGbL7eFL.css","assets/logo-mini.DjPeV5ul.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/index.BVXIswWj.css","assets/vertical.CkoszIad.js"])))=>i.map(i=>d[i]);
import{I as O,u as q,J as z,l as u,K as H,_ as S}from"./index.GuQX7xXE.js";import{d as T,Q as $,a as F,M as p,r as j,p as x,N as J,m as w,b as A,A as K,D as Q,u as v,c as G,e as R,f as y,w as M,g as U,h as X,P as I,v as Y}from"./vue.zNq9Glab.js";const Z={class:"h100"},ee=T({name:"layoutAside"}),ne=T({...ee,setup(te){const B=$(),E=I(()=>S(()=>import("./index.Bg8y3cFr.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),V=I(()=>S(()=>import("./vertical.CkoszIad.js"),__vite__mapDeps([7,2,1,3]))),d=F(),f=F(0),_=O(),W=q(),k=z(),{routesList:r}=p(_),{themeConfig:n}=p(W),{isTagsViewCurrenFull:D}=p(k),l=j({menuList:[],clientWidth:0}),N=x(()=>{const{layout:e,isCollapse:t,menuBar:s}=n.value,o=["#FFFFFF","#FFF","#fff","#ffffff"].includes(s)?"layout-el-aside-br-color":"";if(l.clientWidth<=1e3)if(t){document.body.setAttribute("class","el-popup-parent--hidden");const g=document.querySelector(".layout-container"),a=document.createElement("div");return a.setAttribute("class","layout-aside-mobile-mode"),g.appendChild(a),a.addEventListener("click",m),[o,"layout-aside-mobile","layout-aside-mobile-open"]}else return m(),[o,"layout-aside-mobile","layout-aside-mobile-close"];else return e==="columns"?t?[o,"layout-aside-pc-1"]:[o,"layout-aside-pc-220"]:t?[o,"layout-aside-pc-64"]:[o,"layout-aside-pc-220"]}),P=x(()=>{let{layout:e,isShowLogo:t}=n.value;return t&&e==="defaults"||t&&e==="columns"}),m=()=>{const e=document.querySelector(".layout-aside-mobile-mode");e==null||e.setAttribute("style","animation: error-img-two 0.3s"),setTimeout(()=>{var s;(s=e==null?void 0:e.parentNode)==null||s.removeChild(e)},300),document.body.clientWidth<1e3&&(n.value.isCollapse=!1),document.body.setAttribute("class","")},C=(e,t)=>{for(let s=0;s<e.length;s++){const i=e[s];if(i.children&&i.children.length>0&&(i.children.findIndex(a=>a.path===t)!==-1||C(i.children,t)!==null))return s}return null},c=(e="")=>{if(n.value.layout==="columns")return!1;let{layout:t,isClassicSplitMenu:s}=n.value;t==="classic"&&s?(f.value=C(r.value,e||B.path)||0,l.menuList=h(r.value[f.value].children||[r.value[f.value]])):l.menuList=h(r.value)},h=e=>e.filter(t=>{var s;return!((s=t.meta)!=null&&s.isHide)}).map(t=>(t=Object.assign({},t),t.children&&(t.children=h(t.children)),t)),b=e=>{l.clientWidth=e},L=e=>{let{layout:t}=n.value;if(t!=="columns")return!1;e||u.emit("restoreDefault"),_.setColumnsMenuHover(e)};return J(()=>{b(document.body.clientWidth),c(),u.on("setSendColumnsChildren",e=>{l.menuList=e.children}),u.on("setSendClassicChildren",e=>{let{layout:t,isClassicSplitMenu:s}=n.value;t==="classic"&&s&&(l.menuList=[],c(e.path))}),u.on("getBreadcrumbIndexSetFilterRoutes",()=>{c()}),u.on("layoutMobileResize",e=>{b(e.clientWidth),m()})}),w(n.value,e=>{e.isShowLogoChange!==e.isShowLogo&&d.value&&d.value.update()}),w(H.state,e=>{let{layout:t,isClassicSplitMenu:s}=e.themeConfig.themeConfig;if(t==="classic"&&s)return!1;c()},{deep:!0}),(e,t)=>{const s=A("el-scrollbar"),i=A("el-aside");return K((R(),G("div",Z,[y(i,{class:Y(["layout-aside",N.value])},{default:M(()=>[P.value?(R(),U(v(E),{key:0})):X("",!0),y(s,{class:"flex-auto",ref_key:"layoutAsideScrollbarRef",ref:d,onMouseenter:t[0]||(t[0]=o=>L(!0)),onMouseleave:t[1]||(t[1]=o=>L(!1))},{default:M(()=>[y(v(V),{menuList:l.menuList},null,8,["menuList"])]),_:1},512)]),_:1},8,["class"])],512)),[[Q,!v(D)]])}}});export{ne as default};
