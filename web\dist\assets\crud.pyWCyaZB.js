import{v as s}from"./index.GuQX7xXE.js";import{DelObj as o,UpdateObj as l,AddObj as u,GetList as i}from"./api.TEviRI0f.js";import{a as t}from"./authFunction.BcROZVTX.js";import{p as d}from"./vue.zNq9Glab.js";const y=function({crudExpose:c,context:n}){return{crudOptions:{request:{pageRequest:async e=>await i(e),addRequest:async({form:e})=>await u(e),editRequest:async({form:e,row:r})=>(e.id=r.id,await l(e)),delRequest:async({row:e})=>await o(e.id)},pagination:{show:!0},table:{show:!1},toolbar:{compact:!1},actionbar:{buttons:{add:{show:t("role:Create")}}},rowHandle:{align:"center",fixed:"right",width:d(()=>t("role:AuthorizedAdd")||t("role:AuthorizedSearch")?420:320),buttons:{view:{show:!0},edit:{show:t("role:Update")},remove:{show:t("role:Delete")}}},form:{col:{span:24},labelWidth:"100px",wrapper:{is:"el-dialog",width:"900px"}},columns:{_index:{title:"序号",form:{show:!1},column:{type:"index",align:"center",width:"70px",columnSetDisabled:!0}},id:{title:"ID",column:{show:!1},search:{show:!1},form:{show:!1}},name:{title:"任务名称",search:{show:!0},column:{minWidth:120,sortable:"custom"},form:{rules:[{required:!0,message:"任务名称必填"}],component:{placeholder:"请输入任务名称"}}},task:{title:"执行任务",type:"dict-select",dict:s({url:"/api/dvadmin_celery/task/job_list/?limit=999",value:"label",label:"label"}),search:{show:!0},column:{minWidth:120,sortable:"custom",columnSetDisabled:!0},form:{rules:[{required:!0,message:"执行任务必填"}],component:{placeholder:"输入执行任务"}},valueBuilder(e){const{row:r,key:a}=e;return r[a]}},last_run_at:{title:"最后运行时间",search:{show:!1},type:" datetime",form:{show:!1,rules:[{required:!0,message:"最后运行时间必填"}]}},description:{title:"备注",search:{show:!1},type:" datetime",form:{show:!1,rules:[{required:!0,message:"备注必填"}]}},cron:{title:"表达式",search:{show:!1},type:"number",column:{minWidth:90,sortable:"custom"},form:{rules:[{required:!0,message:"表达式必填"}],value:"1-15 1 * * *"}},kwargs:{title:"请求参数",search:{show:!1},type:"text",column:{minWidth:90,sortable:"custom"}},enabled:{title:"状态",search:{show:!0},type:"dict-radio",form:{rules:[{required:!0,message:"排序必填"}],value:!0},dict:s({data:[{label:"启用",value:!0,color:"success",effect:"dark"},{label:"禁用",value:!1,effect:"dark"}]})}}}}};export{y as createCrudOptions};
