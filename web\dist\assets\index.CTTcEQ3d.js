import{a as g}from"./index.GuQX7xXE.js";import v from"./crud.Dzi05Jdb.js";import{d,a as C,o as x,b as t,g as k,e as R,w as n,f as a,n as V,u as h}from"./vue.zNq9Glab.js";import"./index.NzcGb9Oc.js";import"./_plugin-vue_export-helper.DlAUqK2U.js";import"./authFunction.BcROZVTX.js";const B=d({name:"messageCenter"}),P=d({...B,setup(w){const e=C("send"),l=r=>{const{paneName:o}=r;e.value=o,s.doRefresh()},p={tabActivted:e},{crudRef:m,crudBinding:_,crudExpose:s}=g({createCrudOptions:v,context:p});return x(()=>{s.doRefresh()}),(r,o)=>{const c=t("el-tab-pane"),u=t("el-tabs"),f=t("fs-crud"),i=t("fs-page");return R(),k(i,null,{default:n(()=>[a(f,V({ref_key:"crudRef",ref:m},h(_)),{"header-middle":n(()=>[a(u,{modelValue:e.value,"onUpdate:modelValue":o[0]||(o[0]=b=>e.value=b),onTabClick:l},{default:n(()=>[a(c,{label:"我的发布",name:"send"}),a(c,{label:"我的接收",name:"receive"})]),_:1},8,["modelValue"])]),_:1},16)]),_:1})}}});export{P as default};
