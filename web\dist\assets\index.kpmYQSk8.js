import{d as a,a as m,o as _,b as s,y as h,g,e as y,w as r,f as x,n as v,u as w,A as E,l as z,D as B}from"./vue.zNq9Glab.js";import{a as C}from"./index.GuQX7xXE.js";import{createCrudOptions as b}from"./crud.Dg36Oc6v.js";import{e as R}from"./echarts.D5sl-F-p.js";import"./commonCrud.DFvADd-j.js";import"./_plugin-vue_export-helper.DlAUqK2U.js";const k={id:"myEcharts",style:{width:"100%",height:"300px"}},A=a({name:"loginLog"}),P=a({...A,setup(D){const t=m(!0),{crudBinding:n,crudRef:i,crudExpose:c}=C({createCrudOptions:b,isEcharts:t,initChart:o}),p=R;function o(){let e=p.init(document.getElementById("myEcharts"),"purple-passion");e.setOption({title:{text:"2021年各月份销售量（单位：件）",left:"center"},xAxis:{type:"category",data:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},tooltip:{trigger:"axis"},yAxis:{type:"value"},series:[{data:[606,542,985,687,501,787,339,706,383,684,669,737],type:"line",smooth:!0,itemStyle:{normal:{label:{show:!0,position:"top",formatter:"{c}"}}}}]}),window.onresize=function(){e.resize()}}function l(e){console.log(e)}return _(()=>{c.doRefresh(),o()}),(e,N)=>{const d=s("fs-crud"),u=s("fs-page"),f=h("resize-ob");return y(),g(u,null,{default:r(()=>[x(d,v({ref_key:"crudRef",ref:i},w(n)),{"header-top":r(()=>[E(z("div",k,null,512),[[B,t.value],[f,l]])]),_:1},16)]),_:1})}}});export{P as default};
