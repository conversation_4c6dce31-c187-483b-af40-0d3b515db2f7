import $ from"./index.BznPD_Ny.js";import{g as j,d as A,a as P}from"./api.CAHR2Rwu.js";import{R as w,E as R,y as k}from"./index.GuQX7xXE.js";import{d as q,r as I,a as x,b as m,y as M,c as O,e as C,l as b,A as G,f as s,w as u,k as g,u as l,g as D,h as H,$ as J}from"./vue.zNq9Glab.js";import{_ as K}from"./_plugin-vue_export-helper.DlAUqK2U.js";const L={class:"columns-table-com"},Q={class:"ctc-head"},W={class:"ctc-pagination"},X=q({__name:"index",props:{currentInfo:{type:Object,required:!0,default:()=>{}}},setup(z,{expose:V}){const r=z;let i=I({page:1,limit:20}),c=I({loading:!1,data:[],total:0}),d=x(!1),_=x({});const p=async(t=r.currentInfo)=>{try{c.loading=!0;const e=await j({...i,...t});(e==null?void 0:e.code)===2e3&&(c.data=e.data,c.total=e.total)}finally{c.loading=!1}},h=async()=>{var t,e,a;if((t=r.currentInfo)!=null&&t.role&&((e=r.currentInfo)!=null&&e.model)&&((a=r.currentInfo)!=null&&a.app)){const o=await P(r.currentInfo);(o==null?void 0:o.code)===2e3&&(k("匹配成功"),p());return}w("请选择角色和模型表！")},v=(t,e)=>{var a,o,f;if((a=r.currentInfo)!=null&&a.role&&((o=r.currentInfo)!=null&&o.model)&&((f=r.currentInfo)!=null&&f.app)){t==="update"&&e&&(_.value=e),d.value=!0;return}w("请选择角色和模型表！")},y=t=>{t==="submit"&&p(),d.value=!1,_.value={}},B=({id:t})=>{R.confirm("确定删除该字段吗？","提示",{type:"error",confirmButtonText:"确定",cancelButtonText:"取消"}).then(async()=>{const e=await A(t);(e==null?void 0:e.code)===2e3&&(k("删除成功"),p())}).catch(()=>{})},N=t=>{i.limit=t,p()},F=t=>{i.page=t,p()};return V({fetchData:p}),(t,e)=>{const a=m("el-button"),o=m("el-table-column"),f=m("el-table"),T=m("el-pagination"),U=m("el-drawer"),E=M("loading");return C(),O("div",L,[e[8]||(e[8]=b("p",{class:"ctc-title"},"字段权限",-1)),b("div",Q,[s(a,{type:"primary",onClick:e[0]||(e[0]=n=>v("create"))},{default:u(()=>e[4]||(e[4]=[g("新增")])),_:1,__:[4]}),s(a,{type:"primary",onClick:h},{default:u(()=>e[5]||(e[5]=[g("自动匹配")])),_:1,__:[5]})]),G((C(),D(f,{data:l(c).data,border:"",class:"ctc-table"},{default:u(()=>[s(o,{prop:"field_name",label:"字段名"}),s(o,{prop:"title",label:"列名"}),s(o,{label:"操作",width:"180",align:"center"},{default:u(n=>[s(a,{type:"primary",onClick:S=>v("update",n.row)},{default:u(()=>e[6]||(e[6]=[g("编辑")])),_:2,__:[6]},1032,["onClick"]),s(a,{type:"danger",onClick:S=>B(n.row)},{default:u(()=>e[7]||(e[7]=[g("删除")])),_:2,__:[7]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[E,l(c).loading]]),b("div",W,[s(T,{"current-page":l(i).page,"onUpdate:currentPage":e[1]||(e[1]=n=>l(i).page=n),"page-size":l(i).limit,"onUpdate:pageSize":e[2]||(e[2]=n=>l(i).limit=n),"page-sizes":[5,10,20,50],total:l(c).total,background:"",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:N,onCurrentChange:F},null,8,["current-page","page-size","total"])]),s(U,{modelValue:l(d),"onUpdate:modelValue":e[3]||(e[3]=n=>J(d)?d.value=n:d=n),title:"字段权限",direction:"rtl",size:"500px","close-on-click-modal":!1,"before-close":y},{default:u(()=>[l(d)?(C(),D($,{key:0,currentInfo:r.currentInfo,initFormData:l(_),onDrawerClose:y},null,8,["currentInfo","initFormData"])):H("",!0)]),_:1},8,["modelValue"])])}}}),oe=K(X,[["__scopeId","data-v-1a4f1998"]]);export{oe as default};
