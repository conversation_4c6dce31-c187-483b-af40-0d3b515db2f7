import{d as L,a as y,M as P,r as V,o as z,O as F,b as l,A as h,D as g,c as H,e as B,l as t,v as N,f as s,Z as a,k as $,t as m,w as r,_ as W,S as G,I as K}from"./vue.zNq9Glab.js";import{f as u}from"./formatTime.in1fXasu.js";import{u as U,L as J}from"./index.GuQX7xXE.js";import{_ as O}from"./_plugin-vue_export-helper.DlAUqK2U.js";const Z={class:"layout-lock-screen"},j={class:"layout-lock-screen-date-box"},Q={class:"layout-lock-screen-date-box-time"},X={class:"layout-lock-screen-date-box-minutes"},ee={class:"layout-lock-screen-date-box-info"},oe={class:"layout-lock-screen-date-top"},te={class:"layout-lock-screen-login"},ne={class:"layout-lock-screen-login-box"},se={class:"layout-lock-screen-login-box-value"},ce={class:"layout-lock-screen-login-icon"},ie=L({name:"layoutLockScreen"}),le=L({...ie,setup(ae){const d=y(),f=y(),T=U(),{themeConfig:c}=P(T),e=V({transparency:1,downClientY:0,moveDifference:0,isShowLoockLogin:!1,isFlags:!1,querySelectorEl:"",time:{hm:"",s:"",mdq:""},setIntervalTime:0,isShowLockScreen:!1,isShowLockScreenIntervalTime:0,lockScreenPassword:""}),D=o=>{e.isFlags=!0,e.downClientY=o.clientY},x=o=>{e.isFlags=!0,e.downClientY=o.touches[0].clientY},I=o=>{e.moveDifference=o.clientY-e.downClientY,v()},b=o=>{e.moveDifference=o.touches[0].clientY-e.downClientY,v()},v=()=>{if(e.isFlags){const o=e.querySelectorEl,n=e.transparency-=1/200;if(e.moveDifference>=0)return!1;o.setAttribute("style",`top:${e.moveDifference}px;cursor:pointer;opacity:${n};`),e.moveDifference<-400&&(o.setAttribute("style",`top:${-o.clientHeight}px;cursor:pointer;transition:all 0.3s ease;`),e.moveDifference=-o.clientHeight,setTimeout(()=>{var i;o&&((i=o.parentNode)==null||i.removeChild(o))},300)),e.moveDifference===-o.clientHeight&&(e.isShowLoockLogin=!0,f.value.focus())}},p=()=>{e.isFlags=!1,e.transparency=1,e.moveDifference>=-400&&e.querySelectorEl.setAttribute("style","top:0px;opacity:1;transition:all 0.3s ease;")},C=()=>{K(()=>{e.querySelectorEl=d.value})},k=()=>{e.time.hm=u(new Date,"HH:MM"),e.time.s=u(new Date,"SS"),e.time.mdq=u(new Date,"mm月dd日，WWW")},M=()=>{k(),e.setIntervalTime=window.setInterval(()=>{k()},1e3)},Y=()=>{c.value.isLockScreen?e.isShowLockScreenIntervalTime=window.setInterval(()=>{if(c.value.lockScreenTime<=1)return e.isShowLockScreen=!0,S(),!1;c.value.lockScreenTime--},1e3):clearInterval(e.isShowLockScreenIntervalTime)},S=()=>{c.value.isDrawer=!1,J.set("themeConfig",c.value)},_=()=>{c.value.isLockScreen=!1,c.value.lockScreenTime=30,S()};return z(()=>{C(),M(),Y()}),F(()=>{window.clearInterval(e.setIntervalTime),window.clearInterval(e.isShowLockScreenIntervalTime)}),(o,n)=>{const i=l("SvgIcon"),A=l("ele-Right"),E=l("el-icon"),q=l("el-button"),R=l("el-input");return h((B(),H("div",null,[n[5]||(n[5]=t("div",{class:"layout-lock-screen-mask"},null,-1)),t("div",{class:N(["layout-lock-screen-img",{"layout-lock-screen-filter":e.isShowLoockLogin}])},null,2),t("div",Z,[t("div",{class:"layout-lock-screen-date",ref_key:"layoutLockScreenDateRef",ref:d,onMousedown:D,onMousemove:I,onMouseup:p,onTouchstart:a(x,["stop"]),onTouchmove:a(b,["stop"]),onTouchend:a(p,["stop"])},[t("div",j,[t("div",Q,[$(m(e.time.hm),1),t("span",X,m(e.time.s),1)]),t("div",ee,m(e.time.mdq),1)]),t("div",oe,[s(i,{name:"ele-Top"}),n[2]||(n[2]=t("div",{class:"layout-lock-screen-date-top-text"},"上滑解锁",-1))])],544),s(G,{name:"el-zoom-in-center"},{default:r(()=>[h(t("div",te,[t("div",ne,[n[3]||(n[3]=t("div",{class:"layout-lock-screen-login-box-img"},[t("img",{src:"https://img2.baidu.com/it/u=1978192862,2048448374&fm=253&fmt=auto&app=138&f=JPEG?w=504&h=500"})],-1)),n[4]||(n[4]=t("div",{class:"layout-lock-screen-login-box-name"},"Administrator",-1)),t("div",se,[s(R,{placeholder:"请输入密码",ref_key:"layoutLockScreenInputRef",ref:f,modelValue:e.lockScreenPassword,"onUpdate:modelValue":n[0]||(n[0]=w=>e.lockScreenPassword=w),onKeyup:n[1]||(n[1]=W(a(w=>_(),["stop"]),["enter","native"]))},{append:r(()=>[s(q,{onClick:_},{default:r(()=>[s(E,{class:"el-input__icon"},{default:r(()=>[s(A)]),_:1})]),_:1})]),_:1},8,["modelValue"])])]),t("div",ce,[s(i,{name:"ele-Microphone",size:20}),s(i,{name:"ele-AlarmClock",size:20}),s(i,{name:"ele-SwitchButton",size:20})])],512),[[g,e.isShowLoockLogin]])]),_:1})])],512)),[[g,e.isShowLockScreen]])}}}),fe=O(le,[["__scopeId","data-v-4b6f2927"]]);export{fe as default};
