import{a as B,c as y,b as T,E as D,X as u,e as E}from"./index.GuQX7xXE.js";import{B as M,c as O}from"./crud.DeLz-9l3.js";import{d as S,a as N,p as V,b as o,g as X,e as $,n as z,u as c,w as t,f as a,k as A,t as P}from"./vue.zNq9Glab.js";const J=S({__name:"index",setup(U,{expose:_}){let i=N({name:null});const{crudRef:m,crudBinding:g,crudExpose:s,context:j,selectedRows:l}=B({createCrudOptions:O,context:{selectOptions:i}}),{doRefresh:b,setTableData:h}=s,r=V(()=>l.value.length),v=async()=>{await D.confirm(`确定要批量删除这${l.value.length}条记录吗`,"确认",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1}),await M(u.pluck(l.value,"id")),E.info("删除成功"),l.value=[],await s.doRefresh()},x=e=>{const p=s.getBaseTableRef(),n=s.getTableData();u.pluck(n,"id").includes(e.id)?p.toggleRowSelection(e,!1):l.value=u.remove(l.value,d=>d.id!==e.id)};return _({selectOptions:i,handleRefreshTable:e=>{!e.is_catalog&&e.id?(i.value=e,b()):h([])}}),(e,p)=>{const n=o("el-button"),d=o("el-tooltip"),f=o("el-table-column"),R=o("el-table"),k=o("el-popover"),w=o("fs-crud");return $(),X(w,z({ref_key:"crudRef",ref:m},c(g)),{"pagination-left":t(()=>[a(d,{content:"批量删除"},{default:t(()=>[a(n,{text:"",type:"danger",disabled:r.value===0,icon:c(T),circle:"",onClick:v},null,8,["disabled","icon"])]),_:1})]),"pagination-right":t(()=>[a(k,{placement:"top",width:400,trigger:"click"},{reference:t(()=>[a(n,{text:"",type:r.value>0?"primary":""},{default:t(()=>[A("已选中"+P(r.value)+"条数据",1)]),_:1},8,["type"])]),default:t(()=>[a(R,{data:c(l),size:"small"},{default:t(()=>[a(f,{width:"150",property:"id",label:"id"}),a(f,{fixed:"right",label:"操作","min-width":"60"},{default:t(C=>[a(n,{text:"",type:"info",icon:c(y),onClick:F=>x(C.row),circle:""},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},16)}}});export{J as _};
