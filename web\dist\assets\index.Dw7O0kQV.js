import{a as f}from"./index.GuQX7xXE.js";import{G as i,c as d}from"./crud.a0KJ9-07.js";import{h as m}from"./columnPermission.6pZgyu0_.js";import{d as s,o as _,b as o,g as l,e as g,w as C,f as h,n as w,u as x}from"./vue.zNq9Glab.js";import"./commonCrud.DFvADd-j.js";const O=s({name:"loginLog"}),v=s({...O,setup(k){const{crudBinding:n,crudRef:r,crudExpose:t,crudOptions:a,resetCrudOptions:c}=f({createCrudOptions:d});return _(async()=>{const e=await m(i,a);c(e),t.doRefresh()}),(e,B)=>{const p=o("fs-crud"),u=o("fs-page");return g(),l(u,null,{default:C(()=>[h(p,w({ref_key:"crudRef",ref:r},x(n)),null,16)]),_:1})}}});export{v as default};
