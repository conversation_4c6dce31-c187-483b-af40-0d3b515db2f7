import{a6 as It,a7 as Ut,a8 as Nt,C as jt,m as Lt,f as Mt,h as Kt}from"./index.GuQX7xXE.js";import"./vue.zNq9Glab.js";var At={exports:{}};(function(Ke,Xe){(function(ne,E){Ke.exports=E()})(Ut,function(){return function(H){var ne={};function E(N){if(ne[N])return ne[N].exports;var P=ne[N]={i:N,l:!1,exports:{}};return H[N].call(P.exports,P,P.exports,E),P.l=!0,P.exports}return E.m=H,E.c=ne,E.d=function(N,P,j){E.o(N,P)||Object.defineProperty(N,P,{enumerable:!0,get:j})},E.r=function(N){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(N,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(N,"__esModule",{value:!0})},E.t=function(N,P){if(P&1&&(N=E(N)),P&8||P&4&&typeof N=="object"&&N&&N.__esModule)return N;var j=Object.create(null);if(E.r(j),Object.defineProperty(j,"default",{enumerable:!0,value:N}),P&2&&typeof N!="string")for(var M in N)E.d(j,M,(function(L){return N[L]}).bind(null,M));return j},E.n=function(N){var P=N&&N.__esModule?function(){return N.default}:function(){return N};return E.d(P,"a",P),P},E.o=function(N,P){return Object.prototype.hasOwnProperty.call(N,P)},E.p="/dist/",E(E.s="./index.js")}({"./index.js":function(H,ne,E){var N=E("./src/cos.js");H.exports=N},"./lib/base64.js":function(H,ne){var E=function(N){N=N||{};var P=N.Base64,j="2.1.9",M="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",L=function(r){for(var c={},p=0,I=r.length;p<I;p++)c[r.charAt(p)]=p;return c}(M),i=String.fromCharCode,C=function(c){if(c.length<2){var p=c.charCodeAt(0);return p<128?c:p<2048?i(192|p>>>6)+i(128|p&63):i(224|p>>>12&15)+i(128|p>>>6&63)+i(128|p&63)}else{var p=65536+(c.charCodeAt(0)-55296)*1024+(c.charCodeAt(1)-56320);return i(240|p>>>18&7)+i(128|p>>>12&63)+i(128|p>>>6&63)+i(128|p&63)}},d=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,S=function(c){return c.replace(d,C)},f=function(c){var p=[0,2,1][c.length%3],I=c.charCodeAt(0)<<16|(c.length>1?c.charCodeAt(1):0)<<8|(c.length>2?c.charCodeAt(2):0),R=[M.charAt(I>>>18),M.charAt(I>>>12&63),p>=2?"=":M.charAt(I>>>6&63),p>=1?"=":M.charAt(I&63)];return R.join("")},y=N.btoa?function(r){return N.btoa(r)}:function(r){return r.replace(/[\s\S]{1,3}/g,f)},x=function(r){return y(S(r))},b=function(c,p){return p?x(String(c)).replace(/[+\/]/g,function(I){return I=="+"?"-":"_"}).replace(/=/g,""):x(String(c))},w=function(c){return b(c,!0)},U=new RegExp(["[À-ß][-¿]","[à-ï][-¿]{2}","[ð-÷][-¿]{3}"].join("|"),"g"),z=function(c){switch(c.length){case 4:var p=(7&c.charCodeAt(0))<<18|(63&c.charCodeAt(1))<<12|(63&c.charCodeAt(2))<<6|63&c.charCodeAt(3),I=p-65536;return i((I>>>10)+55296)+i((I&1023)+56320);case 3:return i((15&c.charCodeAt(0))<<12|(63&c.charCodeAt(1))<<6|63&c.charCodeAt(2));default:return i((31&c.charCodeAt(0))<<6|63&c.charCodeAt(1))}},q=function(c){return c.replace(U,z)},h=function(c){var p=c.length,I=p%4,R=(p>0?L[c.charAt(0)]<<18:0)|(p>1?L[c.charAt(1)]<<12:0)|(p>2?L[c.charAt(2)]<<6:0)|(p>3?L[c.charAt(3)]:0),K=[i(R>>>16),i(R>>>8&255),i(R&255)];return K.length-=[0,0,2,1][I],K.join("")},l=N.atob?function(r){return N.atob(r)}:function(r){return r.replace(/[\s\S]{1,4}/g,h)},T=function(r){return q(l(r))},s=function(c){return T(String(c).replace(/[-_]/g,function(p){return p=="-"?"+":"/"}).replace(/[^A-Za-z0-9\+\/]/g,""))},o=function(){var c=N.Base64;return N.Base64=P,c},t={VERSION:j,atob:l,btoa:y,fromBase64:s,toBase64:b,utob:S,encode:b,encodeURI:w,btou:q,decode:s,noConflict:o};return t}();H.exports=E},"./lib/crypto.js":function(H,ne,E){(function(N){var P=E("./node_modules/@babel/runtime/helpers/typeof.js"),j=j||function(M,L){var i={},C=i.lib={},d=function(){},S=C.Base={extend:function(h){d.prototype=this;var l=new d;return h&&l.mixIn(h),l.hasOwnProperty("init")||(l.init=function(){l.$super.init.apply(this,arguments)}),l.init.prototype=l,l.$super=this,l},create:function(){var h=this.extend();return h.init.apply(h,arguments),h},init:function(){},mixIn:function(h){for(var l in h)h.hasOwnProperty(l)&&(this[l]=h[l]);h.hasOwnProperty("toString")&&(this.toString=h.toString)},clone:function(){return this.init.prototype.extend(this)}},f=C.WordArray=S.extend({init:function(h,l){h=this.words=h||[],this.sigBytes=l!=L?l:4*h.length},toString:function(h){return(h||x).stringify(this)},concat:function(h){var l=this.words,T=h.words,s=this.sigBytes;if(h=h.sigBytes,this.clamp(),s%4)for(var o=0;o<h;o++)l[s+o>>>2]|=(T[o>>>2]>>>24-8*(o%4)&255)<<24-8*((s+o)%4);else if(65535<T.length)for(o=0;o<h;o+=4)l[s+o>>>2]=T[o>>>2];else l.push.apply(l,T);return this.sigBytes+=h,this},clamp:function(){var h=this.words,l=this.sigBytes;h[l>>>2]&=4294967295<<32-8*(l%4),h.length=M.ceil(l/4)},clone:function(){var h=S.clone.call(this);return h.words=this.words.slice(0),h},random:function(h){for(var l=[],T=0;T<h;T+=4)l.push(4294967296*M.random()|0);return new f.init(l,h)}}),y=i.enc={},x=y.Hex={stringify:function(h){var l=h.words;h=h.sigBytes;for(var T=[],s=0;s<h;s++){var o=l[s>>>2]>>>24-8*(s%4)&255;T.push((o>>>4).toString(16)),T.push((o&15).toString(16))}return T.join("")},parse:function(h){for(var l=h.length,T=[],s=0;s<l;s+=2)T[s>>>3]|=parseInt(h.substr(s,2),16)<<24-4*(s%8);return new f.init(T,l/2)}},b=y.Latin1={stringify:function(h){var l=h.words;h=h.sigBytes;for(var T=[],s=0;s<h;s++)T.push(String.fromCharCode(l[s>>>2]>>>24-8*(s%4)&255));return T.join("")},parse:function(h){for(var l=h.length,T=[],s=0;s<l;s++)T[s>>>2]|=(h.charCodeAt(s)&255)<<24-8*(s%4);return new f.init(T,l)}},w=y.Utf8={stringify:function(h){try{return decodeURIComponent(escape(b.stringify(h)))}catch{throw Error("Malformed UTF-8 data")}},parse:function(h){return b.parse(unescape(encodeURIComponent(h)))}},U=C.BufferedBlockAlgorithm=S.extend({reset:function(){this._data=new f.init,this._nDataBytes=0},_append:function(h){typeof h=="string"&&(h=w.parse(h)),this._data.concat(h),this._nDataBytes+=h.sigBytes},_process:function(h){var l=this._data,T=l.words,s=l.sigBytes,o=this.blockSize,t=s/(4*o),t=h?M.ceil(t):M.max((t|0)-this._minBufferSize,0);if(h=t*o,s=M.min(4*h,s),h){for(var r=0;r<h;r+=o)this._doProcessBlock(T,r);r=T.splice(0,h),l.sigBytes-=s}return new f.init(r,s)},clone:function(){var h=S.clone.call(this);return h._data=this._data.clone(),h},_minBufferSize:0});C.Hasher=U.extend({cfg:S.extend(),init:function(h){this.cfg=this.cfg.extend(h),this.reset()},reset:function(){U.reset.call(this),this._doReset()},update:function(h){return this._append(h),this._process(),this},finalize:function(h){return h&&this._append(h),this._doFinalize()},blockSize:16,_createHelper:function(h){return function(l,T){return new h.init(T).finalize(l)}},_createHmacHelper:function(h){return function(l,T){return new z.HMAC.init(h,T).finalize(l)}}});var z=i.algo={};return i}(Math);(function(){var M=j,d=M.lib,L=d.WordArray,i=d.Hasher,C=[],d=M.algo.SHA1=i.extend({_doReset:function(){this._hash=new L.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(f,y){for(var x=this._hash.words,b=x[0],w=x[1],U=x[2],z=x[3],q=x[4],h=0;80>h;h++){if(16>h)C[h]=f[y+h]|0;else{var l=C[h-3]^C[h-8]^C[h-14]^C[h-16];C[h]=l<<1|l>>>31}l=(b<<5|b>>>27)+q+C[h],l=20>h?l+((w&U|~w&z)+1518500249):40>h?l+((w^U^z)+1859775393):60>h?l+((w&U|w&z|U&z)-1894007588):l+((w^U^z)-899497514),q=z,z=U,U=w<<30|w>>>2,w=b,b=l}x[0]=x[0]+b|0,x[1]=x[1]+w|0,x[2]=x[2]+U|0,x[3]=x[3]+z|0,x[4]=x[4]+q|0},_doFinalize:function(){var f=this._data,y=f.words,x=8*this._nDataBytes,b=8*f.sigBytes;return y[b>>>5]|=128<<24-b%32,y[(b+64>>>9<<4)+14]=Math.floor(x/4294967296),y[(b+64>>>9<<4)+15]=x,f.sigBytes=4*y.length,this._process(),this._hash},clone:function(){var f=i.clone.call(this);return f._hash=this._hash.clone(),f}});M.SHA1=i._createHelper(d),M.HmacSHA1=i._createHmacHelper(d)})(),function(){var M=j,L=M.enc.Utf8;M.algo.HMAC=M.lib.Base.extend({init:function(C,d){C=this._hasher=new C.init,typeof d=="string"&&(d=L.parse(d));var S=C.blockSize,f=4*S;d.sigBytes>f&&(d=C.finalize(d)),d.clamp();for(var y=this._oKey=d.clone(),x=this._iKey=d.clone(),b=y.words,w=x.words,U=0;U<S;U++)b[U]^=1549556828,w[U]^=909522486;y.sigBytes=x.sigBytes=f,this.reset()},reset:function(){var C=this._hasher;C.reset(),C.update(this._iKey)},update:function(C){return this._hasher.update(C),this},finalize:function(C){var d=this._hasher;return C=d.finalize(C),d.reset(),d.finalize(this._oKey.clone().concat(C))}})}(),function(){var M=j,L=M.lib,i=L.WordArray,C=M.enc;C.Base64={stringify:function(S){var f=S.words,y=S.sigBytes,x=this._map;S.clamp();for(var b=[],w=0;w<y;w+=3)for(var U=f[w>>>2]>>>24-w%4*8&255,z=f[w+1>>>2]>>>24-(w+1)%4*8&255,q=f[w+2>>>2]>>>24-(w+2)%4*8&255,h=U<<16|z<<8|q,l=0;l<4&&w+l*.75<y;l++)b.push(x.charAt(h>>>6*(3-l)&63));var T=x.charAt(64);if(T)for(;b.length%4;)b.push(T);return b.join("")},parse:function(S){var f=S.length,y=this._map,x=y.charAt(64);if(x){var b=S.indexOf(x);b!=-1&&(f=b)}for(var w=[],U=0,z=0;z<f;z++)if(z%4){var q=y.indexOf(S.charAt(z-1))<<z%4*2,h=y.indexOf(S.charAt(z))>>>6-z%4*2;w[U>>>2]|=(q|h)<<24-U%4*8,U++}return i.create(w,U)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),P(N)==="object"?N.exports=j:window.CryptoJS=j}).call(this,E("./node_modules/webpack/buildin/module.js")(H))},"./lib/md5.js":function(H,ne,E){(function(N){var P,j=E("./node_modules/@babel/runtime/helpers/typeof.js");(function(){var M=(typeof window>"u"?"undefined":j(window))==="object",L=M?window:{};L.JS_MD5_NO_WINDOW&&(M=!1);var i=!M&&(typeof self>"u"?"undefined":j(self))==="object";i&&(L=self);var C=!L.JS_MD5_NO_COMMON_JS&&j(N)==="object"&&N.exports,d=E("./node_modules/webpack/buildin/amd-options.js"),S=!L.JS_MD5_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",f="0123456789abcdef".split(""),y=[128,32768,8388608,-2147483648],x=[0,8,16,24],b=["hex","array","digest","buffer","arrayBuffer","base64"],w="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),U=[],z;if(S){var q=new ArrayBuffer(68);z=new Uint8Array(q),U=new Uint32Array(q)}(L.JS_MD5_NO_NODE_JS||!Array.isArray)&&(Array.isArray=function(o){return Object.prototype.toString.call(o)==="[object Array]"}),S&&(L.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW||!ArrayBuffer.isView)&&(ArrayBuffer.isView=function(o){return j(o)==="object"&&o.buffer&&o.buffer.constructor===ArrayBuffer});var h=function(t){return function(r,c){return new T(!0).update(r,c)[t]()}},l=function(){var t=h("hex");t.getCtx=t.create=function(){return new T},t.update=function(p){return t.create().update(p)};for(var r=0;r<b.length;++r){var c=b[r];t[c]=h(c)}return t};function T(o){if(o)U[0]=U[16]=U[1]=U[2]=U[3]=U[4]=U[5]=U[6]=U[7]=U[8]=U[9]=U[10]=U[11]=U[12]=U[13]=U[14]=U[15]=0,this.blocks=U,this.buffer8=z;else if(S){var t=new ArrayBuffer(68);this.buffer8=new Uint8Array(t),this.blocks=new Uint32Array(t)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}T.prototype.update=function(o,t){if(!this.finalized){for(var r,c=0,p,I=o.length,R=this.blocks,K=this.buffer8;c<I;){if(this.hashed&&(this.hashed=!1,R[0]=R[16],R[16]=R[1]=R[2]=R[3]=R[4]=R[5]=R[6]=R[7]=R[8]=R[9]=R[10]=R[11]=R[12]=R[13]=R[14]=R[15]=0),S)for(p=this.start;c<I&&p<64;++c)r=o.charCodeAt(c),t||r<128?K[p++]=r:r<2048?(K[p++]=192|r>>6,K[p++]=128|r&63):r<55296||r>=57344?(K[p++]=224|r>>12,K[p++]=128|r>>6&63,K[p++]=128|r&63):(r=65536+((r&1023)<<10|o.charCodeAt(++c)&1023),K[p++]=240|r>>18,K[p++]=128|r>>12&63,K[p++]=128|r>>6&63,K[p++]=128|r&63);else for(p=this.start;c<I&&p<64;++c)r=o.charCodeAt(c),t||r<128?R[p>>2]|=r<<x[p++&3]:r<2048?(R[p>>2]|=(192|r>>6)<<x[p++&3],R[p>>2]|=(128|r&63)<<x[p++&3]):r<55296||r>=57344?(R[p>>2]|=(224|r>>12)<<x[p++&3],R[p>>2]|=(128|r>>6&63)<<x[p++&3],R[p>>2]|=(128|r&63)<<x[p++&3]):(r=65536+((r&1023)<<10|o.charCodeAt(++c)&1023),R[p>>2]|=(240|r>>18)<<x[p++&3],R[p>>2]|=(128|r>>12&63)<<x[p++&3],R[p>>2]|=(128|r>>6&63)<<x[p++&3],R[p>>2]|=(128|r&63)<<x[p++&3]);this.lastByteIndex=p,this.bytes+=p-this.start,p>=64?(this.start=p-64,this.hash(),this.hashed=!0):this.start=p}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},T.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var o=this.blocks,t=this.lastByteIndex;o[t>>2]|=y[t&3],t>=56&&(this.hashed||this.hash(),o[0]=o[16],o[16]=o[1]=o[2]=o[3]=o[4]=o[5]=o[6]=o[7]=o[8]=o[9]=o[10]=o[11]=o[12]=o[13]=o[14]=o[15]=0),o[14]=this.bytes<<3,o[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},T.prototype.hash=function(){var o,t,r,c,p,I,R=this.blocks;this.first?(o=R[0]-680876937,o=(o<<7|o>>>25)-271733879<<0,c=(-1732584194^o&2004318071)+R[1]-117830708,c=(c<<12|c>>>20)+o<<0,r=(-271733879^c&(o^-271733879))+R[2]-1126478375,r=(r<<17|r>>>15)+c<<0,t=(o^r&(c^o))+R[3]-1316259209,t=(t<<22|t>>>10)+r<<0):(o=this.h0,t=this.h1,r=this.h2,c=this.h3,o+=(c^t&(r^c))+R[0]-680876936,o=(o<<7|o>>>25)+t<<0,c+=(r^o&(t^r))+R[1]-389564586,c=(c<<12|c>>>20)+o<<0,r+=(t^c&(o^t))+R[2]+606105819,r=(r<<17|r>>>15)+c<<0,t+=(o^r&(c^o))+R[3]-1044525330,t=(t<<22|t>>>10)+r<<0),o+=(c^t&(r^c))+R[4]-176418897,o=(o<<7|o>>>25)+t<<0,c+=(r^o&(t^r))+R[5]+1200080426,c=(c<<12|c>>>20)+o<<0,r+=(t^c&(o^t))+R[6]-1473231341,r=(r<<17|r>>>15)+c<<0,t+=(o^r&(c^o))+R[7]-45705983,t=(t<<22|t>>>10)+r<<0,o+=(c^t&(r^c))+R[8]+1770035416,o=(o<<7|o>>>25)+t<<0,c+=(r^o&(t^r))+R[9]-1958414417,c=(c<<12|c>>>20)+o<<0,r+=(t^c&(o^t))+R[10]-42063,r=(r<<17|r>>>15)+c<<0,t+=(o^r&(c^o))+R[11]-1990404162,t=(t<<22|t>>>10)+r<<0,o+=(c^t&(r^c))+R[12]+1804603682,o=(o<<7|o>>>25)+t<<0,c+=(r^o&(t^r))+R[13]-40341101,c=(c<<12|c>>>20)+o<<0,r+=(t^c&(o^t))+R[14]-1502002290,r=(r<<17|r>>>15)+c<<0,t+=(o^r&(c^o))+R[15]+1236535329,t=(t<<22|t>>>10)+r<<0,o+=(r^c&(t^r))+R[1]-165796510,o=(o<<5|o>>>27)+t<<0,c+=(t^r&(o^t))+R[6]-1069501632,c=(c<<9|c>>>23)+o<<0,r+=(o^t&(c^o))+R[11]+643717713,r=(r<<14|r>>>18)+c<<0,t+=(c^o&(r^c))+R[0]-373897302,t=(t<<20|t>>>12)+r<<0,o+=(r^c&(t^r))+R[5]-701558691,o=(o<<5|o>>>27)+t<<0,c+=(t^r&(o^t))+R[10]+38016083,c=(c<<9|c>>>23)+o<<0,r+=(o^t&(c^o))+R[15]-660478335,r=(r<<14|r>>>18)+c<<0,t+=(c^o&(r^c))+R[4]-405537848,t=(t<<20|t>>>12)+r<<0,o+=(r^c&(t^r))+R[9]+568446438,o=(o<<5|o>>>27)+t<<0,c+=(t^r&(o^t))+R[14]-1019803690,c=(c<<9|c>>>23)+o<<0,r+=(o^t&(c^o))+R[3]-187363961,r=(r<<14|r>>>18)+c<<0,t+=(c^o&(r^c))+R[8]+1163531501,t=(t<<20|t>>>12)+r<<0,o+=(r^c&(t^r))+R[13]-1444681467,o=(o<<5|o>>>27)+t<<0,c+=(t^r&(o^t))+R[2]-51403784,c=(c<<9|c>>>23)+o<<0,r+=(o^t&(c^o))+R[7]+1735328473,r=(r<<14|r>>>18)+c<<0,t+=(c^o&(r^c))+R[12]-1926607734,t=(t<<20|t>>>12)+r<<0,p=t^r,o+=(p^c)+R[5]-378558,o=(o<<4|o>>>28)+t<<0,c+=(p^o)+R[8]-2022574463,c=(c<<11|c>>>21)+o<<0,I=c^o,r+=(I^t)+R[11]+1839030562,r=(r<<16|r>>>16)+c<<0,t+=(I^r)+R[14]-35309556,t=(t<<23|t>>>9)+r<<0,p=t^r,o+=(p^c)+R[1]-1530992060,o=(o<<4|o>>>28)+t<<0,c+=(p^o)+R[4]+1272893353,c=(c<<11|c>>>21)+o<<0,I=c^o,r+=(I^t)+R[7]-155497632,r=(r<<16|r>>>16)+c<<0,t+=(I^r)+R[10]-1094730640,t=(t<<23|t>>>9)+r<<0,p=t^r,o+=(p^c)+R[13]+681279174,o=(o<<4|o>>>28)+t<<0,c+=(p^o)+R[0]-358537222,c=(c<<11|c>>>21)+o<<0,I=c^o,r+=(I^t)+R[3]-722521979,r=(r<<16|r>>>16)+c<<0,t+=(I^r)+R[6]+76029189,t=(t<<23|t>>>9)+r<<0,p=t^r,o+=(p^c)+R[9]-640364487,o=(o<<4|o>>>28)+t<<0,c+=(p^o)+R[12]-421815835,c=(c<<11|c>>>21)+o<<0,I=c^o,r+=(I^t)+R[15]+530742520,r=(r<<16|r>>>16)+c<<0,t+=(I^r)+R[2]-995338651,t=(t<<23|t>>>9)+r<<0,o+=(r^(t|~c))+R[0]-198630844,o=(o<<6|o>>>26)+t<<0,c+=(t^(o|~r))+R[7]+1126891415,c=(c<<10|c>>>22)+o<<0,r+=(o^(c|~t))+R[14]-1416354905,r=(r<<15|r>>>17)+c<<0,t+=(c^(r|~o))+R[5]-57434055,t=(t<<21|t>>>11)+r<<0,o+=(r^(t|~c))+R[12]+1700485571,o=(o<<6|o>>>26)+t<<0,c+=(t^(o|~r))+R[3]-1894986606,c=(c<<10|c>>>22)+o<<0,r+=(o^(c|~t))+R[10]-1051523,r=(r<<15|r>>>17)+c<<0,t+=(c^(r|~o))+R[1]-2054922799,t=(t<<21|t>>>11)+r<<0,o+=(r^(t|~c))+R[8]+1873313359,o=(o<<6|o>>>26)+t<<0,c+=(t^(o|~r))+R[15]-30611744,c=(c<<10|c>>>22)+o<<0,r+=(o^(c|~t))+R[6]-1560198380,r=(r<<15|r>>>17)+c<<0,t+=(c^(r|~o))+R[13]+1309151649,t=(t<<21|t>>>11)+r<<0,o+=(r^(t|~c))+R[4]-145523070,o=(o<<6|o>>>26)+t<<0,c+=(t^(o|~r))+R[11]-1120210379,c=(c<<10|c>>>22)+o<<0,r+=(o^(c|~t))+R[2]+718787259,r=(r<<15|r>>>17)+c<<0,t+=(c^(r|~o))+R[9]-343485551,t=(t<<21|t>>>11)+r<<0,this.first?(this.h0=o+1732584193<<0,this.h1=t-271733879<<0,this.h2=r-1732584194<<0,this.h3=c+271733878<<0,this.first=!1):(this.h0=this.h0+o<<0,this.h1=this.h1+t<<0,this.h2=this.h2+r<<0,this.h3=this.h3+c<<0)},T.prototype.hex=function(){this.finalize();var o=this.h0,t=this.h1,r=this.h2,c=this.h3;return f[o>>4&15]+f[o&15]+f[o>>12&15]+f[o>>8&15]+f[o>>20&15]+f[o>>16&15]+f[o>>28&15]+f[o>>24&15]+f[t>>4&15]+f[t&15]+f[t>>12&15]+f[t>>8&15]+f[t>>20&15]+f[t>>16&15]+f[t>>28&15]+f[t>>24&15]+f[r>>4&15]+f[r&15]+f[r>>12&15]+f[r>>8&15]+f[r>>20&15]+f[r>>16&15]+f[r>>28&15]+f[r>>24&15]+f[c>>4&15]+f[c&15]+f[c>>12&15]+f[c>>8&15]+f[c>>20&15]+f[c>>16&15]+f[c>>28&15]+f[c>>24&15]},T.prototype.toString=T.prototype.hex,T.prototype.digest=function(o){if(o==="hex")return this.hex();this.finalize();var t=this.h0,r=this.h1,c=this.h2,p=this.h3,I=[t&255,t>>8&255,t>>16&255,t>>24&255,r&255,r>>8&255,r>>16&255,r>>24&255,c&255,c>>8&255,c>>16&255,c>>24&255,p&255,p>>8&255,p>>16&255,p>>24&255];return I},T.prototype.array=T.prototype.digest,T.prototype.arrayBuffer=function(){this.finalize();var o=new ArrayBuffer(16),t=new Uint32Array(o);return t[0]=this.h0,t[1]=this.h1,t[2]=this.h2,t[3]=this.h3,o},T.prototype.buffer=T.prototype.arrayBuffer,T.prototype.base64=function(){for(var o,t,r,c="",p=this.array(),I=0;I<15;)o=p[I++],t=p[I++],r=p[I++],c+=w[o>>>2]+w[(o<<4|t>>>4)&63]+w[(t<<2|r>>>6)&63]+w[r&63];return o=p[I],c+=w[o>>>2]+w[o<<4&63]+"==",c};var s=l();C?N.exports=s:(L.md5=s,d&&(P=(function(){return s}).call(s,E,s,N),P!==void 0&&(N.exports=P)))})()}).call(this,E("./node_modules/webpack/buildin/module.js")(H))},"./lib/request.js":function(H,ne,E){var N=E("./node_modules/@babel/runtime/helpers/typeof.js"),P=function(d){switch(N(d)){case"string":return d;case"boolean":return d?"true":"false";case"number":return isFinite(d)?d:"";default:return""}},j=function(d,S,f,y){return S=S||"&",f=f||"=",d===null&&(d=void 0),N(d)==="object"?Object.keys(d).map(function(x){var b=encodeURIComponent(P(x))+f;return Array.isArray(d[x])?d[x].map(function(w){return b+encodeURIComponent(P(w))}).join(S):b+encodeURIComponent(P(d[x]))}).filter(Boolean).join(S):""},M=function(d,S,f){var y={},x=S.getAllResponseHeaders();return x&&x.length>0&&x.trim().split(`
`).forEach(function(b){if(b){var w=b.indexOf(":"),U=b.substr(0,w).trim().toLowerCase(),z=b.substr(w+1).trim();y[U]=z}}),{error:d,statusCode:S.status,statusMessage:S.statusText,headers:y,body:f}},L=function(d,S){return!S&&S==="text"?d.responseText:d.response},i=function(d,S){var f=(d.method||"GET").toUpperCase(),y=d.url;if(d.qs){var x=j(d.qs);x&&(y+=(y.indexOf("?")===-1?"?":"&")+x)}var b=new XMLHttpRequest;if(b.open(f,y,!0),b.responseType=d.dataType||"text",d.xhrFields)for(var w in d.xhrFields)b[w]=d.xhrFields[w];var U=d.headers;if(U)for(var z in U)U.hasOwnProperty(z)&&z.toLowerCase()!=="content-length"&&z.toLowerCase()!=="user-agent"&&z.toLowerCase()!=="origin"&&z.toLowerCase()!=="host"&&b.setRequestHeader(z,U[z]);return d.onProgress&&b.upload&&(b.upload.onprogress=d.onProgress),d.onDownloadProgress&&(b.onprogress=d.onDownloadProgress),d.timeout&&(b.timeout=d.timeout),b.ontimeout=function(q){var h=new Error("timeout");S(M(h,b))},b.onload=function(){S(M(null,b,L(b,d.dataType)))},b.onerror=function(q){var h=L(b,d.dataType);if(h)S(M(null,b,h));else{var l=b.statusText;!l&&b.status===0&&(l=new Error("CORS blocked or network error")),S(M(l,b,h))}},b.send(d.body||""),b};H.exports=i},"./node_modules/@babel/runtime/helpers/classCallCheck.js":function(H,ne){function E(N,P){if(!(N instanceof P))throw new TypeError("Cannot call a class as a function")}H.exports=E,H.exports.__esModule=!0,H.exports.default=H.exports},"./node_modules/@babel/runtime/helpers/createClass.js":function(H,ne,E){var N=E("./node_modules/@babel/runtime/helpers/toPropertyKey.js");function P(M,L){for(var i=0;i<L.length;i++){var C=L[i];C.enumerable=C.enumerable||!1,C.configurable=!0,"value"in C&&(C.writable=!0),Object.defineProperty(M,N(C.key),C)}}function j(M,L,i){return L&&P(M.prototype,L),i&&P(M,i),Object.defineProperty(M,"prototype",{writable:!1}),M}H.exports=j,H.exports.__esModule=!0,H.exports.default=H.exports},"./node_modules/@babel/runtime/helpers/defineProperty.js":function(H,ne,E){var N=E("./node_modules/@babel/runtime/helpers/toPropertyKey.js");function P(j,M,L){return(M=N(M))in j?Object.defineProperty(j,M,{value:L,enumerable:!0,configurable:!0,writable:!0}):j[M]=L,j}H.exports=P,H.exports.__esModule=!0,H.exports.default=H.exports},"./node_modules/@babel/runtime/helpers/toPrimitive.js":function(H,ne,E){var N=E("./node_modules/@babel/runtime/helpers/typeof.js").default;function P(j,M){if(N(j)!="object"||!j)return j;var L=j[Symbol.toPrimitive];if(L!==void 0){var i=L.call(j,M||"default");if(N(i)!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(M==="string"?String:Number)(j)}H.exports=P,H.exports.__esModule=!0,H.exports.default=H.exports},"./node_modules/@babel/runtime/helpers/toPropertyKey.js":function(H,ne,E){var N=E("./node_modules/@babel/runtime/helpers/typeof.js").default,P=E("./node_modules/@babel/runtime/helpers/toPrimitive.js");function j(M){var L=P(M,"string");return N(L)=="symbol"?L:L+""}H.exports=j,H.exports.__esModule=!0,H.exports.default=H.exports},"./node_modules/@babel/runtime/helpers/typeof.js":function(H,ne){function E(N){"@babel/helpers - typeof";return H.exports=E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(P){return typeof P}:function(P){return P&&typeof Symbol=="function"&&P.constructor===Symbol&&P!==Symbol.prototype?"symbol":typeof P},H.exports.__esModule=!0,H.exports.default=H.exports,E(N)}H.exports=E,H.exports.__esModule=!0,H.exports.default=H.exports},"./node_modules/fast-xml-parser/src/fxp.js":function(H,ne,E){const N=E("./node_modules/fast-xml-parser/src/validator.js"),P=E("./node_modules/fast-xml-parser/src/xmlparser/XMLParser.js"),j=E("./node_modules/fast-xml-parser/src/xmlbuilder/json2xml.js");H.exports={XMLParser:P,XMLValidator:N,XMLBuilder:j}},"./node_modules/fast-xml-parser/src/ignoreAttributes.js":function(H,ne){function E(N){return typeof N=="function"?N:Array.isArray(N)?P=>{for(const j of N)if(typeof j=="string"&&P===j||j instanceof RegExp&&j.test(P))return!0}:()=>!1}H.exports=E},"./node_modules/fast-xml-parser/src/util.js":function(H,ne,E){const N=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",P=N+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040",j="["+N+"]["+P+"]*",M=new RegExp("^"+j+"$"),L=function(C,d){const S=[];let f=d.exec(C);for(;f;){const y=[];y.startIndex=d.lastIndex-f[0].length;const x=f.length;for(let b=0;b<x;b++)y.push(f[b]);S.push(y),f=d.exec(C)}return S},i=function(C){const d=M.exec(C);return!(d===null||typeof d>"u")};ne.isExist=function(C){return typeof C<"u"},ne.isEmptyObject=function(C){return Object.keys(C).length===0},ne.merge=function(C,d,S){if(d){const f=Object.keys(d),y=f.length;for(let x=0;x<y;x++)S==="strict"?C[f[x]]=[d[f[x]]]:C[f[x]]=d[f[x]]}},ne.getValue=function(C){return ne.isExist(C)?C:""},ne.isName=i,ne.getAllMatches=L,ne.nameRegexp=j},"./node_modules/fast-xml-parser/src/validator.js":function(H,ne,E){const N=E("./node_modules/fast-xml-parser/src/util.js"),P={allowBooleanAttributes:!1,unpairedTags:[]};ne.validate=function(h,l){l=Object.assign({},P,l);const T=[];let s=!1,o=!1;h[0]==="\uFEFF"&&(h=h.substr(1));for(let t=0;t<h.length;t++)if(h[t]==="<"&&h[t+1]==="?"){if(t+=2,t=M(h,t),t.err)return t}else if(h[t]==="<"){let r=t;if(t++,h[t]==="!"){t=L(h,t);continue}else{let c=!1;h[t]==="/"&&(c=!0,t++);let p="";for(;t<h.length&&h[t]!==">"&&h[t]!==" "&&h[t]!=="	"&&h[t]!==`
`&&h[t]!=="\r";t++)p+=h[t];if(p=p.trim(),p[p.length-1]==="/"&&(p=p.substring(0,p.length-1),t--),!U(p)){let K;return p.trim().length===0?K="Invalid space after '<'.":K="Tag '"+p+"' is an invalid name.",b("InvalidTag",K,z(h,t))}const I=d(h,t);if(I===!1)return b("InvalidAttr","Attributes for '"+p+"' have open quote.",z(h,t));let R=I.value;if(t=I.index,R[R.length-1]==="/"){const K=t-R.length;R=R.substring(0,R.length-1);const F=f(R,l);if(F===!0)s=!0;else return b(F.err.code,F.err.msg,z(h,K+F.err.line))}else if(c)if(I.tagClosed){if(R.trim().length>0)return b("InvalidTag","Closing tag '"+p+"' can't have attributes or invalid starting.",z(h,r));if(T.length===0)return b("InvalidTag","Closing tag '"+p+"' has not been opened.",z(h,r));{const K=T.pop();if(p!==K.tagName){let F=z(h,K.tagStartPos);return b("InvalidTag","Expected closing tag '"+K.tagName+"' (opened in line "+F.line+", col "+F.col+") instead of closing tag '"+p+"'.",z(h,r))}T.length==0&&(o=!0)}}else return b("InvalidTag","Closing tag '"+p+"' doesn't have proper closing.",z(h,t));else{const K=f(R,l);if(K!==!0)return b(K.err.code,K.err.msg,z(h,t-R.length+K.err.line));if(o===!0)return b("InvalidXml","Multiple possible root nodes found.",z(h,t));l.unpairedTags.indexOf(p)!==-1||T.push({tagName:p,tagStartPos:r}),s=!0}for(t++;t<h.length;t++)if(h[t]==="<")if(h[t+1]==="!"){t++,t=L(h,t);continue}else if(h[t+1]==="?"){if(t=M(h,++t),t.err)return t}else break;else if(h[t]==="&"){const K=x(h,t);if(K==-1)return b("InvalidChar","char '&' is not expected.",z(h,t));t=K}else if(o===!0&&!j(h[t]))return b("InvalidXml","Extra text at the end",z(h,t));h[t]==="<"&&t--}}else{if(j(h[t]))continue;return b("InvalidChar","char '"+h[t]+"' is not expected.",z(h,t))}if(s){if(T.length==1)return b("InvalidTag","Unclosed tag '"+T[0].tagName+"'.",z(h,T[0].tagStartPos));if(T.length>0)return b("InvalidXml","Invalid '"+JSON.stringify(T.map(t=>t.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1})}else return b("InvalidXml","Start tag expected.",1);return!0};function j(h){return h===" "||h==="	"||h===`
`||h==="\r"}function M(h,l){const T=l;for(;l<h.length;l++)if(h[l]=="?"||h[l]==" "){const s=h.substr(T,l-T);if(l>5&&s==="xml")return b("InvalidXml","XML declaration allowed only at the start of the document.",z(h,l));if(h[l]=="?"&&h[l+1]==">"){l++;break}else continue}return l}function L(h,l){if(h.length>l+5&&h[l+1]==="-"&&h[l+2]==="-"){for(l+=3;l<h.length;l++)if(h[l]==="-"&&h[l+1]==="-"&&h[l+2]===">"){l+=2;break}}else if(h.length>l+8&&h[l+1]==="D"&&h[l+2]==="O"&&h[l+3]==="C"&&h[l+4]==="T"&&h[l+5]==="Y"&&h[l+6]==="P"&&h[l+7]==="E"){let T=1;for(l+=8;l<h.length;l++)if(h[l]==="<")T++;else if(h[l]===">"&&(T--,T===0))break}else if(h.length>l+9&&h[l+1]==="["&&h[l+2]==="C"&&h[l+3]==="D"&&h[l+4]==="A"&&h[l+5]==="T"&&h[l+6]==="A"&&h[l+7]==="["){for(l+=8;l<h.length;l++)if(h[l]==="]"&&h[l+1]==="]"&&h[l+2]===">"){l+=2;break}}return l}const i='"',C="'";function d(h,l){let T="",s="",o=!1;for(;l<h.length;l++){if(h[l]===i||h[l]===C)s===""?s=h[l]:s!==h[l]||(s="");else if(h[l]===">"&&s===""){o=!0;break}T+=h[l]}return s!==""?!1:{value:T,index:l,tagClosed:o}}const S=new RegExp(`(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['"])(([\\s\\S])*?)\\5)?`,"g");function f(h,l){const T=N.getAllMatches(h,S),s={};for(let o=0;o<T.length;o++){if(T[o][1].length===0)return b("InvalidAttr","Attribute '"+T[o][2]+"' has no space in starting.",q(T[o]));if(T[o][3]!==void 0&&T[o][4]===void 0)return b("InvalidAttr","Attribute '"+T[o][2]+"' is without value.",q(T[o]));if(T[o][3]===void 0&&!l.allowBooleanAttributes)return b("InvalidAttr","boolean attribute '"+T[o][2]+"' is not allowed.",q(T[o]));const t=T[o][2];if(!w(t))return b("InvalidAttr","Attribute '"+t+"' is an invalid name.",q(T[o]));if(!s.hasOwnProperty(t))s[t]=1;else return b("InvalidAttr","Attribute '"+t+"' is repeated.",q(T[o]))}return!0}function y(h,l){let T=/\d/;for(h[l]==="x"&&(l++,T=/[\da-fA-F]/);l<h.length;l++){if(h[l]===";")return l;if(!h[l].match(T))break}return-1}function x(h,l){if(l++,h[l]===";")return-1;if(h[l]==="#")return l++,y(h,l);let T=0;for(;l<h.length;l++,T++)if(!(h[l].match(/\w/)&&T<20)){if(h[l]===";")break;return-1}return l}function b(h,l,T){return{err:{code:h,msg:l,line:T.line||T,col:T.col}}}function w(h){return N.isName(h)}function U(h){return N.isName(h)}function z(h,l){const T=h.substring(0,l).split(/\r?\n/);return{line:T.length,col:T[T.length-1].length+1}}function q(h){return h.startIndex+h[1].length}},"./node_modules/fast-xml-parser/src/xmlbuilder/json2xml.js":function(H,ne,E){const N=E("./node_modules/fast-xml-parser/src/xmlbuilder/orderedJs2Xml.js"),P=E("./node_modules/fast-xml-parser/src/ignoreAttributes.js"),j={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(d,S){return S},attributeValueProcessor:function(d,S){return S},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:new RegExp("&","g"),val:"&amp;"},{regex:new RegExp(">","g"),val:"&gt;"},{regex:new RegExp("<","g"),val:"&lt;"},{regex:new RegExp("'","g"),val:"&apos;"},{regex:new RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[],oneListGroup:!1};function M(d){this.options=Object.assign({},j,d),this.options.ignoreAttributes===!0||this.options.attributesGroupName?this.isAttribute=function(){return!1}:(this.ignoreAttributesFn=P(this.options.ignoreAttributes),this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=C),this.processTextOrObjNode=L,this.options.format?(this.indentate=i,this.tagEndChar=`>
`,this.newLine=`
`):(this.indentate=function(){return""},this.tagEndChar=">",this.newLine="")}M.prototype.build=function(d){return this.options.preserveOrder?N(d,this.options):(Array.isArray(d)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1&&(d={[this.options.arrayNodeName]:d}),this.j2x(d,0,[]).val)},M.prototype.j2x=function(d,S,f){let y="",x="";const b=f.join(".");for(let w in d)if(Object.prototype.hasOwnProperty.call(d,w))if(typeof d[w]>"u")this.isAttribute(w)&&(x+="");else if(d[w]===null)this.isAttribute(w)?x+="":w[0]==="?"?x+=this.indentate(S)+"<"+w+"?"+this.tagEndChar:x+=this.indentate(S)+"<"+w+"/"+this.tagEndChar;else if(d[w]instanceof Date)x+=this.buildTextValNode(d[w],w,"",S);else if(typeof d[w]!="object"){const U=this.isAttribute(w);if(U&&!this.ignoreAttributesFn(U,b))y+=this.buildAttrPairStr(U,""+d[w]);else if(!U)if(w===this.options.textNodeName){let z=this.options.tagValueProcessor(w,""+d[w]);x+=this.replaceEntitiesValue(z)}else x+=this.buildTextValNode(d[w],w,"",S)}else if(Array.isArray(d[w])){const U=d[w].length;let z="",q="";for(let h=0;h<U;h++){const l=d[w][h];if(!(typeof l>"u"))if(l===null)w[0]==="?"?x+=this.indentate(S)+"<"+w+"?"+this.tagEndChar:x+=this.indentate(S)+"<"+w+"/"+this.tagEndChar;else if(typeof l=="object")if(this.options.oneListGroup){const T=this.j2x(l,S+1,f.concat(w));z+=T.val,this.options.attributesGroupName&&l.hasOwnProperty(this.options.attributesGroupName)&&(q+=T.attrStr)}else z+=this.processTextOrObjNode(l,w,S,f);else if(this.options.oneListGroup){let T=this.options.tagValueProcessor(w,l);T=this.replaceEntitiesValue(T),z+=T}else z+=this.buildTextValNode(l,w,"",S)}this.options.oneListGroup&&(z=this.buildObjectNode(z,w,q,S)),x+=z}else if(this.options.attributesGroupName&&w===this.options.attributesGroupName){const U=Object.keys(d[w]),z=U.length;for(let q=0;q<z;q++)y+=this.buildAttrPairStr(U[q],""+d[w][U[q]])}else x+=this.processTextOrObjNode(d[w],w,S,f);return{attrStr:y,val:x}},M.prototype.buildAttrPairStr=function(d,S){return S=this.options.attributeValueProcessor(d,""+S),S=this.replaceEntitiesValue(S),this.options.suppressBooleanAttributes&&S==="true"?" "+d:" "+d+'="'+S+'"'};function L(d,S,f,y){const x=this.j2x(d,f+1,y.concat(S));return d[this.options.textNodeName]!==void 0&&Object.keys(d).length===1?this.buildTextValNode(d[this.options.textNodeName],S,x.attrStr,f):this.buildObjectNode(x.val,S,x.attrStr,f)}M.prototype.buildObjectNode=function(d,S,f,y){if(d==="")return S[0]==="?"?this.indentate(y)+"<"+S+f+"?"+this.tagEndChar:this.indentate(y)+"<"+S+f+this.closeTag(S)+this.tagEndChar;{let x="</"+S+this.tagEndChar,b="";return S[0]==="?"&&(b="?",x=""),(f||f==="")&&d.indexOf("<")===-1?this.indentate(y)+"<"+S+f+b+">"+d+x:this.options.commentPropName!==!1&&S===this.options.commentPropName&&b.length===0?this.indentate(y)+`<!--${d}-->`+this.newLine:this.indentate(y)+"<"+S+f+b+this.tagEndChar+d+this.indentate(y)+x}},M.prototype.closeTag=function(d){let S="";return this.options.unpairedTags.indexOf(d)!==-1?this.options.suppressUnpairedNode||(S="/"):this.options.suppressEmptyNode?S="/":S=`></${d}`,S},M.prototype.buildTextValNode=function(d,S,f,y){if(this.options.cdataPropName!==!1&&S===this.options.cdataPropName)return this.indentate(y)+`<![CDATA[${d}]]>`+this.newLine;if(this.options.commentPropName!==!1&&S===this.options.commentPropName)return this.indentate(y)+`<!--${d}-->`+this.newLine;if(S[0]==="?")return this.indentate(y)+"<"+S+f+"?"+this.tagEndChar;{let x=this.options.tagValueProcessor(S,d);return x=this.replaceEntitiesValue(x),x===""?this.indentate(y)+"<"+S+f+this.closeTag(S)+this.tagEndChar:this.indentate(y)+"<"+S+f+">"+x+"</"+S+this.tagEndChar}},M.prototype.replaceEntitiesValue=function(d){if(d&&d.length>0&&this.options.processEntities)for(let S=0;S<this.options.entities.length;S++){const f=this.options.entities[S];d=d.replace(f.regex,f.val)}return d};function i(d){return this.options.indentBy.repeat(d)}function C(d){return d.startsWith(this.options.attributeNamePrefix)&&d!==this.options.textNodeName?d.substr(this.attrPrefixLen):!1}H.exports=M},"./node_modules/fast-xml-parser/src/xmlbuilder/orderedJs2Xml.js":function(H,ne){const E=`
`;function N(C,d){let S="";return d.format&&d.indentBy.length>0&&(S=E),P(C,d,"",S)}function P(C,d,S,f){let y="",x=!1;for(let b=0;b<C.length;b++){const w=C[b],U=j(w);if(U===void 0)continue;let z="";if(S.length===0?z=U:z=`${S}.${U}`,U===d.textNodeName){let s=w[U];L(z,d)||(s=d.tagValueProcessor(U,s),s=i(s,d)),x&&(y+=f),y+=s,x=!1;continue}else if(U===d.cdataPropName){x&&(y+=f),y+=`<![CDATA[${w[U][0][d.textNodeName]}]]>`,x=!1;continue}else if(U===d.commentPropName){y+=f+`<!--${w[U][0][d.textNodeName]}-->`,x=!0;continue}else if(U[0]==="?"){const s=M(w[":@"],d),o=U==="?xml"?"":f;let t=w[U][0][d.textNodeName];t=t.length!==0?" "+t:"",y+=o+`<${U}${t}${s}?>`,x=!0;continue}let q=f;q!==""&&(q+=d.indentBy);const h=M(w[":@"],d),l=f+`<${U}${h}`,T=P(w[U],d,z,q);d.unpairedTags.indexOf(U)!==-1?d.suppressUnpairedNode?y+=l+">":y+=l+"/>":(!T||T.length===0)&&d.suppressEmptyNode?y+=l+"/>":T&&T.endsWith(">")?y+=l+`>${T}${f}</${U}>`:(y+=l+">",T&&f!==""&&(T.includes("/>")||T.includes("</"))?y+=f+d.indentBy+T+f:y+=T,y+=`</${U}>`),x=!0}return y}function j(C){const d=Object.keys(C);for(let S=0;S<d.length;S++){const f=d[S];if(C.hasOwnProperty(f)&&f!==":@")return f}}function M(C,d){let S="";if(C&&!d.ignoreAttributes)for(let f in C){if(!C.hasOwnProperty(f))continue;let y=d.attributeValueProcessor(f,C[f]);y=i(y,d),y===!0&&d.suppressBooleanAttributes?S+=` ${f.substr(d.attributeNamePrefix.length)}`:S+=` ${f.substr(d.attributeNamePrefix.length)}="${y}"`}return S}function L(C,d){C=C.substr(0,C.length-d.textNodeName.length-1);let S=C.substr(C.lastIndexOf(".")+1);for(let f in d.stopNodes)if(d.stopNodes[f]===C||d.stopNodes[f]==="*."+S)return!0;return!1}function i(C,d){if(C&&C.length>0&&d.processEntities)for(let S=0;S<d.entities.length;S++){const f=d.entities[S];C=C.replace(f.regex,f.val)}return C}H.exports=N},"./node_modules/fast-xml-parser/src/xmlparser/DocTypeReader.js":function(H,ne,E){const N=E("./node_modules/fast-xml-parser/src/util.js");function P(f,y){const x={};if(f[y+3]==="O"&&f[y+4]==="C"&&f[y+5]==="T"&&f[y+6]==="Y"&&f[y+7]==="P"&&f[y+8]==="E"){y=y+9;let b=1,w=!1,U=!1,z="";for(;y<f.length;y++)if(f[y]==="<"&&!U){if(w&&L(f,y))y+=7,[entityName,val,y]=j(f,y+1),val.indexOf("&")===-1&&(x[S(entityName)]={regx:RegExp(`&${entityName};`,"g"),val});else if(w&&i(f,y))y+=8;else if(w&&C(f,y))y+=8;else if(w&&d(f,y))y+=9;else if(M)U=!0;else throw new Error("Invalid DOCTYPE");b++,z=""}else if(f[y]===">"){if(U?f[y-1]==="-"&&f[y-2]==="-"&&(U=!1,b--):b--,b===0)break}else f[y]==="["?w=!0:z+=f[y];if(b!==0)throw new Error("Unclosed DOCTYPE")}else throw new Error("Invalid Tag instead of DOCTYPE");return{entities:x,i:y}}function j(f,y){let x="";for(;y<f.length&&f[y]!=="'"&&f[y]!=='"';y++)x+=f[y];if(x=x.trim(),x.indexOf(" ")!==-1)throw new Error("External entites are not supported");const b=f[y++];let w="";for(;y<f.length&&f[y]!==b;y++)w+=f[y];return[x,w,y]}function M(f,y){return f[y+1]==="!"&&f[y+2]==="-"&&f[y+3]==="-"}function L(f,y){return f[y+1]==="!"&&f[y+2]==="E"&&f[y+3]==="N"&&f[y+4]==="T"&&f[y+5]==="I"&&f[y+6]==="T"&&f[y+7]==="Y"}function i(f,y){return f[y+1]==="!"&&f[y+2]==="E"&&f[y+3]==="L"&&f[y+4]==="E"&&f[y+5]==="M"&&f[y+6]==="E"&&f[y+7]==="N"&&f[y+8]==="T"}function C(f,y){return f[y+1]==="!"&&f[y+2]==="A"&&f[y+3]==="T"&&f[y+4]==="T"&&f[y+5]==="L"&&f[y+6]==="I"&&f[y+7]==="S"&&f[y+8]==="T"}function d(f,y){return f[y+1]==="!"&&f[y+2]==="N"&&f[y+3]==="O"&&f[y+4]==="T"&&f[y+5]==="A"&&f[y+6]==="T"&&f[y+7]==="I"&&f[y+8]==="O"&&f[y+9]==="N"}function S(f){if(N.isName(f))return f;throw new Error(`Invalid entity name ${f}`)}H.exports=P},"./node_modules/fast-xml-parser/src/xmlparser/OptionsBuilder.js":function(H,ne){const E={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(P,j){return j},attributeValueProcessor:function(P,j){return j},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(P,j,M){return P}},N=function(P){return Object.assign({},E,P)};ne.buildOptions=N,ne.defaultOptions=E},"./node_modules/fast-xml-parser/src/xmlparser/OrderedObjParser.js":function(H,ne,E){const N=E("./node_modules/fast-xml-parser/src/util.js"),P=E("./node_modules/fast-xml-parser/src/xmlparser/xmlNode.js"),j=E("./node_modules/fast-xml-parser/src/xmlparser/DocTypeReader.js"),M=E("./node_modules/strnum/strnum.js"),L=E("./node_modules/fast-xml-parser/src/ignoreAttributes.js");class i{constructor(t){this.options=t,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"¢"},pound:{regex:/&(pound|#163);/g,val:"£"},yen:{regex:/&(yen|#165);/g,val:"¥"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"©"},reg:{regex:/&(reg|#174);/g,val:"®"},inr:{regex:/&(inr|#8377);/g,val:"₹"},num_dec:{regex:/&#([0-9]{1,7});/g,val:(r,c)=>String.fromCharCode(Number.parseInt(c,10))},num_hex:{regex:/&#x([0-9a-fA-F]{1,6});/g,val:(r,c)=>String.fromCharCode(Number.parseInt(c,16))}},this.addExternalEntities=C,this.parseXml=x,this.parseTextData=d,this.resolveNameSpace=S,this.buildAttributesMap=y,this.isItStopNode=z,this.replaceEntitiesValue=w,this.readStopNodeData=T,this.saveTextToParentTag=U,this.addChild=b,this.ignoreAttributesFn=L(this.options.ignoreAttributes)}}function C(o){const t=Object.keys(o);for(let r=0;r<t.length;r++){const c=t[r];this.lastEntities[c]={regex:new RegExp("&"+c+";","g"),val:o[c]}}}function d(o,t,r,c,p,I,R){if(o!==void 0&&(this.options.trimValues&&!c&&(o=o.trim()),o.length>0)){R||(o=this.replaceEntitiesValue(o));const K=this.options.tagValueProcessor(t,o,r,p,I);return K==null?o:typeof K!=typeof o||K!==o?K:this.options.trimValues?s(o,this.options.parseTagValue,this.options.numberParseOptions):o.trim()===o?s(o,this.options.parseTagValue,this.options.numberParseOptions):o}}function S(o){if(this.options.removeNSPrefix){const t=o.split(":"),r=o.charAt(0)==="/"?"/":"";if(t[0]==="xmlns")return"";t.length===2&&(o=r+t[1])}return o}const f=new RegExp(`([^\\s=]+)\\s*(=\\s*(['"])([\\s\\S]*?)\\3)?`,"gm");function y(o,t,r){if(this.options.ignoreAttributes!==!0&&typeof o=="string"){const c=N.getAllMatches(o,f),p=c.length,I={};for(let R=0;R<p;R++){const K=this.resolveNameSpace(c[R][1]);if(this.ignoreAttributesFn(K,t))continue;let F=c[R][4],J=this.options.attributeNamePrefix+K;if(K.length)if(this.options.transformAttributeName&&(J=this.options.transformAttributeName(J)),J==="__proto__"&&(J="#__proto__"),F!==void 0){this.options.trimValues&&(F=F.trim()),F=this.replaceEntitiesValue(F);const Q=this.options.attributeValueProcessor(K,F,t);Q==null?I[J]=F:typeof Q!=typeof F||Q!==F?I[J]=Q:I[J]=s(F,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(I[J]=!0)}if(!Object.keys(I).length)return;if(this.options.attributesGroupName){const R={};return R[this.options.attributesGroupName]=I,R}return I}}const x=function(o){const t=new P("!xml");let r=t,c="",p="";for(let I=0;I<o.length;I++)if(o[I]==="<")if(o[I+1]==="/"){const K=h(o,">",I,"Closing Tag is not closed.");let F=o.substring(I+2,K).trim();if(this.options.removeNSPrefix){const ee=F.indexOf(":");ee!==-1&&(F=F.substr(ee+1))}this.options.transformTagName&&(F=this.options.transformTagName(F)),r&&(c=this.saveTextToParentTag(c,r,p));const J=p.substring(p.lastIndexOf(".")+1);if(F&&this.options.unpairedTags.indexOf(F)!==-1)throw new Error(`Unpaired tag can not be used as closing tag: </${F}>`);let Q=0;J&&this.options.unpairedTags.indexOf(J)!==-1?(Q=p.lastIndexOf(".",p.lastIndexOf(".")-1),this.tagsNodeStack.pop()):Q=p.lastIndexOf("."),p=p.substring(0,Q),r=this.tagsNodeStack.pop(),c="",I=K}else if(o[I+1]==="?"){let K=l(o,I,!1,"?>");if(!K)throw new Error("Pi Tag is not closed.");if(c=this.saveTextToParentTag(c,r,p),!(this.options.ignoreDeclaration&&K.tagName==="?xml"||this.options.ignorePiTags)){const F=new P(K.tagName);F.add(this.options.textNodeName,""),K.tagName!==K.tagExp&&K.attrExpPresent&&(F[":@"]=this.buildAttributesMap(K.tagExp,p,K.tagName)),this.addChild(r,F,p)}I=K.closeIndex+1}else if(o.substr(I+1,3)==="!--"){const K=h(o,"-->",I+4,"Comment is not closed.");if(this.options.commentPropName){const F=o.substring(I+4,K-2);c=this.saveTextToParentTag(c,r,p),r.add(this.options.commentPropName,[{[this.options.textNodeName]:F}])}I=K}else if(o.substr(I+1,2)==="!D"){const K=j(o,I);this.docTypeEntities=K.entities,I=K.i}else if(o.substr(I+1,2)==="!["){const K=h(o,"]]>",I,"CDATA is not closed.")-2,F=o.substring(I+9,K);c=this.saveTextToParentTag(c,r,p);let J=this.parseTextData(F,r.tagname,p,!0,!1,!0,!0);J==null&&(J=""),this.options.cdataPropName?r.add(this.options.cdataPropName,[{[this.options.textNodeName]:F}]):r.add(this.options.textNodeName,J),I=K+2}else{let K=l(o,I,this.options.removeNSPrefix),F=K.tagName;const J=K.rawTagName;let Q=K.tagExp,ee=K.attrExpPresent,te=K.closeIndex;this.options.transformTagName&&(F=this.options.transformTagName(F)),r&&c&&r.tagname!=="!xml"&&(c=this.saveTextToParentTag(c,r,p,!1));const Ce=r;if(Ce&&this.options.unpairedTags.indexOf(Ce.tagname)!==-1&&(r=this.tagsNodeStack.pop(),p=p.substring(0,p.lastIndexOf("."))),F!==t.tagname&&(p+=p?"."+F:F),this.isItStopNode(this.options.stopNodes,p,F)){let ae="";if(Q.length>0&&Q.lastIndexOf("/")===Q.length-1)F[F.length-1]==="/"?(F=F.substr(0,F.length-1),p=p.substr(0,p.length-1),Q=F):Q=Q.substr(0,Q.length-1),I=K.closeIndex;else if(this.options.unpairedTags.indexOf(F)!==-1)I=K.closeIndex;else{const Z=this.readStopNodeData(o,J,te+1);if(!Z)throw new Error(`Unexpected end of ${J}`);I=Z.i,ae=Z.tagContent}const ie=new P(F);F!==Q&&ee&&(ie[":@"]=this.buildAttributesMap(Q,p,F)),ae&&(ae=this.parseTextData(ae,F,p,!0,ee,!0,!0)),p=p.substr(0,p.lastIndexOf(".")),ie.add(this.options.textNodeName,ae),this.addChild(r,ie,p)}else{if(Q.length>0&&Q.lastIndexOf("/")===Q.length-1){F[F.length-1]==="/"?(F=F.substr(0,F.length-1),p=p.substr(0,p.length-1),Q=F):Q=Q.substr(0,Q.length-1),this.options.transformTagName&&(F=this.options.transformTagName(F));const ae=new P(F);F!==Q&&ee&&(ae[":@"]=this.buildAttributesMap(Q,p,F)),this.addChild(r,ae,p),p=p.substr(0,p.lastIndexOf("."))}else{const ae=new P(F);this.tagsNodeStack.push(r),F!==Q&&ee&&(ae[":@"]=this.buildAttributesMap(Q,p,F)),this.addChild(r,ae,p),r=ae}c="",I=te}}else c+=o[I];return t.child};function b(o,t,r){const c=this.options.updateTag(t.tagname,r,t[":@"]);c===!1||(typeof c=="string"&&(t.tagname=c),o.addChild(t))}const w=function(o){if(this.options.processEntities){for(let t in this.docTypeEntities){const r=this.docTypeEntities[t];o=o.replace(r.regx,r.val)}for(let t in this.lastEntities){const r=this.lastEntities[t];o=o.replace(r.regex,r.val)}if(this.options.htmlEntities)for(let t in this.htmlEntities){const r=this.htmlEntities[t];o=o.replace(r.regex,r.val)}o=o.replace(this.ampEntity.regex,this.ampEntity.val)}return o};function U(o,t,r,c){return o&&(c===void 0&&(c=Object.keys(t.child).length===0),o=this.parseTextData(o,t.tagname,r,!1,t[":@"]?Object.keys(t[":@"]).length!==0:!1,c),o!==void 0&&o!==""&&t.add(this.options.textNodeName,o),o=""),o}function z(o,t,r){const c="*."+r;for(const p in o){const I=o[p];if(c===I||t===I)return!0}return!1}function q(o,t,r=">"){let c,p="";for(let I=t;I<o.length;I++){let R=o[I];if(c)R===c&&(c="");else if(R==='"'||R==="'")c=R;else if(R===r[0])if(r[1]){if(o[I+1]===r[1])return{data:p,index:I}}else return{data:p,index:I};else R==="	"&&(R=" ");p+=R}}function h(o,t,r,c){const p=o.indexOf(t,r);if(p===-1)throw new Error(c);return p+t.length-1}function l(o,t,r,c=">"){const p=q(o,t+1,c);if(!p)return;let I=p.data;const R=p.index,K=I.search(/\s/);let F=I,J=!0;K!==-1&&(F=I.substring(0,K),I=I.substring(K+1).trimStart());const Q=F;if(r){const ee=F.indexOf(":");ee!==-1&&(F=F.substr(ee+1),J=F!==p.data.substr(ee+1))}return{tagName:F,tagExp:I,closeIndex:R,attrExpPresent:J,rawTagName:Q}}function T(o,t,r){const c=r;let p=1;for(;r<o.length;r++)if(o[r]==="<")if(o[r+1]==="/"){const I=h(o,">",r,`${t} is not closed`);if(o.substring(r+2,I).trim()===t&&(p--,p===0))return{tagContent:o.substring(c,r),i:I};r=I}else if(o[r+1]==="?")r=h(o,"?>",r+1,"StopNode is not closed.");else if(o.substr(r+1,3)==="!--")r=h(o,"-->",r+3,"StopNode is not closed.");else if(o.substr(r+1,2)==="![")r=h(o,"]]>",r,"StopNode is not closed.")-2;else{const I=l(o,r,">");I&&((I&&I.tagName)===t&&I.tagExp[I.tagExp.length-1]!=="/"&&p++,r=I.closeIndex)}}function s(o,t,r){if(t&&typeof o=="string"){const c=o.trim();return c==="true"?!0:c==="false"?!1:M(o,r)}else return N.isExist(o)?o:""}H.exports=i},"./node_modules/fast-xml-parser/src/xmlparser/XMLParser.js":function(H,ne,E){const{buildOptions:N}=E("./node_modules/fast-xml-parser/src/xmlparser/OptionsBuilder.js"),P=E("./node_modules/fast-xml-parser/src/xmlparser/OrderedObjParser.js"),{prettify:j}=E("./node_modules/fast-xml-parser/src/xmlparser/node2json.js"),M=E("./node_modules/fast-xml-parser/src/validator.js");class L{constructor(C){this.externalEntities={},this.options=N(C)}parse(C,d){if(typeof C!="string")if(C.toString)C=C.toString();else throw new Error("XML data is accepted in String or Bytes[] form.");if(d){d===!0&&(d={});const y=M.validate(C,d);if(y!==!0)throw Error(`${y.err.msg}:${y.err.line}:${y.err.col}`)}const S=new P(this.options);S.addExternalEntities(this.externalEntities);const f=S.parseXml(C);return this.options.preserveOrder||f===void 0?f:j(f,this.options)}addEntity(C,d){if(d.indexOf("&")!==-1)throw new Error("Entity value can't have '&'");if(C.indexOf("&")!==-1||C.indexOf(";")!==-1)throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if(d==="&")throw new Error("An entity with value '&' is not permitted");this.externalEntities[C]=d}}H.exports=L},"./node_modules/fast-xml-parser/src/xmlparser/node2json.js":function(H,ne,E){function N(i,C){return P(i,C)}function P(i,C,d){let S;const f={};for(let y=0;y<i.length;y++){const x=i[y],b=j(x);let w="";if(d===void 0?w=b:w=d+"."+b,b===C.textNodeName)S===void 0?S=x[b]:S+=""+x[b];else{if(b===void 0)continue;if(x[b]){let U=P(x[b],C,w);const z=L(U,C);x[":@"]?M(U,x[":@"],w,C):Object.keys(U).length===1&&U[C.textNodeName]!==void 0&&!C.alwaysCreateTextNode?U=U[C.textNodeName]:Object.keys(U).length===0&&(C.alwaysCreateTextNode?U[C.textNodeName]="":U=""),f[b]!==void 0&&f.hasOwnProperty(b)?(Array.isArray(f[b])||(f[b]=[f[b]]),f[b].push(U)):C.isArray(b,w,z)?f[b]=[U]:f[b]=U}}}return typeof S=="string"?S.length>0&&(f[C.textNodeName]=S):S!==void 0&&(f[C.textNodeName]=S),f}function j(i){const C=Object.keys(i);for(let d=0;d<C.length;d++){const S=C[d];if(S!==":@")return S}}function M(i,C,d,S){if(C){const f=Object.keys(C),y=f.length;for(let x=0;x<y;x++){const b=f[x];S.isArray(b,d+"."+b,!0,!0)?i[b]=[C[b]]:i[b]=C[b]}}}function L(i,C){const{textNodeName:d}=C,S=Object.keys(i).length;return!!(S===0||S===1&&(i[d]||typeof i[d]=="boolean"||i[d]===0))}ne.prettify=N},"./node_modules/fast-xml-parser/src/xmlparser/xmlNode.js":function(H,ne,E){class N{constructor(j){this.tagname=j,this.child=[],this[":@"]={}}add(j,M){j==="__proto__"&&(j="#__proto__"),this.child.push({[j]:M})}addChild(j){j.tagname==="__proto__"&&(j.tagname="#__proto__"),j[":@"]&&Object.keys(j[":@"]).length>0?this.child.push({[j.tagname]:j.child,":@":j[":@"]}):this.child.push({[j.tagname]:j.child})}}H.exports=N},"./node_modules/process/browser.js":function(H,ne){var E=H.exports={},N,P;function j(){throw new Error("setTimeout has not been defined")}function M(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?N=setTimeout:N=j}catch{N=j}try{typeof clearTimeout=="function"?P=clearTimeout:P=M}catch{P=M}})();function L(U){if(N===setTimeout)return setTimeout(U,0);if((N===j||!N)&&setTimeout)return N=setTimeout,setTimeout(U,0);try{return N(U,0)}catch{try{return N.call(null,U,0)}catch{return N.call(this,U,0)}}}function i(U){if(P===clearTimeout)return clearTimeout(U);if((P===M||!P)&&clearTimeout)return P=clearTimeout,clearTimeout(U);try{return P(U)}catch{try{return P.call(null,U)}catch{return P.call(this,U)}}}var C=[],d=!1,S,f=-1;function y(){!d||!S||(d=!1,S.length?C=S.concat(C):f=-1,C.length&&x())}function x(){if(!d){var U=L(y);d=!0;for(var z=C.length;z;){for(S=C,C=[];++f<z;)S&&S[f].run();f=-1,z=C.length}S=null,d=!1,i(U)}}E.nextTick=function(U){var z=new Array(arguments.length-1);if(arguments.length>1)for(var q=1;q<arguments.length;q++)z[q-1]=arguments[q];C.push(new b(U,z)),C.length===1&&!d&&L(x)};function b(U,z){this.fun=U,this.array=z}b.prototype.run=function(){this.fun.apply(null,this.array)},E.title="browser",E.browser=!0,E.env={},E.argv=[],E.version="",E.versions={};function w(){}E.on=w,E.addListener=w,E.once=w,E.off=w,E.removeListener=w,E.removeAllListeners=w,E.emit=w,E.prependListener=w,E.prependOnceListener=w,E.listeners=function(U){return[]},E.binding=function(U){throw new Error("process.binding is not supported")},E.cwd=function(){return"/"},E.chdir=function(U){throw new Error("process.chdir is not supported")},E.umask=function(){return 0}},"./node_modules/strnum/strnum.js":function(H,ne){const E=/^[-+]?0x[a-fA-F0-9]+$/,N=/^([\-\+])?(0*)([0-9]*(\.[0-9]*)?)$/,P={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};function j(i,C={}){if(C=Object.assign({},P,C),!i||typeof i!="string")return i;let d=i.trim();if(C.skipLike!==void 0&&C.skipLike.test(d))return i;if(i==="0")return 0;if(C.hex&&E.test(d))return L(d,16);if(d.search(/[eE]/)!==-1){const S=d.match(/^([-\+])?(0*)([0-9]*(\.[0-9]*)?[eE][-\+]?[0-9]+)$/);if(S){if(C.leadingZeros)d=(S[1]||"")+S[3];else if(!(S[2]==="0"&&S[3][0]==="."))return i;return C.eNotation?Number(d):i}else return i}else{const S=N.exec(d);if(S){const f=S[1],y=S[2];let x=M(S[3]);if(!C.leadingZeros&&y.length>0&&f&&d[2]!==".")return i;if(!C.leadingZeros&&y.length>0&&!f&&d[1]!==".")return i;if(C.leadingZeros&&y===i)return 0;{const b=Number(d),w=""+b;return w.search(/[eE]/)!==-1?C.eNotation?b:i:d.indexOf(".")!==-1?w==="0"&&x===""||w===x||f&&w==="-"+x?b:i:y?x===w||f+x===w?b:i:d===w||d===f+w?b:i}}else return i}}function M(i){return i&&i.indexOf(".")!==-1&&(i=i.replace(/0+$/,""),i==="."?i="0":i[0]==="."?i="0"+i:i[i.length-1]==="."&&(i=i.substr(0,i.length-1))),i}function L(i,C){if(parseInt)return parseInt(i,C);if(Number.parseInt)return Number.parseInt(i,C);if(window&&window.parseInt)return window.parseInt(i,C);throw new Error("parseInt, Number.parseInt, window.parseInt are not supported")}H.exports=j},"./node_modules/webpack/buildin/amd-options.js":function(H,ne){(function(E){H.exports=E}).call(this,{})},"./node_modules/webpack/buildin/module.js":function(H,ne){H.exports=function(E){return E.webpackPolyfill||(E.deprecate=function(){},E.paths=[],E.children||(E.children=[]),Object.defineProperty(E,"loaded",{enumerable:!0,get:function(){return E.l}}),Object.defineProperty(E,"id",{enumerable:!0,get:function(){return E.i}}),E.webpackPolyfill=1),E}},"./package.json":function(H){H.exports=JSON.parse('{"name":"cos-js-sdk-v5","version":"1.10.1","description":"JavaScript SDK for [腾讯云对象存储](https://cloud.tencent.com/product/cos)","main":"dist/cos-js-sdk-v5.js","types":"index.d.ts","scripts":{"prettier":"prettier --write src demo/demo.js demo/CIDemos/*.js test/test.js server/sts.js lib/request.js index.d.ts","server":"node server/sts.js","dev":"cross-env NODE_ENV=development webpack -w --mode=development","build":"cross-env NODE_ENV=production webpack --mode=production","cos-auth.min.js":"uglifyjs ./demo/common/cos-auth.js -o ./demo/common/cos-auth.min.js -c -m","test":"jest --runInBand --coverage","postinstall":"node scripts/patch-check.js"},"repository":{"type":"git","url":"git+https://github.com/tencentyun/cos-js-sdk-v5.git"},"keywords":[],"author":"carsonxu","license":"ISC","bugs":{"url":"https://github.com/tencentyun/cos-js-sdk-v5/issues"},"homepage":"https://github.com/tencentyun/cos-js-sdk-v5#readme","dependencies":{"fast-xml-parser":"4.5.0"},"devDependencies":{"@babel/core":"7.17.9","@babel/plugin-transform-runtime":"7.18.10","@babel/preset-env":"7.16.11","babel-loader":"8.2.5","body-parser":"^1.18.3","cross-env":"^5.2.0","express":"^4.16.4","jest":"29.7.0","jest-environment-jsdom":"29.7.0","patch-package":"^8.0.0","prettier":"^3.0.1","qcloud-cos-sts":"^3.0.2","request":"^2.87.0","terser-webpack-plugin":"4.2.3","uglifyjs":"^2.4.11","webpack":"4.46.0","webpack-cli":"4.10.0"}}')},"./src/advance.js":function(H,ne,E){var N=E("./node_modules/@babel/runtime/helpers/typeof.js"),P=E("./src/session.js"),j=E("./src/async.js"),M=E("./src/event.js").EventProxy,L=E("./src/util.js"),i=E("./src/tracker.js");function C(s,o){var t=this,r=new M,c=s.TaskId,p=s.Bucket,I=s.Region,R=s.Key,K=s.Body,F=s.ChunkSize||s.SliceSize||t.options.ChunkSize,J=s.AsyncLimit,Q=s.StorageClass,ee=s.ServerSideEncryption,te,Ce,ae=s.onHashProgress,ie=s.tracker;ie&&ie.setParams({chunkSize:F}),t.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 分块上传开始")}),r.on("error",function(Z){if(t._isRunningTask(c))return s.UploadData.UploadId&&P.removeUsing(s.UploadData.UploadId),Z.UploadId=s.UploadData.UploadId||"",t.logger.error({cate:"RESULT",tag:"upload",msg:"[key=".concat(s.Key,"] 分块上传失败: ").concat(JSON.stringify(Z))}),o(Z)}),r.on("upload_complete",function(Z){var D=L.extend({UploadId:s.UploadData.UploadId||""},Z);o(null,D)}),r.on("upload_slice_complete",function(Z){var D={};L.each(s.Headers,function(Y,le){var de=le.toLowerCase();(de.indexOf("x-cos-meta-")===0||["pic-operations","x-cos-callback","x-cos-callback-var","x-cos-return-body"].includes(de))&&(D[le]=Y)}),t.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 开始完成分块请求")}),b.call(t,{Bucket:p,Region:I,Key:R,UploadId:Z.UploadId,SliceList:Z.SliceList,Headers:D,tracker:ie},function(Y,le){if(t._isRunningTask(c)){if(P.removeUsing(Z.UploadId),Y)return Ce(null,!0),t.logger.error({cate:"RESULT",tag:"upload",msg:"[key=".concat(s.Key,"] 完成分块请求失败")}),r.emit("error",Y);P.removeUploadId.call(t,Z.UploadId),Ce({loaded:te,total:te},!0),t.logger.info({cate:"RESULT",tag:"upload",msg:"[key=".concat(s.Key,"] 完成分块请求成功")}),r.emit("upload_complete",le)}})}),r.on("get_upload_data_finish",function(Z){var D=P.getFileId(K,s.ChunkSize,p,R);D&&P.saveUploadId.call(t,D,Z.UploadId,t.options.UploadIdCacheLimit),P.setUsing(Z.UploadId),Ce(null,!0),t.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 开始上传各个分块")}),y.call(t,{TaskId:c,Bucket:p,Region:I,Key:R,Body:K,FileSize:te,SliceSize:F,AsyncLimit:J,ServerSideEncryption:ee,UploadData:Z,Headers:s.Headers,onProgress:Ce,tracker:ie},function(Y,le){if(t._isRunningTask(c)){if(Y)return Ce(null,!0),t.logger.error({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 分块上传失败")}),r.emit("error",Y);t.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 所有分块上传完成")}),r.emit("upload_slice_complete",le)}})}),r.on("get_file_size_finish",function(){if(Ce=L.throttleOnProgress.call(t,te,s.onProgress),s.UploadData.UploadId)t.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 已经获取到 uploadId, ").concat(s.UploadData.UploadId)}),r.emit("get_upload_data_finish",s.UploadData);else{var Z=L.extend({TaskId:c,Bucket:p,Region:I,Key:R,Headers:s.Headers,StorageClass:Q,Body:K,FileSize:te,SliceSize:F,onHashProgress:ae,tracker:ie},s);t.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 去获取 uploadId")}),d.call(t,Z,function(D,Y){if(t._isRunningTask(c)){if(D)return r.emit("error",D);s.UploadData.UploadId=Y.UploadId,s.UploadData.PartList=Y.PartList,t.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 获取到 uploadId, ").concat(s.UploadData.UploadId)}),r.emit("get_upload_data_finish",s.UploadData)}})}}),te=s.ContentLength,delete s.ContentLength,!s.Headers&&(s.Headers={}),L.each(s.Headers,function(Z,D){D.toLowerCase()==="content-length"&&delete s.Headers[D]}),function(){for(var Z=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],D=1024*1024,Y=0;Y<Z.length&&(D=Z[Y]*1024*1024,!(te/D<=t.options.MaxPartNumber));Y++);s.ChunkSize=s.SliceSize=F=Math.max(F,D)}(),te===0?(s.Body="",s.ContentLength=0,s.SkipTask=!0,t.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 文件大小为 0，执行简单上传")}),t.putObject(s,o)):r.emit("get_file_size_finish")}function d(s,o){var t=s.TaskId,r=s.Bucket,c=s.Region,p=s.Key,I=s.StorageClass,R=this,K={},F=s.FileSize,J=s.SliceSize,Q=Math.ceil(F/J),ee=0,te=L.throttleOnProgress.call(R,F,s.onHashProgress),Ce=function(D,Y){var le=J*(D-1),de=Math.min(le+J,F),oe=de-le;K[D]?Y(null,{PartNumber:D,ETag:K[D],Size:oe}):L.fileSlice(s.Body,le,de,!1,function(ue){L.getFileMd5(ue,function(he,ge){if(he)return Y(L.error(he));var pe='"'+ge+'"';K[D]=pe,ee+=oe,te({loaded:ee,total:F}),Y(null,{PartNumber:D,ETag:pe,Size:oe})})})},ae=function(D,Y){var le=D.length;if(le===0)return Y(null,!0);if(le>Q)return Y(null,!1);if(le>1){var de=Math.max(D[0].Size,D[1].Size);if(de!==J)return Y(null,!1)}var oe=function(he){if(he<le){var ge=D[he];Ce(ge.PartNumber,function(pe,Be){Be&&Be.ETag===ge.ETag&&Be.Size===ge.Size?oe(he+1):Y(null,!1)})}else Y(null,!0)};oe(0)},ie=new M;ie.on("error",function(Z){if(R._isRunningTask(t))return o(Z)}),ie.on("upload_id_available",function(Z){var D={},Y=[];L.each(Z.PartList,function(oe){D[oe.PartNumber]=oe});for(var le=1;le<=Q;le++){var de=D[le];de?(de.PartNumber=le,de.Uploaded=!0):de={PartNumber:le,ETag:null,Uploaded:!1},Y.push(de)}Z.PartList=Y,o(null,Z)}),ie.on("no_available_upload_id",function(){if(R._isRunningTask(t)){var Z=L.extend({Bucket:r,Region:c,Key:p,Query:L.clone(s.Query),StorageClass:I,Body:s.Body,calledBySdk:"sliceUploadFile",tracker:s.tracker},s),D=L.clone(s.Headers);delete D["x-cos-mime-limit"],Z.Headers=D,R.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 准备初始化分块上传")}),R.multipartInit(Z,function(Y,le){if(R._isRunningTask(t)){if(Y)return R.logger.error({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 初始化分块上传失败, ").concat(JSON.stringify(Y))}),ie.emit("error",Y);var de=le.UploadId;if(!de)return o(L.error(new Error("no such upload id")));R.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 初始化分块上传成功")}),ie.emit("upload_id_available",{UploadId:de,PartList:[]})}})}}),ie.on("has_and_check_upload_id",function(Z){Z=Z.reverse(),j.eachLimit(Z,1,function(D,Y){if(R._isRunningTask(t)){if(P.using[D]){Y();return}f.call(R,{Bucket:r,Region:c,Key:p,UploadId:D,tracker:s.tracker},function(le,de){if(R._isRunningTask(t)){if(le)return P.removeUsing(D),ie.emit("error",le);var oe=de.PartList;oe.forEach(function(ue){ue.PartNumber*=1,ue.Size*=1,ue.ETag=ue.ETag||""}),ae(oe,function(ue,he){if(R._isRunningTask(t)){if(ue)return ie.emit("error",ue);he?Y({UploadId:D,PartList:oe}):Y()}})}})}},function(D){R._isRunningTask(t)&&(te(null,!0),D&&D.UploadId?ie.emit("upload_id_available",D):ie.emit("no_available_upload_id"))})}),ie.on("seek_local_avail_upload_id",function(Z){var D=P.getFileId(s.Body,s.ChunkSize,r,p),Y=P.getUploadIdList.call(R,D);if(!D||!Y){ie.emit("has_and_check_upload_id",Z);return}var le=function(oe){if(oe>=Y.length){ie.emit("has_and_check_upload_id",Z);return}var ue=Y[oe];if(!L.isInArray(Z,ue)){P.removeUploadId.call(R,ue),le(oe+1);return}if(P.using[ue]){le(oe+1);return}f.call(R,{Bucket:r,Region:c,Key:p,UploadId:ue,tracker:s.tracker},function(he,ge){R._isRunningTask(t)&&(he?(P.removeUploadId.call(R,ue),le(oe+1)):ie.emit("upload_id_available",{UploadId:ue,PartList:ge.PartList}))})};le(0)}),ie.on("get_remote_upload_id_list",function(){S.call(R,{Bucket:r,Region:c,Key:p,tracker:s.tracker},function(Z,D){if(R._isRunningTask(t)){if(Z)return ie.emit("error",Z);var Y=L.filter(D.UploadList,function(oe){return oe.Key===p&&(!I||oe.StorageClass.toUpperCase()===I.toUpperCase())}).reverse().map(function(oe){return oe.UploadId||oe.UploadID});if(Y.length)ie.emit("seek_local_avail_upload_id",Y);else{var le=P.getFileId(s.Body,s.ChunkSize,r,p),de;le&&(de=P.getUploadIdList.call(R,le))&&L.each(de,function(oe){P.removeUploadId.call(R,oe)}),ie.emit("no_available_upload_id")}}})}),ie.emit("get_remote_upload_id_list")}function S(s,o){var t=this,r=[],c={Bucket:s.Bucket,Region:s.Region,Prefix:s.Key,calledBySdk:s.calledBySdk||"sliceUploadFile",tracker:s.tracker},p=function(){t.multipartList(c,function(R,K){if(R)return o(R);r.push.apply(r,K.Upload||[]),K.IsTruncated==="true"?(c.KeyMarker=K.NextKeyMarker,c.UploadIdMarker=K.NextUploadIdMarker,p()):o(null,{UploadList:r})})};p()}function f(s,o){var t=this,r=[],c={Bucket:s.Bucket,Region:s.Region,Key:s.Key,UploadId:s.UploadId,calledBySdk:"sliceUploadFile",tracker:s.tracker},p=function(){t.multipartListPart(c,function(R,K){if(R)return o(R);r.push.apply(r,K.Part||[]),K.IsTruncated==="true"?(c.PartNumberMarker=K.NextPartNumberMarker,p()):o(null,{PartList:r})})};p()}function y(s,o){var t=this,r=s.TaskId,c=s.Bucket,p=s.Region,I=s.Key,R=s.UploadData,K=s.FileSize,F=s.SliceSize,J=Math.min(s.AsyncLimit||t.options.ChunkParallelLimit||1,256),Q=s.Body,ee=Math.ceil(K/F),te=0,Ce=s.ServerSideEncryption,ae=s.Headers,ie=L.filter(R.PartList,function(D){return D.Uploaded&&(te+=D.PartNumber>=ee&&K%F||F),!D.Uploaded}),Z=s.onProgress;t.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 开始并发上传各个分块")}),j.eachLimit(ie,J,function(D,Y){if(t._isRunningTask(r)){var le=D.PartNumber,de=Math.min(K,D.PartNumber*F)-(D.PartNumber-1)*F,oe=0;t.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 分块").concat(le,"开始上传")}),x.call(t,{TaskId:r,Bucket:c,Region:p,Key:I,SliceSize:F,FileSize:K,PartNumber:le,ServerSideEncryption:Ce,Body:Q,UploadData:R,Headers:ae,onProgress:function(he){te+=he.loaded-oe,oe=he.loaded,Z({loaded:te,total:K})},tracker:s.tracker},function(ue,he){t._isRunningTask(r)&&(!ue&&!he.ETag&&(ue='get ETag error, please add "ETag" to CORS ExposeHeader setting.( 获取ETag失败，请在CORS ExposeHeader设置中添加ETag，请参考文档：https://cloud.tencent.com/document/product/436/13318 )',t.logger.error({cate:"PROCESS",tag:"upload",msg:"[key=".concat(s.Key,"] 分块").concat(le,"上传请求成功，但是未获取到 eTag")})),ue?(te-=oe,t.logger.info({cate:"RESULT",tag:"upload",msg:"[key=".concat(s.Key,"] 分块").concat(le,"上传失败")})):(te+=de-oe,D.ETag=he.ETag),t.logger.info({cate:"RESULT",tag:"upload",msg:"[key=".concat(s.Key,"] 分块").concat(le,"上传成功")}),Z({loaded:te,total:K}),Y(ue||null,he))})}},function(D){if(t._isRunningTask(r)){if(D)return o(D);o(null,{UploadId:R.UploadId,SliceList:R.PartList})}})}function x(s,o){var t=this,r=s.TaskId,c=s.Bucket,p=s.Region,I=s.Key,R=s.FileSize,K=s.Body,F=s.PartNumber*1,J=s.SliceSize,Q=s.ServerSideEncryption,ee=s.UploadData,te=s.Headers||{},Ce=t.options.ChunkRetryTimes+1,ae=J*(F-1),ie=J,Z=ae+J;Z>R&&(Z=R,ie=Z-ae);var D=["x-cos-traffic-limit","x-cos-mime-limit"],Y={};L.each(te,function(de,oe){D.indexOf(oe)>-1&&(Y[oe]=de)});var le=ee.PartList[F-1];j.retry(Ce,function(de){t._isRunningTask(r)&&L.fileSlice(K,ae,Z,!0,function(oe){t.multipartUpload({TaskId:r,Bucket:c,Region:p,Key:I,ContentLength:ie,PartNumber:F,UploadId:ee.UploadId,ServerSideEncryption:Q,Body:oe,Headers:Y,onProgress:s.onProgress,calledBySdk:"sliceUploadFile",tracker:s.tracker},function(ue,he){if(t._isRunningTask(r))return ue?de(ue):(le.Uploaded=!0,de(null,he))})})},function(de,oe){if(t._isRunningTask(r))return o(de,oe)})}function b(s,o){var t=s.Bucket,r=s.Region,c=s.Key,p=s.UploadId,I=s.SliceList,R=this,K=this.options.ChunkRetryTimes+1,F=s.Headers,J=I.map(function(Q){return{PartNumber:Q.PartNumber,ETag:Q.ETag}});j.retry(K,function(Q){R.multipartComplete({Bucket:t,Region:r,Key:c,UploadId:p,Parts:J,Headers:F,calledBySdk:"sliceUploadFile",tracker:s.tracker},Q)},function(Q,ee){o(Q,ee)})}function w(s,o){var t=s.Bucket,r=s.Region,c=s.Key,p=s.UploadId,I=s.Level||"task",R=s.AsyncLimit,K=this,F=new M;if(F.on("error",function(J){return o(J)}),F.on("get_abort_array",function(J){U.call(K,{Bucket:t,Region:r,Key:c,Headers:s.Headers,AsyncLimit:R,AbortArray:J},o)}),I==="bucket")S.call(K,{Bucket:t,Region:r,calledBySdk:"abortUploadTask"},function(J,Q){if(J)return o(J);F.emit("get_abort_array",Q.UploadList||[])});else if(I==="file"){if(!c)return o(L.error(new Error("abort_upload_task_no_key")));S.call(K,{Bucket:t,Region:r,Key:c,calledBySdk:"abortUploadTask"},function(J,Q){if(J)return o(J);F.emit("get_abort_array",Q.UploadList||[])})}else if(I==="task"){if(!p)return o(L.error(new Error("abort_upload_task_no_id")));if(!c)return o(L.error(new Error("abort_upload_task_no_key")));F.emit("get_abort_array",[{Key:c,UploadId:p}])}else return o(L.error(new Error("abort_unknown_level")))}function U(s,o){var t=s.Bucket,r=s.Region,c=s.Key,p=s.AbortArray,I=s.AsyncLimit||1,R=this,K=0,F=new Array(p.length);j.eachLimit(p,I,function(J,Q){var ee=K;if(c&&c!==J.Key){F[ee]={error:{KeyNotMatch:!0}},Q(null);return}var te=J.UploadId||J.UploadID;R.multipartAbort({Bucket:t,Region:r,Key:J.Key,Headers:s.Headers,UploadId:te},function(Ce){var ae={Bucket:t,Region:r,Key:J.Key,UploadId:te};F[ee]={error:Ce,task:ae},Q(null)}),K++},function(J){if(J)return o(J);for(var Q=[],ee=[],te=0,Ce=F.length;te<Ce;te++){var ae=F[te];ae.task&&(ae.error?ee.push(ae.task):Q.push(ae.task))}return o(null,{successList:Q,errorList:ee})})}function z(s,o){var t=this,r=s.SliceSize===void 0?t.options.SliceSize:s.SliceSize,c=[],p=s.Body,I=p.size||p.length||0,R={TaskId:""};if(t.options.EnableReporter){var K=t.options.UseAccelerate||typeof t.options.Domain=="string"&&t.options.Domain.includes("accelerate."),F=I>r?"sliceUploadFile":"putObject";s.tracker=new i({Beacon:t.options.BeaconReporter,clsReporter:t.options.ClsReporter,bucket:s.Bucket,region:s.Region,apiName:"uploadFile",realApi:F,fileKey:s.Key,fileSize:I,accelerate:K,deepTracker:t.options.DeepTracker,customId:t.options.CustomId,delay:t.options.TrackerDelay})}L.each(s,function(ae,ie){N(ae)!=="object"&&typeof ae!="function"&&(R[ie]=ae)});var J=s.onTaskReady,Q=function(ie){R.TaskId=ie,J&&J(ie)};s.onTaskReady=Q;var ee=I>r?"sliceUploadFile":"putObject",te=s.onFileFinish,Ce=function(ie,Z){s.tracker&&s.tracker.report(ie,Z),te&&te(ie,Z,R),o&&o(ie,Z)};c.push({api:ee,params:s,callback:Ce}),t._addTasks(c)}function q(s,o){var t=this,r=s.SliceSize===void 0?t.options.SliceSize:s.SliceSize,c=0,p=0,I=L.throttleOnProgress.call(t,p,s.onProgress),R=s.files.length,K=s.onFileFinish,F=Array(R),J=function(te,Ce,ae){I(null,!0),K&&K(te,Ce,ae),F[ae.Index]={options:ae,error:te,data:Ce},--R<=0&&o&&o(null,{files:F})},Q=[];L.each(s.files,function(ee,te){(function(){var Ce=ee.Body,ae=Ce.size||Ce.length||0,ie={Index:te,TaskId:""};if(!t.options.UseRawKey&&ee.Key&&ee.Key.substr(0,1)==="/"&&(ee.Key=ee.Key.substr(1)),c+=ae,t.options.EnableReporter){var Z=t.options.UseAccelerate||typeof t.options.Domain=="string"&&t.options.Domain.includes("accelerate."),D=ae>r?"sliceUploadFile":"putObject";ee.tracker=new i({Beacon:t.options.BeaconReporter,clsReporter:t.options.ClsReporter,bucket:ee.Bucket,region:ee.Region,apiName:"uploadFiles",realApi:D,fileKey:ee.Key,fileSize:ae,accelerate:Z,deepTracker:t.options.DeepTracker,customId:t.options.CustomId,delay:t.options.TrackerDelay})}L.each(ee,function(Be,ke){N(Be)!=="object"&&typeof Be!="function"&&(ie[ke]=Be)});var Y=ee.onTaskReady,le=function(ke){ie.TaskId=ke,Y&&Y(ke)};ee.onTaskReady=le;var de=0,oe=ee.onProgress,ue=function(ke){p=p-de+ke.loaded,de=ke.loaded,oe&&oe(ke),I({loaded:p,total:c})};ee.onProgress=ue;var he=ae>r?"sliceUploadFile":"putObject",ge=ee.onFileFinish,pe=function(ke,Oe){ee.tracker&&ee.tracker.report(ke,Oe),ge&&ge(ke,Oe),J&&J(ke,Oe,ie)};Q.push({api:he,params:ee,callback:pe})})()}),t._addTasks(Q)}function h(s,o){var t=new M,r=this,c=s.Bucket,p=s.Region,I=s.Key,R=s.CopySource,K=L.getSourceParams.call(this,R);if(!K){o(L.error(new Error("CopySource format error")));return}var F=K.Bucket,J=K.Region,Q=decodeURIComponent(K.Key),ee=s.CopySliceSize===void 0?r.options.CopySliceSize:s.CopySliceSize;ee=Math.max(0,ee);var te=s.CopyChunkSize||this.options.CopyChunkSize,Ce=this.options.CopyChunkParallelLimit,ae=this.options.ChunkRetryTimes+1,ie=0,Z=0,D,Y,le={},de={},oe={};t.on("copy_slice_complete",function(ue){L.each(s.Headers,function(ge,pe){pe.toLowerCase().indexOf("x-cos-meta-")});var he=L.map(ue.PartList,function(ge){return{PartNumber:ge.PartNumber,ETag:ge.ETag}});j.retry(ae,function(ge){r.multipartComplete({Bucket:c,Region:p,Key:I,UploadId:ue.UploadId,Parts:he,tracker:s.tracker,calledBySdk:"sliceCopyFile"},ge)},function(ge,pe){if(P.removeUsing(ue.UploadId),ge)return Y(null,!0),o(ge);P.removeUploadId(ue.UploadId),Y({loaded:D,total:D},!0),o(null,pe)})}),t.on("get_copy_data_finish",function(ue){var he=P.getCopyFileId(R,le,te,c,I);he&&P.saveUploadId(he,ue.UploadId,r.options.UploadIdCacheLimit),P.setUsing(ue.UploadId);var ge=L.filter(ue.PartList,function(pe){return pe.Uploaded&&(Z+=pe.PartNumber>=ie&&D%te||te),!pe.Uploaded});j.eachLimit(ge,Ce,function(pe,Be){var ke=pe.PartNumber,Oe=pe.CopySourceRange,Ge=pe.end-pe.start;j.retry(ae,function(Fe){l.call(r,{Bucket:c,Region:p,Key:I,CopySource:R,UploadId:ue.UploadId,PartNumber:ke,CopySourceRange:Oe,tracker:s.tracker,calledBySdk:"sliceCopyFile"},Fe)},function(Fe,We){if(Fe)return Be(Fe);Z+=Ge,Y({loaded:Z,total:D}),pe.ETag=We.ETag,Be(Fe||null,We)})},function(pe){if(pe)return P.removeUsing(ue.UploadId),Y(null,!0),o(pe);t.emit("copy_slice_complete",ue)})}),t.on("get_chunk_size_finish",function(){var ue=function(){r.multipartInit({Bucket:c,Region:p,Key:I,Headers:oe,tracker:s.tracker,calledBySdk:"sliceCopyFile"},function(ke,Oe){if(ke)return o(ke);s.UploadId=Oe.UploadId,t.emit("get_copy_data_finish",{UploadId:s.UploadId,PartList:s.PartList})})},he=P.getCopyFileId(R,le,te,c,I),ge=P.getUploadIdList(he);if(!he||!ge)return ue();var pe=function(ke){if(ke>=ge.length)return ue();var Oe=ge[ke];if(P.using[Oe])return pe(ke+1);f.call(r,{Bucket:c,Region:p,Key:I,UploadId:Oe,tracker:s.tracker,calledBySdk:"sliceCopyFile"},function(Ge,Fe){if(Ge)P.removeUploadId(Oe),pe(ke+1);else{if(P.using[Oe])return pe(ke+1);var We={},Ze=0;L.each(Fe.PartList,function(_e){var $e=parseInt(_e.Size),rt=Ze+$e-1;We[_e.PartNumber+"|"+Ze+"|"+rt]=_e.ETag,Ze+=$e}),L.each(s.PartList,function(_e){var $e=We[_e.PartNumber+"|"+_e.start+"|"+_e.end];$e&&(_e.ETag=$e,_e.Uploaded=!0)}),t.emit("get_copy_data_finish",{UploadId:Oe,PartList:s.PartList})}})};pe(0)}),t.on("get_file_size_finish",function(){if(function(){for(var he=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],ge=1024*1024,pe=0;pe<he.length&&(ge=he[pe]*1024*1024,!(D/ge<=r.options.MaxPartNumber));pe++);s.ChunkSize=te=Math.max(te,ge),ie=Math.ceil(D/te);for(var Be=[],ke=1;ke<=ie;ke++){var Oe=(ke-1)*te,Ge=ke*te<D?ke*te-1:D-1,Fe={PartNumber:ke,start:Oe,end:Ge,CopySourceRange:"bytes="+Oe+"-"+Ge};Be.push(Fe)}s.PartList=Be}(),s.Headers["x-cos-metadata-directive"]==="Replaced"?oe=s.Headers:oe=de,oe["x-cos-storage-class"]=s.Headers["x-cos-storage-class"]||de["x-cos-storage-class"],oe=L.clearKey(oe),de["x-cos-storage-class"]==="ARCHIVE"||de["x-cos-storage-class"]==="DEEP_ARCHIVE"){var ue=de["x-cos-restore"];if(!ue||ue==='ongoing-request="true"'){o(L.error(new Error("Unrestored archive object is not allowed to be copied")));return}}delete oe["x-cos-copy-source"],delete oe["x-cos-metadata-directive"],delete oe["x-cos-copy-source-If-Modified-Since"],delete oe["x-cos-copy-source-If-Unmodified-Since"],delete oe["x-cos-copy-source-If-Match"],delete oe["x-cos-copy-source-If-None-Match"],t.emit("get_chunk_size_finish")}),r.headObject({Bucket:F,Region:J,Key:Q,tracker:s.tracker,calledBySdk:"sliceCopyFile"},function(ue,he){if(ue){ue.statusCode&&ue.statusCode===404?o(L.error(ue,{ErrorStatus:Q+" Not Exist"})):o(ue);return}if(D=s.FileSize=he.headers["content-length"],D===void 0||!D){o(L.error(new Error('get Content-Length error, please add "Content-Length" to CORS ExposeHeader setting.（ 获取Content-Length失败，请在CORS ExposeHeader设置中添加Content-Length，请参考文档：https://cloud.tencent.com/document/product/436/13318 ）')));return}if(s.tracker&&s.tracker.setParams({httpSize:D}),Y=L.throttleOnProgress.call(r,D,s.onProgress),D<=ee)s.Headers["x-cos-metadata-directive"]||(s.Headers["x-cos-metadata-directive"]="Copy"),r.putObjectCopy(Object.assign(s,{calledBySdk:"sliceCopyFile"}),function(pe,Be){if(pe)return Y(null,!0),o(pe);Y({loaded:D,total:D},!0),o(pe,Be)});else{var ge=he.headers;le=ge,de={"Cache-Control":ge["cache-control"],"Content-Disposition":ge["content-disposition"],"Content-Encoding":ge["content-encoding"],"Content-Type":ge["content-type"],Expires:ge.expires,"x-cos-storage-class":ge["x-cos-storage-class"]},L.each(ge,function(pe,Be){var ke="x-cos-meta-";Be.indexOf(ke)===0&&Be.length>ke.length&&(de[Be]=pe)}),t.emit("get_file_size_finish")}})}function l(s,o){var t=s.TaskId,r=s.Bucket,c=s.Region,p=s.Key,I=s.CopySource,R=s.UploadId,K=s.PartNumber*1,F=s.CopySourceRange,J=this.options.ChunkRetryTimes+1,Q=this;j.retry(J,function(ee){Q.uploadPartCopy({TaskId:t,Bucket:r,Region:c,Key:p,CopySource:I,UploadId:R,PartNumber:K,CopySourceRange:F,tracker:s.tracker,calledBySdk:s.calledBySdk},function(te,Ce){ee(te||null,Ce)})},function(ee,te){return o(ee,te)})}var T={sliceUploadFile:C,abortUploadTask:w,uploadFile:z,uploadFiles:q,sliceCopyFile:h};H.exports.init=function(s,o){o.transferToTaskMethod(T,"sliceUploadFile"),L.each(T,function(t,r){s.prototype[r]=L.apiWrapper(r,t)})}},"./src/async.js":function(H,ne){var E=function(M,L,i,C){if(C=C||function(){},!M.length||L<=0)return C();var d=0,S=0,f=0;(function y(){if(d>=M.length)return C();for(;f<L&&S<M.length;)S+=1,f+=1,i(M[S-1],function(x){x?(C(x),C=function(){}):(d+=1,f-=1,d>=M.length?C():y())})})()},N=function(M,L,i){var C=function(S){L(function(f,y){f&&S<M?C(S+1):i(f,y)})};M<1?i():C(1)},P={eachLimit:E,retry:N};H.exports=P},"./src/base.js":function(H,ne,E){var N=E("./node_modules/@babel/runtime/helpers/defineProperty.js"),P=E("./node_modules/@babel/runtime/helpers/typeof.js");function j(e,a){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(e);a&&(u=u.filter(function(v){return Object.getOwnPropertyDescriptor(e,v).enumerable})),n.push.apply(n,u)}return n}function M(e){for(var a=1;a<arguments.length;a++){var n=arguments[a]!=null?arguments[a]:{};a%2?j(Object(n),!0).forEach(function(u){N(e,u,n[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach(function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(n,u))})}return e}var L=E("./lib/request.js"),i=E("./src/util.js");function C(e,a){var n=this.options.Protocol||(i.isBrowser&&(typeof location>"u"?"undefined":P(location))==="object"&&location.protocol==="http:"?"http:":"https:"),u=this.options.ServiceDomain,v=e.AppId||this.options.appId,g=e.Region;u?(u=u.replace(/\{\{AppId\}\}/gi,v||"").replace(/\{\{Region\}\}/gi,g||"").replace(/\{\{.*?\}\}/gi,""),/^[a-zA-Z]+:\/\//.test(u)||(u=n+"//"+u),u.slice(-1)==="/"&&(u=u.slice(0,-1))):g?u=n+"//cos."+g+".myqcloud.com":u=n+"//service.cos.myqcloud.com";var k="",B=g?"cos."+g+".myqcloud.com":"service.cos.myqcloud.com",_=u.replace(/^https?:\/\/([^/]+)(\/.*)?$/,"$1");B===_&&(k=B),X.call(this,{Action:"name/cos:GetService",url:u,method:"GET",headers:e.Headers,SignHost:k,tracker:e.tracker},function(ce,se){if(ce)return a(ce);var Se=se&&se.ListAllMyBucketsResult&&se.ListAllMyBucketsResult.Buckets&&se.ListAllMyBucketsResult.Buckets.Bucket||[];Se=i.isArray(Se)?Se:[Se];var W=se&&se.ListAllMyBucketsResult&&se.ListAllMyBucketsResult.Owner||{};a(null,{Buckets:Se,Owner:W,statusCode:se.statusCode,headers:se.headers})})}function d(e,a){var n=this,u="";if(e.BucketAZConfig){var v={BucketAZConfig:e.BucketAZConfig};u=i.json2xml({CreateBucketConfiguration:v})}X.call(this,{Action:"name/cos:PutBucket",method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,body:u,tracker:e.tracker},function(g,k){if(g)return a(g);var B=ze({protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:e.Region,isLocation:!0});a(null,{Location:B,statusCode:k.statusCode,headers:k.headers})})}function S(e,a){X.call(this,{Action:"name/cos:HeadBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"HEAD",tracker:e.tracker},a)}function f(e,a){var n={};n.prefix=e.Prefix||"",n.delimiter=e.Delimiter,n.marker=e.Marker,n["max-keys"]=e.MaxKeys,n["encoding-type"]=e.EncodingType,X.call(this,{Action:"name/cos:GetBucket",ResourceKey:n.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n,tracker:e.tracker},function(u,v){if(u)return a(u);var g=v.ListBucketResult||{},k=g.Contents||[],B=g.CommonPrefixes||[];k=i.isArray(k)?k:[k],B=i.isArray(B)?B:[B];var _=i.clone(g);i.extend(_,{Contents:k,CommonPrefixes:B,statusCode:v.statusCode,headers:v.headers}),a(null,_)})}function y(e,a){X.call(this,{Action:"name/cos:DeleteBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"DELETE",tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode,headers:u.headers})})}function x(e,a){var n=e.Headers,u="";if(e.AccessControlPolicy){var v=i.clone(e.AccessControlPolicy||{}),g=v.Grants||v.Grant;g=i.isArray(g)?g:[g],delete v.Grant,delete v.Grants,v.AccessControlList={Grant:g},u=i.json2xml({AccessControlPolicy:v}),n["Content-Type"]="application/xml",n["Content-MD5"]=i.b64(i.md5(u))}i.each(n,function(k,B){B.indexOf("x-cos-grant-")===0&&(n[B]=De(n[B]))}),X.call(this,{Action:"name/cos:PutBucketACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:n,action:"acl",body:u,tracker:e.tracker},function(k,B){if(k)return a(k);a(null,{statusCode:B.statusCode,headers:B.headers})})}function b(e,a){X.call(this,{Action:"name/cos:GetBucketACL",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"acl",tracker:e.tracker},function(n,u){if(n)return a(n);var v=u.AccessControlPolicy||{},g=v.Owner||{},k=v.AccessControlList.Grant||[];k=i.isArray(k)?k:[k];var B=xe(v);u.headers&&u.headers["x-cos-acl"]&&(B.ACL=u.headers["x-cos-acl"]),B=i.extend(B,{Owner:g,Grants:k,statusCode:u.statusCode,headers:u.headers}),a(null,B)})}function w(e,a){var n=e.CORSConfiguration||{},u=n.CORSRules||e.CORSRules||[];u=i.clone(i.isArray(u)?u:[u]),i.each(u,function(B){i.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],function(_){var ce=_+"s",se=B[ce]||B[_]||[];delete B[ce],B[_]=i.isArray(se)?se:[se]})});var v={CORSRule:u};e.ResponseVary&&(v.ResponseVary=e.ResponseVary);var g=i.json2xml({CORSConfiguration:v}),k=e.Headers;k["Content-Type"]="application/xml",k["Content-MD5"]=i.b64(i.md5(g)),X.call(this,{Action:"name/cos:PutBucketCORS",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:g,action:"cors",headers:k,tracker:e.tracker},function(B,_){if(B)return a(B);a(null,{statusCode:_.statusCode,headers:_.headers})})}function U(e,a){X.call(this,{Action:"name/cos:GetBucketCORS",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors",tracker:e.tracker},function(n,u){if(n){if(n.statusCode===404&&n.error&&n.error.Code==="NoSuchCORSConfiguration"){var v={CORSRules:[],statusCode:n.statusCode};n.headers&&(v.headers=n.headers),a(null,v)}else a(n);return}var g=u.CORSConfiguration||{},k=g.CORSRules||g.CORSRule||[];k=i.clone(i.isArray(k)?k:[k]);var B=g.ResponseVary;i.each(k,function(_){i.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],function(ce){var se=ce+"s",Se=_[se]||_[ce]||[];delete _[ce],_[se]=i.isArray(Se)?Se:[Se]})}),a(null,{CORSRules:k,ResponseVary:B,statusCode:u.statusCode,headers:u.headers})})}function z(e,a){X.call(this,{Action:"name/cos:DeleteBucketCORS",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors",tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode||n.statusCode,headers:u.headers})})}function q(e,a){X.call(this,{Action:"name/cos:GetBucketLocation",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"location",tracker:e.tracker},a)}function h(e,a){var n=e.Policy;try{typeof n=="string"&&(n=JSON.parse(n))}catch{}if(!n||typeof n=="string")return a(i.error(new Error("Policy format error")));var u=JSON.stringify(n);n.version||(n.version="2.0");var v=e.Headers;v["Content-Type"]="application/json",v["Content-MD5"]=i.b64(i.md5(u)),X.call(this,{Action:"name/cos:PutBucketPolicy",method:"PUT",Bucket:e.Bucket,Region:e.Region,action:"policy",body:u,headers:v,tracker:e.tracker},function(g,k){if(g&&g.statusCode===204)return a(null,{statusCode:g.statusCode});if(g)return a(g);a(null,{statusCode:k.statusCode,headers:k.headers})})}function l(e,a){X.call(this,{Action:"name/cos:GetBucketPolicy",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy",rawBody:!0,tracker:e.tracker},function(n,u){if(n)return n.statusCode&&n.statusCode===403?a(i.error(n,{ErrorStatus:"Access Denied"})):n.statusCode&&n.statusCode===405?a(i.error(n,{ErrorStatus:"Method Not Allowed"})):n.statusCode&&n.statusCode===404?a(i.error(n,{ErrorStatus:"Policy Not Found"})):a(n);var v={};try{v=JSON.parse(u.body)}catch{}a(null,{Policy:v,statusCode:u.statusCode,headers:u.headers})})}function T(e,a){X.call(this,{Action:"name/cos:DeleteBucketPolicy",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy",tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode||n.statusCode,headers:u.headers})})}function s(e,a){var n=e.Tagging||{},u=n.TagSet||n.Tags||e.Tags||[];u=i.clone(i.isArray(u)?u:[u]);var v=i.json2xml({Tagging:{TagSet:{Tag:u}}}),g=e.Headers;g["Content-Type"]="application/xml",g["Content-MD5"]=i.b64(i.md5(v)),X.call(this,{Action:"name/cos:PutBucketTagging",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:v,action:"tagging",headers:g,tracker:e.tracker},function(k,B){if(k&&k.statusCode===204)return a(null,{statusCode:k.statusCode});if(k)return a(k);a(null,{statusCode:B.statusCode,headers:B.headers})})}function o(e,a){X.call(this,{Action:"name/cos:GetBucketTagging",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging",tracker:e.tracker},function(n,u){if(n){if(n.statusCode===404&&n.error&&(n.error==="Not Found"||n.error.Code==="NoSuchTagSet")){var v={Tags:[],statusCode:n.statusCode};n.headers&&(v.headers=n.headers),a(null,v)}else a(n);return}var g=[];try{g=u.Tagging.TagSet.Tag||[]}catch{}g=i.clone(i.isArray(g)?g:[g]),a(null,{Tags:g,statusCode:u.statusCode,headers:u.headers})})}function t(e,a){X.call(this,{Action:"name/cos:DeleteBucketTagging",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging",tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode,headers:u.headers})})}function r(e,a){var n=e.LifecycleConfiguration||{},u=n.Rules||e.Rules||[];u=i.clone(u);var v=i.json2xml({LifecycleConfiguration:{Rule:u}}),g=e.Headers;g["Content-Type"]="application/xml",g["Content-MD5"]=i.b64(i.md5(v)),X.call(this,{Action:"name/cos:PutBucketLifecycle",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:v,action:"lifecycle",headers:g,tracker:e.tracker},function(k,B){if(k&&k.statusCode===204)return a(null,{statusCode:k.statusCode});if(k)return a(k);a(null,{statusCode:B.statusCode,headers:B.headers})})}function c(e,a){X.call(this,{Action:"name/cos:GetBucketLifecycle",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle",tracker:e.tracker},function(n,u){if(n){if(n.statusCode===404&&n.error&&n.error.Code==="NoSuchLifecycleConfiguration"){var v={Rules:[],statusCode:n.statusCode};n.headers&&(v.headers=n.headers),a(null,v)}else a(n);return}var g=[];try{g=u.LifecycleConfiguration.Rule||[]}catch{}g=i.clone(i.isArray(g)?g:[g]),a(null,{Rules:g,statusCode:u.statusCode,headers:u.headers})})}function p(e,a){X.call(this,{Action:"name/cos:DeleteBucketLifecycle",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle",tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode,headers:u.headers})})}function I(e,a){if(!e.VersioningConfiguration){a(i.error(new Error("missing param VersioningConfiguration")));return}var n=e.VersioningConfiguration||{},u=i.json2xml({VersioningConfiguration:n}),v=e.Headers;v["Content-Type"]="application/xml",v["Content-MD5"]=i.b64(i.md5(u)),X.call(this,{Action:"name/cos:PutBucketVersioning",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:u,action:"versioning",headers:v,tracker:e.tracker},function(g,k){if(g&&g.statusCode===204)return a(null,{statusCode:g.statusCode});if(g)return a(g);a(null,{statusCode:k.statusCode,headers:k.headers})})}function R(e,a){X.call(this,{Action:"name/cos:GetBucketVersioning",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"versioning",tracker:e.tracker},function(n,u){n||!u.VersioningConfiguration&&(u.VersioningConfiguration={}),a(n,u)})}function K(e,a){var n=i.clone(e.ReplicationConfiguration),u=i.json2xml({ReplicationConfiguration:n});u=u.replace(/<(\/?)Rules>/gi,"<$1Rule>"),u=u.replace(/<(\/?)Tags>/gi,"<$1Tag>");var v=e.Headers;v["Content-Type"]="application/xml",v["Content-MD5"]=i.b64(i.md5(u)),X.call(this,{Action:"name/cos:PutBucketReplication",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:u,action:"replication",headers:v,tracker:e.tracker},function(g,k){if(g&&g.statusCode===204)return a(null,{statusCode:g.statusCode});if(g)return a(g);a(null,{statusCode:k.statusCode,headers:k.headers})})}function F(e,a){X.call(this,{Action:"name/cos:GetBucketReplication",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication",tracker:e.tracker},function(n,u){if(n){if(n.statusCode===404&&n.error&&(n.error==="Not Found"||n.error.Code==="ReplicationConfigurationnotFoundError")){var v={ReplicationConfiguration:{Rules:[]},statusCode:n.statusCode};n.headers&&(v.headers=n.headers),a(null,v)}else a(n);return}!u.ReplicationConfiguration&&(u.ReplicationConfiguration={}),u.ReplicationConfiguration.Rule&&(u.ReplicationConfiguration.Rules=i.makeArray(u.ReplicationConfiguration.Rule),delete u.ReplicationConfiguration.Rule),a(n,u)})}function J(e,a){X.call(this,{Action:"name/cos:DeleteBucketReplication",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication",tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode,headers:u.headers})})}function Q(e,a){if(!e.WebsiteConfiguration){a(i.error(new Error("missing param WebsiteConfiguration")));return}var n=i.clone(e.WebsiteConfiguration||{}),u=n.RoutingRules||n.RoutingRule||[];u=i.isArray(u)?u:[u],delete n.RoutingRule,delete n.RoutingRules,u.length&&(n.RoutingRules={RoutingRule:u});var v=i.json2xml({WebsiteConfiguration:n}),g=e.Headers;g["Content-Type"]="application/xml",g["Content-MD5"]=i.b64(i.md5(v)),X.call(this,{Action:"name/cos:PutBucketWebsite",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:v,action:"website",headers:g,tracker:e.tracker},function(k,B){if(k&&k.statusCode===204)return a(null,{statusCode:k.statusCode});if(k)return a(k);a(null,{statusCode:B.statusCode,headers:B.headers})})}function ee(e,a){X.call(this,{Action:"name/cos:GetBucketWebsite",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"website",tracker:e.tracker},function(n,u){if(n){if(n.statusCode===404&&n.error.Code==="NoSuchWebsiteConfiguration"){var v={WebsiteConfiguration:{},statusCode:n.statusCode};n.headers&&(v.headers=n.headers),a(null,v)}else a(n);return}var g=u.WebsiteConfiguration||{};if(g.RoutingRules){var k=i.clone(g.RoutingRules.RoutingRule||[]);k=i.makeArray(k),g.RoutingRules=k}a(null,{WebsiteConfiguration:g,statusCode:u.statusCode,headers:u.headers})})}function te(e,a){X.call(this,{Action:"name/cos:DeleteBucketWebsite",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"website",tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode,headers:u.headers})})}function Ce(e,a){if(!e.RefererConfiguration){a(i.error(new Error("missing param RefererConfiguration")));return}var n=i.clone(e.RefererConfiguration||{}),u=n.DomainList||{},v=u.Domains||u.Domain||[];v=i.isArray(v)?v:[v],v.length&&(n.DomainList={Domain:v});var g=i.json2xml({RefererConfiguration:n}),k=e.Headers;k["Content-Type"]="application/xml",k["Content-MD5"]=i.b64(i.md5(g)),X.call(this,{Action:"name/cos:PutBucketReferer",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:g,action:"referer",headers:k,tracker:e.tracker},function(B,_){if(B&&B.statusCode===204)return a(null,{statusCode:B.statusCode});if(B)return a(B);a(null,{statusCode:_.statusCode,headers:_.headers})})}function ae(e,a){X.call(this,{Action:"name/cos:GetBucketReferer",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"referer",tracker:e.tracker},function(n,u){if(n){if(n.statusCode===404&&n.error.Code==="NoSuchRefererConfiguration"){var v={WebsiteConfiguration:{},statusCode:n.statusCode};n.headers&&(v.headers=n.headers),a(null,v)}else a(n);return}var g=u.RefererConfiguration||{};if(g.DomainList){var k=i.makeArray(g.DomainList.Domain||[]);g.DomainList={Domains:k}}a(null,{RefererConfiguration:g,statusCode:u.statusCode,headers:u.headers})})}function ie(e,a){var n=e.DomainConfiguration||{},u=n.DomainRule||e.DomainRule||[];u=i.clone(u);var v=i.json2xml({DomainConfiguration:{DomainRule:u}}),g=e.Headers;g["Content-Type"]="application/xml",g["Content-MD5"]=i.b64(i.md5(v)),X.call(this,{Action:"name/cos:PutBucketDomain",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:v,action:"domain",headers:g,tracker:e.tracker},function(k,B){if(k&&k.statusCode===204)return a(null,{statusCode:k.statusCode});if(k)return a(k);a(null,{statusCode:B.statusCode,headers:B.headers})})}function Z(e,a){X.call(this,{Action:"name/cos:GetBucketDomain",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"domain",tracker:e.tracker},function(n,u){if(n)return a(n);var v=[];try{v=u.DomainConfiguration.DomainRule||[]}catch{}v=i.clone(i.isArray(v)?v:[v]),a(null,{DomainRule:v,statusCode:u.statusCode,headers:u.headers})})}function D(e,a){X.call(this,{Action:"name/cos:DeleteBucketDomain",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"domain",tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode,headers:u.headers})})}function Y(e,a){var n=e.OriginConfiguration||{},u=n.OriginRule||e.OriginRule||[];u=i.clone(u);var v=i.json2xml({OriginConfiguration:{OriginRule:u}}),g=e.Headers;g["Content-Type"]="application/xml",g["Content-MD5"]=i.b64(i.md5(v)),X.call(this,{Action:"name/cos:PutBucketOrigin",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:v,action:"origin",headers:g,tracker:e.tracker},function(k,B){if(k&&k.statusCode===204)return a(null,{statusCode:k.statusCode});if(k)return a(k);a(null,{statusCode:B.statusCode,headers:B.headers})})}function le(e,a){X.call(this,{Action:"name/cos:GetBucketOrigin",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"origin",tracker:e.tracker},function(n,u){if(n)return a(n);var v=[];try{v=u.OriginConfiguration.OriginRule||[]}catch{}v=i.clone(i.isArray(v)?v:[v]),a(null,{OriginRule:v,statusCode:u.statusCode,headers:u.headers})})}function de(e,a){X.call(this,{Action:"name/cos:DeleteBucketOrigin",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"origin",tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode,headers:u.headers})})}function oe(e,a){var n=i.json2xml({BucketLoggingStatus:e.BucketLoggingStatus||""}),u=e.Headers;u["Content-Type"]="application/xml",u["Content-MD5"]=i.b64(i.md5(n)),X.call(this,{Action:"name/cos:PutBucketLogging",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"logging",headers:u,tracker:e.tracker},function(v,g){if(v&&v.statusCode===204)return a(null,{statusCode:v.statusCode});if(v)return a(v);a(null,{statusCode:g.statusCode,headers:g.headers})})}function ue(e,a){X.call(this,{Action:"name/cos:GetBucketLogging",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"logging",tracker:e.tracker},function(n,u){if(n)return a(n);a(null,{BucketLoggingStatus:u.BucketLoggingStatus,statusCode:u.statusCode,headers:u.headers})})}function he(e,a,n){var u=i.clone(a.InventoryConfiguration);if(u.OptionalFields){var v=u.OptionalFields||[];u.OptionalFields={Field:v}}if(u.Destination&&u.Destination.COSBucketDestination&&u.Destination.COSBucketDestination.Encryption){var g=u.Destination.COSBucketDestination.Encryption;Object.keys(g).indexOf("SSECOS")>-1&&(g["SSE-COS"]=g.SSECOS,delete g.SSECOS)}var k=i.json2xml({InventoryConfiguration:u}),B=a.Headers;B["Content-Type"]="application/xml",B["Content-MD5"]=i.b64(i.md5(k));var _=e==="PUT"?"name/cos:PutBucketInventory":"name/cos:PostBucketInventory";X.call(this,{Action:_,method:e,Bucket:a.Bucket,Region:a.Region,body:k,action:"inventory",qs:{id:a.Id},headers:B,tracker:a.tracker},function(ce,se){if(ce&&ce.statusCode===204)return n(null,{statusCode:ce.statusCode});if(ce)return n(ce);n(null,{statusCode:se.statusCode,headers:se.headers})})}function ge(e,a){return he.call(this,"PUT",e,a)}function pe(e,a){return he.call(this,"POST",e,a)}function Be(e,a){X.call(this,{Action:"name/cos:GetBucketInventory",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{id:e.Id},tracker:e.tracker},function(n,u){if(n)return a(n);var v=u.InventoryConfiguration;if(v&&v.OptionalFields&&v.OptionalFields.Field){var g=v.OptionalFields.Field;i.isArray(g)||(g=[g]),v.OptionalFields=g}if(v.Destination&&v.Destination.COSBucketDestination&&v.Destination.COSBucketDestination.Encryption){var k=v.Destination.COSBucketDestination.Encryption;Object.keys(k).indexOf("SSE-COS")>-1&&(k.SSECOS=k["SSE-COS"],delete k["SSE-COS"])}a(null,{InventoryConfiguration:v,statusCode:u.statusCode,headers:u.headers})})}function ke(e,a){X.call(this,{Action:"name/cos:ListBucketInventory",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{"continuation-token":e.ContinuationToken},tracker:e.tracker},function(n,u){if(n)return a(n);var v=u.ListInventoryConfigurationResult,g=v.InventoryConfiguration||[];g=i.isArray(g)?g:[g],delete v.InventoryConfiguration,i.each(g,function(k){if(k&&k.OptionalFields&&k.OptionalFields.Field){var B=k.OptionalFields.Field;i.isArray(B)||(B=[B]),k.OptionalFields=B}if(k.Destination&&k.Destination.COSBucketDestination&&k.Destination.COSBucketDestination.Encryption){var _=k.Destination.COSBucketDestination.Encryption;Object.keys(_).indexOf("SSE-COS")>-1&&(_.SSECOS=_["SSE-COS"],delete _["SSE-COS"])}}),v.InventoryConfigurations=g,i.extend(v,{statusCode:u.statusCode,headers:u.headers}),a(null,v)})}function Oe(e,a){X.call(this,{Action:"name/cos:DeleteBucketInventory",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{id:e.Id},tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode,headers:u.headers})})}function Ge(e,a){if(!e.AccelerateConfiguration){a(i.error(new Error("missing param AccelerateConfiguration")));return}var n={AccelerateConfiguration:e.AccelerateConfiguration||{}},u=i.json2xml(n),v={};v["Content-Type"]="application/xml",v["Content-MD5"]=i.b64(i.md5(u)),X.call(this,{Action:"name/cos:PutBucketAccelerate",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:u,action:"accelerate",headers:v,tracker:e.tracker},function(g,k){if(g)return a(g);a(null,{statusCode:k.statusCode,headers:k.headers})})}function Fe(e,a){X.call(this,{Action:"name/cos:GetBucketAccelerate",method:"GET",Bucket:e.Bucket,Region:e.Region,action:"accelerate",tracker:e.tracker},function(n,u){n||!u.AccelerateConfiguration&&(u.AccelerateConfiguration={}),a(n,u)})}function We(e,a){var n=e.ServerSideEncryptionConfiguration||{},u=n.Rule||n.Rules||[],v=i.json2xml({ServerSideEncryptionConfiguration:{Rule:u}}),g=e.Headers;g["Content-Type"]="application/xml",g["Content-MD5"]=i.b64(i.md5(v)),X.call(this,{Action:"name/cos:PutBucketEncryption",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:v,action:"encryption",headers:g,tracker:e.tracker},function(k,B){if(k&&k.statusCode===204)return a(null,{statusCode:k.statusCode});if(k)return a(k);a(null,{statusCode:B.statusCode,headers:B.headers})})}function Ze(e,a){X.call(this,{Action:"name/cos:GetBucketEncryption",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"encryption",tracker:e.tracker},function(n,u){if(n){if(n.statusCode===404&&n.code==="NoSuchEncryptionConfiguration"){var v={EncryptionConfiguration:{Rules:[]},statusCode:n.statusCode};n.headers&&(v.headers=n.headers),a(null,v)}else a(n);return}var g=i.makeArray(u.EncryptionConfiguration&&u.EncryptionConfiguration.Rule||[]);u.EncryptionConfiguration={Rules:g},a(n,u)})}function _e(e,a){X.call(this,{Action:"name/cos:DeleteBucketReplication",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"encryption",tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode,headers:u.headers})})}function $e(e,a){X.call(this,{Action:"name/cos:HeadObject",method:"HEAD",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers,tracker:e.tracker},function(n,u){if(n){var v=n.statusCode;return e.Headers["If-Modified-Since"]&&v&&v===304?a(null,{NotModified:!0,statusCode:v}):a(n)}u.ETag=i.attr(u.headers,"etag",""),a(null,u)})}function rt(e,a){var n={};n.prefix=e.Prefix||"",n.delimiter=e.Delimiter,n["key-marker"]=e.KeyMarker,n["version-id-marker"]=e.VersionIdMarker,n["max-keys"]=e.MaxKeys,n["encoding-type"]=e.EncodingType,X.call(this,{Action:"name/cos:GetBucketObjectVersions",ResourceKey:n.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n,action:"versions",tracker:e.tracker},function(u,v){if(u)return a(u);var g=v.ListVersionsResult||{},k=g.DeleteMarker||[];k=i.isArray(k)?k:[k];var B=g.Version||[];B=i.isArray(B)?B:[B];var _=i.clone(g);delete _.DeleteMarker,delete _.Version,i.extend(_,{DeleteMarkers:k,Versions:B,statusCode:v.statusCode,headers:v.headers}),a(null,_)})}function at(e,a){var n=this;if(n.logger.info({cate:"PROCESS",tag:"download",msg:"[key=".concat(e.Key,"] getObject开始")}),this.options.ObjectKeySimplifyCheck){var u=i.simplifyPath(e.Key);if(u==="/"){a(i.error(new Error("The Getobject Key is illegal")));return}}var v=e.Query||{},g=e.QueryString||"",k=i.throttleOnProgress.call(this,0,e.onProgress),B=e.tracker;B&&B.setParams({signStartTime:new Date().getTime()}),v["response-content-type"]=e.ResponseContentType,v["response-content-language"]=e.ResponseContentLanguage,v["response-expires"]=e.ResponseExpires,v["response-cache-control"]=e.ResponseCacheControl,v["response-content-disposition"]=e.ResponseContentDisposition,v["response-content-encoding"]=e.ResponseContentEncoding,X.call(this,{Action:"name/cos:GetObject",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,DataType:e.DataType,headers:e.Headers,qs:v,qsStr:g,rawBody:!0,onDownloadProgress:k,tracker:B},function(_,ce){if(k(null,!0),_){var se=_.statusCode;return e.Headers["If-Modified-Since"]&&se&&se===304?a(null,{NotModified:!0}):a(_)}a(null,{Body:ce.body,ETag:i.attr(ce.headers,"etag",""),statusCode:ce.statusCode,headers:ce.headers}),n.logger.info({cate:"PROCESS",tag:"download",msg:"[key=".concat(e.Key,"] getObject结束")})})}function lt(e,a){var n=this,u=e.ContentLength,v=i.throttleOnProgress.call(n,u,e.onProgress);n.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] putObject开始")});var g=e.Headers;!g["Cache-Control"]&&!g["cache-control"]&&(g["Cache-Control"]=""),!g["Content-Type"]&&!g["content-type"]&&(g["Content-Type"]=e.Body&&e.Body.type||"");var k=e.UploadAddMetaMd5||n.options.UploadAddMetaMd5||n.options.UploadCheckContentMd5,B=e.tracker;k&&B&&B.setParams({md5StartTime:new Date().getTime()}),k&&n.logger.debug({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 开始计算 md5")}),i.getBodyMd5(k,e.Body,function(_){_&&(n.logger.debug({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] md5: ").concat(_,"，md5Base64=").concat(i.b64(_))}),B&&B.setParams({md5EndTime:new Date().getTime()}),n.options.UploadCheckContentMd5&&(g["Content-MD5"]=i.b64(_)),(e.UploadAddMetaMd5||n.options.UploadAddMetaMd5)&&(g["x-cos-meta-md5"]=_)),e.ContentLength!==void 0&&(g["Content-Length"]=e.ContentLength),v(null,!0),X.call(n,{Action:"name/cos:PutObject",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:e.Query,body:e.Body,onProgress:v,tracker:B},function(ce,se){if(ce)return n.logger.error({cate:"ERROR",tag:"upload",msg:"上传失败，错误信息：".concat(JSON.stringify(ce))}),v(null,!0),a(ce);v({loaded:u,total:u},!0);var Se=ze({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:n.options.UseAccelerate?"accelerate":e.Region,object:e.Key});Se=Se.substr(Se.indexOf("://")+3),se.Location=Se,se.ETag=i.attr(se.headers,"etag",""),n.logger.info({cate:"RESULT",tag:"upload",msg:"上传成功，Location=".concat(Se)}),n.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] putObject结束")}),a(null,se)})},e.onHashProgress)}function dt(e,a){X.call(this,{Action:"name/cos:DeleteObject",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,VersionId:e.VersionId,action:e.Recursive?"recursive":"",tracker:e.tracker},function(n,u){if(n){var v=n.statusCode;return v&&v===404?a(null,{BucketNotFound:!0,statusCode:v}):a(n)}a(null,{statusCode:u.statusCode,headers:u.headers})})}function ft(e,a){var n={};e.VersionId&&(n.versionId=e.VersionId),X.call(this,{Action:"name/cos:GetObjectACL",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:n,action:"acl",tracker:e.tracker},function(u,v){if(u)return a(u);var g=v.AccessControlPolicy||{},k=g.Owner||{},B=g.AccessControlList&&g.AccessControlList.Grant||[];B=i.isArray(B)?B:[B];var _=xe(g);delete _.GrantWrite,v.headers&&v.headers["x-cos-acl"]&&(_.ACL=v.headers["x-cos-acl"]),_=i.extend(_,{Owner:k,Grants:B,statusCode:v.statusCode,headers:v.headers}),a(null,_)})}function ht(e,a){var n=e.Headers,u="";if(e.AccessControlPolicy){var v=i.clone(e.AccessControlPolicy||{}),g=v.Grants||v.Grant;g=i.isArray(g)?g:[g],delete v.Grant,delete v.Grants,v.AccessControlList={Grant:g},u=i.json2xml({AccessControlPolicy:v}),n["Content-Type"]="application/xml",n["Content-MD5"]=i.b64(i.md5(u))}i.each(n,function(k,B){B.indexOf("x-cos-grant-")===0&&(n[B]=De(n[B]))}),X.call(this,{Action:"name/cos:PutObjectACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"acl",headers:n,body:u,tracker:e.tracker},function(k,B){if(k)return a(k);a(null,{statusCode:B.statusCode,headers:B.headers})})}function gt(e,a){var n=e.Headers;n.Origin=e.Origin,n["Access-Control-Request-Method"]=e.AccessControlRequestMethod,n["Access-Control-Request-Headers"]=e.AccessControlRequestHeaders,X.call(this,{Action:"name/cos:OptionsObject",method:"OPTIONS",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:n,tracker:e.tracker},function(u,v){if(u)return u.statusCode&&u.statusCode===403?a(null,{OptionsForbidden:!0,statusCode:u.statusCode}):a(u);var g=v.headers||{};a(null,{AccessControlAllowOrigin:g["access-control-allow-origin"],AccessControlAllowMethods:g["access-control-allow-methods"],AccessControlAllowHeaders:g["access-control-allow-headers"],AccessControlExposeHeaders:g["access-control-expose-headers"],AccessControlMaxAge:g["access-control-max-age"],statusCode:v.statusCode,headers:v.headers})})}function vt(e,a){var n=this,u=e.Headers;!u["Cache-Control"]&&!u["cache-control"]&&(u["Cache-Control"]="");var v=e.CopySource||"",g=i.getSourceParams.call(this,v);if(!g){a(i.error(new Error("CopySource format error")));return}var k=g.Bucket,B=g.Region,_=decodeURIComponent(g.Key);X.call(this,{Scope:[{action:"name/cos:GetObject",bucket:k,region:B,prefix:_},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers,tracker:e.tracker},function(ce,se){if(ce)return a(ce);var Se=i.clone(se.CopyObjectResult||{}),W=ze({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key,isLocation:!0});i.extend(Se,{Location:W,statusCode:se.statusCode,headers:se.headers}),a(null,Se)})}function je(e,a){var n=e.CopySource||"",u=i.getSourceParams.call(this,n);if(!u){a(i.error(new Error("CopySource format error")));return}var v=u.Bucket,g=u.Region,k=decodeURIComponent(u.Key);X.call(this,{Scope:[{action:"name/cos:GetObject",bucket:v,region:g,prefix:k},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers,tracker:e.tracker},function(B,_){if(B)return a(B);var ce=i.clone(_.CopyPartResult||{});i.extend(ce,{statusCode:_.statusCode,headers:_.headers}),a(null,ce)})}function V(e,a){var n=e.Objects||[],u=e.Quiet;n=i.isArray(n)?n:[n];var v=i.json2xml({Delete:{Object:n,Quiet:u||!1}}),g=e.Headers;g["Content-Type"]="application/xml",g["Content-MD5"]=i.b64(i.md5(v));var k=i.map(n,function(B){return{action:"name/cos:DeleteObject",bucket:e.Bucket,region:e.Region,prefix:B.Key}});X.call(this,{Scope:k,method:"POST",Bucket:e.Bucket,Region:e.Region,body:v,action:"delete",headers:g,tracker:e.tracker},function(B,_){if(B)return a(B);var ce=_.DeleteResult||{},se=ce.Deleted||[],Se=ce.Error||[];se=i.isArray(se)?se:[se],Se=i.isArray(Se)?Se:[Se];var W=i.clone(ce);i.extend(W,{Error:Se,Deleted:se,statusCode:_.statusCode,headers:_.headers}),a(null,W)})}function m(e,a){var n=e.Headers;if(!e.RestoreRequest){a(i.error(new Error("missing param RestoreRequest")));return}var u=e.RestoreRequest||{},v=i.json2xml({RestoreRequest:u});n["Content-Type"]="application/xml",n["Content-MD5"]=i.b64(i.md5(v)),X.call(this,{Action:"name/cos:RestoreObject",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,body:v,action:"restore",headers:n,tracker:e.tracker},a)}function A(e,a){var n=e.Tagging||{},u=n.TagSet||n.Tags||e.Tags||[];u=i.clone(i.isArray(u)?u:[u]);var v=i.json2xml({Tagging:{TagSet:{Tag:u}}}),g=e.Headers;g["Content-Type"]="application/xml",g["Content-MD5"]=i.b64(i.md5(v)),X.call(this,{Action:"name/cos:PutObjectTagging",method:"PUT",Bucket:e.Bucket,Key:e.Key,Region:e.Region,body:v,action:"tagging",headers:g,VersionId:e.VersionId,tracker:e.tracker},function(k,B){if(k&&k.statusCode===204)return a(null,{statusCode:k.statusCode});if(k)return a(k);a(null,{statusCode:B.statusCode,headers:B.headers})})}function O(e,a){X.call(this,{Action:"name/cos:GetObjectTagging",method:"GET",Key:e.Key,Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging",VersionId:e.VersionId,tracker:e.tracker},function(n,u){if(n){if(n.statusCode===404&&n.error&&(n.error==="Not Found"||n.error.Code==="NoSuchTagSet")){var v={Tags:[],statusCode:n.statusCode};n.headers&&(v.headers=n.headers),a(null,v)}else a(n);return}var g=[];try{g=u.Tagging.TagSet.Tag||[]}catch{}g=i.clone(i.isArray(g)?g:[g]),a(null,{Tags:g,statusCode:u.statusCode,headers:u.headers})})}function G(e,a){X.call(this,{Action:"name/cos:DeleteObjectTagging",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"tagging",VersionId:e.VersionId,tracker:e.tracker},function(n,u){if(n&&n.statusCode===204)return a(null,{statusCode:n.statusCode});if(n)return a(n);a(null,{statusCode:u.statusCode,headers:u.headers})})}function $(e,a){var n=e.SelectType;if(!n)return a(i.error(new Error("missing param SelectType")));var u=e.SelectRequest||{},v=i.json2xml({SelectRequest:u}),g=e.Headers;g["Content-Type"]="application/xml",g["Content-MD5"]=i.b64(i.md5(v)),X.call(this,{Action:"name/cos:GetObject",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"select",qs:{"select-type":e.SelectType},VersionId:e.VersionId,body:v,DataType:"arraybuffer",rawBody:!0,tracker:e.tracker},function(k,B){if(k&&k.statusCode===204)return a(null,{statusCode:k.statusCode});if(k)return a(k);var _=i.parseSelectPayload(B.body);a(null,{statusCode:B.statusCode,headers:B.headers,Body:_.body,Payload:_.payload})})}function re(e,a){var n=this,u=e.Headers,v=e.tracker;!u["Cache-Control"]&&!u["cache-control"]&&(u["Cache-Control"]=""),!u["Content-Type"]&&!u["content-type"]&&(u["Content-Type"]=e.Body&&e.Body.type||"");var g=e.Body&&(e.UploadAddMetaMd5||n.options.UploadAddMetaMd5);g&&v&&v.setParams({md5StartTime:new Date().getTime()}),i.getBodyMd5(g,e.Body,function(k){k&&(e.Headers["x-cos-meta-md5"]=k),g&&v&&v.setParams({md5EndTime:new Date().getTime()}),X.call(n,{Action:"name/cos:InitiateMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"uploads",headers:e.Headers,qs:e.Query,tracker:v},function(B,_){if(B)return v&&v.parent&&v.parent.setParams({errorNode:"multipartInit"}),a(B);if(_=i.clone(_||{}),_&&_.InitiateMultipartUploadResult)return a(null,i.extend(_.InitiateMultipartUploadResult,{statusCode:_.statusCode,headers:_.headers}));a(null,_)})},e.onHashProgress)}function fe(e,a){var n=this;i.getFileSize("multipartUpload",e,function(){var u=e.tracker,v=n.options.UploadCheckContentMd5;v&&u&&u.setParams({md5StartTime:new Date().getTime()}),i.getBodyMd5(v,e.Body,function(g){g&&(e.Headers["Content-MD5"]=i.b64(g)),v&&u&&u.setParams({md5EndTime:new Date().getTime()}),u&&u.setParams({partNumber:e.PartNumber}),X.call(n,{Action:"name/cos:UploadPart",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers,onProgress:e.onProgress,body:e.Body||null,tracker:u},function(k,B){if(k)return u&&u.parent&&u.parent.setParams({errorNode:"multipartUpload"}),a(k);a(null,{ETag:i.attr(B.headers,"etag",""),statusCode:B.statusCode,headers:B.headers})})})})}function ye(e,a){for(var n=this,u=e.UploadId,v=e.Parts,g=e.tracker,k=0,B=v.length;k<B;k++)v[k].ETag&&v[k].ETag.indexOf('"')===0||(v[k].ETag='"'+v[k].ETag+'"');var _=i.json2xml({CompleteMultipartUpload:{Part:v}});_=_.replace(/\n\s*/g,"");var ce=e.Headers;ce["Content-Type"]="application/xml",ce["Content-MD5"]=i.b64(i.md5(_)),X.call(this,{Action:"name/cos:CompleteMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{uploadId:u},body:_,headers:ce,tracker:g},function(se,Se){if(se)return g&&g.parent&&g.parent.setParams({errorNode:"multipartComplete"}),a(se);var W=ze({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:n.options.UseAccelerate?"accelerate":e.Region,object:e.Key,isLocation:!0}),Re=Se.CompleteMultipartUploadResult||{};if(Re.ProcessResults&&(Re.UploadResult={OriginalInfo:{Key:Re.Key,Location:W,ETag:Re.ETag,ImageInfo:Re.ImageInfo},ProcessResults:Re.ProcessResults},delete Re.ImageInfo,delete Re.ProcessResults),Re.CallbackResult){var we=Re.CallbackResult;if(we.Status==="200"&&we.CallbackBody)try{Re.CallbackBody=JSON.parse(i.decodeBase64(we.CallbackBody))}catch{Re.CallbackBody={}}else Re.CallbackError=we.Error||{};delete Re.CallbackResult}if(Re.ReturnBodyResult){var Ne=Re.ReturnBodyResult;if(Ne.Status==="200"&&Ne.ReturnBody)try{Re.ReturnBody=JSON.parse(i.decodeBase64(Ne.ReturnBody))}catch{Re.ReturnBody={}}else Re.ReturnError={Code:Ne.Code,Message:Ne.Message,Status:Ne.Status};delete Re.ReturnBodyResult}var Le=i.extend(Re,{Location:W,statusCode:Se.statusCode,headers:Se.headers});a(null,Le)})}function me(e,a){var n={};n.delimiter=e.Delimiter,n["encoding-type"]=e.EncodingType,n.prefix=e.Prefix||"",n["max-uploads"]=e.MaxUploads,n["key-marker"]=e.KeyMarker,n["upload-id-marker"]=e.UploadIdMarker,n=i.clearKey(n);var u=e.tracker;u&&u.setParams({signStartTime:new Date().getTime()}),X.call(this,{Action:"name/cos:ListMultipartUploads",ResourceKey:n.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n,action:"uploads",tracker:u},function(v,g){if(v)return u&&u.parent&&u.parent.setParams({errorNode:"multipartList"}),a(v);if(g&&g.ListMultipartUploadsResult){var k=g.ListMultipartUploadsResult.Upload||[];k=i.isArray(k)?k:[k],g.ListMultipartUploadsResult.Upload=k}var B=i.clone(g.ListMultipartUploadsResult||{});i.extend(B,{statusCode:g.statusCode,headers:g.headers}),a(null,B)})}function Ae(e,a){var n={},u=e.tracker;n.uploadId=e.UploadId,n["encoding-type"]=e.EncodingType,n["max-parts"]=e.MaxParts,n["part-number-marker"]=e.PartNumberMarker,X.call(this,{Action:"name/cos:ListParts",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:n,tracker:u},function(v,g){if(v)return u&&u.parent&&u.parent.setParams({errorNode:"multipartListPart"}),a(v);var k=g.ListPartsResult||{},B=k.Part||[];B=i.isArray(B)?B:[B],k.Part=B;var _=i.clone(k);i.extend(_,{statusCode:g.statusCode,headers:g.headers}),a(null,_)})}function Ie(e,a){var n={};n.uploadId=e.UploadId,X.call(this,{Action:"name/cos:AbortMultipartUpload",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:n,tracker:e.tracker},function(u,v){if(u)return a(u);a(null,{statusCode:v.statusCode,headers:v.headers})})}function Pe(e,a){X.call(this,{method:e.Method,Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:e.Action,headers:e.Headers,qs:e.Query,body:e.Body,Url:e.Url,rawBody:e.RawBody,DataType:e.DataType,tracker:e.tracker},function(n,u){if(n)return a(n);u&&u.body&&(u.Body=u.body,delete u.body),a(n,u)})}function He(e,a){var n=e.Headers;!n["Cache-Control"]&&!n["cache-control"]&&(n["Cache-Control"]=""),!n["Content-Type"]&&!n["content-type"]&&(n["Content-Type"]=e.Body&&e.Body.type||""),X.call(this,{Action:"name/cos:AppendObject",method:"POST",Bucket:e.Bucket,Region:e.Region,action:"append",Key:e.Key,body:e.Body,qs:{position:e.Position},headers:e.Headers,tracker:e.tracker},function(u,v){if(u)return a(u);a(null,v)})}function Ye(e){var a=this;return i.getAuth({SecretId:e.SecretId||this.options.SecretId||"",SecretKey:e.SecretKey||this.options.SecretKey||"",Bucket:e.Bucket,Region:e.Region,Method:e.Method,Key:e.Key,Query:e.Query,Headers:e.Headers,Expires:e.Expires,UseRawKey:a.options.UseRawKey,SystemClockOffset:a.options.SystemClockOffset})}function Ve(e,a){var n=this,u=e.UseAccelerate===void 0?n.options.UseAccelerate:e.UseAccelerate,v=ze({ForcePathStyle:n.options.ForcePathStyle,protocol:e.Protocol||n.options.Protocol,domain:e.Domain||n.options.Domain,bucket:e.Bucket,region:u?"accelerate":e.Region,object:e.Key}),g="";e.Query&&(g+=i.obj2str(e.Query)),e.QueryString&&(g+=(g?"&":"")+e.QueryString);var k=v;if(e.Sign!==void 0&&!e.Sign)return g&&(k+="?"+g),a(null,{Url:k}),k;var B=nt.call(this,{Bucket:e.Bucket,Region:e.Region,UseAccelerate:e.UseAccelerate,Url:v}),_=it.call(this,{Action:(e.Method||"").toUpperCase()==="PUT"?"name/cos:PutObject":"name/cos:GetObject",Bucket:e.Bucket||"",Region:e.Region||"",Method:e.Method||"get",Key:e.Key,Expires:e.Expires,Headers:e.Headers,Query:e.Query,SignHost:B,ForceSignHost:e.ForceSignHost===!1?!1:n.options.ForceSignHost},function(ce,se){if(a){if(ce){a(ce);return}var Se=function(we){var Ne=we.match(/q-url-param-list.*?(?=&)/g)[0],Le="q-url-param-list="+encodeURIComponent(Ne.replace(/q-url-param-list=/,"")).toLowerCase(),Me=new RegExp(Ne,"g"),Qe=we.replace(Me,Le);return Qe},W=v;W+="?"+(se.Authorization.indexOf("q-signature")>-1?Se(se.Authorization):"sign="+encodeURIComponent(se.Authorization)),se.SecurityToken&&(W+="&x-cos-security-token="+se.SecurityToken),se.ClientIP&&(W+="&clientIP="+se.ClientIP),se.ClientUA&&(W+="&clientUA="+se.ClientUA),se.Token&&(W+="&token="+se.Token),g&&(W+="&"+g),setTimeout(function(){a(null,{Url:W})})}});return _?(k+="?"+_.Authorization+(_.SecurityToken?"&x-cos-security-token="+_.SecurityToken:""),g&&(k+="&"+g)):g&&(k+="?"+g),k}function xe(e){var a={GrantFullControl:[],GrantWrite:[],GrantRead:[],GrantReadAcp:[],GrantWriteAcp:[],ACL:""},n={FULL_CONTROL:"GrantFullControl",WRITE:"GrantWrite",READ:"GrantRead",READ_ACP:"GrantReadAcp",WRITE_ACP:"GrantWriteAcp"},u=e&&e.AccessControlList||{},v=u.Grant;v&&(v=i.isArray(v)?v:[v]);var g={READ:0,WRITE:0,FULL_CONTROL:0};return v&&v.length&&i.each(v,function(k){var B=k.Grantee.URI&&k.Grantee.URI.endsWith("/groups/global/AllUsers");k.Grantee.ID==="qcs::cam::anyone:anyone"||B?g[k.Permission]=1:k.Grantee.ID!==e.Owner.ID&&a[n[k.Permission]].push('id="'+k.Grantee.ID+'"')}),g.FULL_CONTROL||g.WRITE&&g.READ?a.ACL="public-read-write":g.READ?a.ACL="public-read":a.ACL="private",i.each(n,function(k){a[k]=De(a[k].join(","))}),a}function De(e){var a=e.split(","),n={},u,v;for(u=0;u<a.length;)v=a[u].trim(),n[v]?a.splice(u,1):(n[v]=!0,a[u]=v,u++);return a.join(",")}function ze(e){var a=e.region||"",n=e.bucket||"",u=n.substr(0,n.lastIndexOf("-")),v=n.substr(n.lastIndexOf("-")+1),g=e.domain,k=e.object;typeof g=="function"&&(g=g({Bucket:n,Region:a})),["http","https"].includes(e.protocol)&&(e.protocol=e.protocol+":");var B=e.protocol||(i.isBrowser&&(typeof location>"u"?"undefined":P(location))==="object"&&location.protocol==="http:"?"http:":"https:");g||(["cn-south","cn-south-2","cn-north","cn-east","cn-southwest","sg"].indexOf(a)>-1?g="{Region}.myqcloud.com":g="cos.{Region}.myqcloud.com",e.ForcePathStyle||(g="{Bucket}."+g)),g=g.replace(/\{\{AppId\}\}/gi,v).replace(/\{\{Bucket\}\}/gi,u).replace(/\{\{Region\}\}/gi,a).replace(/\{\{.*?\}\}/gi,""),g=g.replace(/\{AppId\}/gi,v).replace(/\{BucketName\}/gi,u).replace(/\{Bucket\}/gi,n).replace(/\{Region\}/gi,a).replace(/\{.*?\}/gi,""),/^[a-zA-Z]+:\/\//.test(g)||(g=B+"//"+g),g.slice(-1)==="/"&&(g=g.slice(0,-1));var _=g;return e.ForcePathStyle&&(_+="/"+n),_+="/",k&&(_+=i.camSafeUrlEncode(k).replace(/%2F/g,"/")),e.isLocation&&(_=_.replace(/^https?:\/\//,"")),_}var nt=function(a){var n=a.Url||a.Bucket&&a.Region;if(!n)return"";var u=a.UseAccelerate===void 0?this.options.UseAccelerate:a.UseAccelerate,v=a.Url||ze({ForcePathStyle:this.options.ForcePathStyle,protocol:this.options.Protocol,domain:this.options.Domain,bucket:a.Bucket,region:u?"accelerate":a.Region}),g=v.replace(/^https?:\/\/([^/]+)(\/.*)?$/,"$1");return g};function it(e,a){var n=i.clone(e.Headers),u="";i.each(n,function(be,ve){(be===""||["content-type","cache-control","expires"].indexOf(ve.toLowerCase())>-1)&&delete n[ve],ve.toLowerCase()==="host"&&(u=be)});var v=e.ForceSignHost!==!1;!u&&e.SignHost&&v&&(n.Host=e.SignHost);var g=!1,k=function(ve,Ee){g||(g=!0,Ee&&Ee.XCosSecurityToken&&!Ee.SecurityToken&&(Ee=i.clone(Ee),Ee.SecurityToken=Ee.XCosSecurityToken,delete Ee.XCosSecurityToken),a&&a(ve,Ee))},B=this,_=e.Bucket||"",ce=e.Region||"",se=e.Key||"";B.options.ForcePathStyle&&_&&(se=_+"/"+se);var Se="/"+se,W={},Re=e.Scope;if(!Re){var we=e.Action||"",Ne=e.ResourceKey||e.Key||"";Re=e.Scope||[{action:we,bucket:_,region:ce,prefix:Ne}]}var Le=i.md5(JSON.stringify(Re));B._StsCache=B._StsCache||[],function(){var be,ve;for(be=B._StsCache.length-1;be>=0;be--){ve=B._StsCache[be];var Ee=Math.round(i.getSkewTime(B.options.SystemClockOffset)/1e3)+30;if(ve.StartTime&&Ee<ve.StartTime||Ee>=ve.ExpiredTime){B._StsCache.splice(be,1);continue}if(!ve.ScopeLimit||ve.ScopeLimit&&ve.ScopeKey===Le){W=ve;break}}}();var Me=function(){var ve="";W.StartTime&&e.Expires?ve=W.StartTime+";"+(W.StartTime+e.Expires*1):W.StartTime&&W.ExpiredTime&&(ve=W.StartTime+";"+W.ExpiredTime);var Ee=i.getAuth({SecretId:W.TmpSecretId,SecretKey:W.TmpSecretKey,Method:e.Method,Pathname:Se,Query:e.Query,Headers:n,Expires:e.Expires,UseRawKey:B.options.UseRawKey,SystemClockOffset:B.options.SystemClockOffset,KeyTime:ve,ForceSignHost:v}),Te={Authorization:Ee,SecurityToken:W.SecurityToken||W.XCosSecurityToken||"",Token:W.Token||"",ClientIP:W.ClientIP||"",ClientUA:W.ClientUA||"",SignFrom:"client"};k(null,Te)},Qe=function(ve){if(ve.Authorization){var Ee=!1,Te=ve.Authorization;if(Te)if(Te.indexOf(" ")>-1)Ee=!1;else if(Te.indexOf("q-sign-algorithm=")>-1&&Te.indexOf("q-ak=")>-1&&Te.indexOf("q-sign-time=")>-1&&Te.indexOf("q-key-time=")>-1&&Te.indexOf("q-url-param-list=")>-1)Ee=!0;else try{Te=atob(Te),Te.indexOf("a=")>-1&&Te.indexOf("k=")>-1&&Te.indexOf("t=")>-1&&Te.indexOf("r=")>-1&&Te.indexOf("b=")>-1&&(Ee=!0)}catch{}if(!Ee)return i.error(new Error("getAuthorization callback params format error"))}else{if(!ve.TmpSecretId)return i.error(new Error('getAuthorization callback params missing "TmpSecretId"'));if(!ve.TmpSecretKey)return i.error(new Error('getAuthorization callback params missing "TmpSecretKey"'));if(!ve.SecurityToken&&!ve.XCosSecurityToken)return i.error(new Error('getAuthorization callback params missing "SecurityToken"'));if(!ve.ExpiredTime)return i.error(new Error('getAuthorization callback params missing "ExpiredTime"'));if(ve.ExpiredTime&&ve.ExpiredTime.toString().length!==10)return i.error(new Error('getAuthorization callback params "ExpiredTime" should be 10 digits'));if(ve.StartTime&&ve.StartTime.toString().length!==10)return i.error(new Error('getAuthorization callback params "StartTime" should be 10 StartTime'))}return!1};if(W.ExpiredTime&&W.ExpiredTime-i.getSkewTime(B.options.SystemClockOffset)/1e3>60)Me();else if(B.options.getAuthorization)B.options.getAuthorization.call(B,{Bucket:_,Region:ce,Method:e.Method,Key:se,Pathname:Se,Query:e.Query,Headers:n,Scope:Re,SystemClockOffset:B.options.SystemClockOffset,ForceSignHost:v},function(be){typeof be=="string"&&(be={Authorization:be});var ve=Qe(be);if(ve)return k(ve);be.Authorization?k(null,be):(W=be||{},W.Scope=Re,W.ScopeKey=Le,B._StsCache.push(W),Me())});else if(B.options.getSTS)B.options.getSTS.call(B,{Bucket:_,Region:ce},function(be){W=be||{},W.Scope=Re,W.ScopeKey=Le,W.TmpSecretId||(W.TmpSecretId=W.SecretId),W.TmpSecretKey||(W.TmpSecretKey=W.SecretKey);var ve=Qe(W);if(ve)return k(ve);B._StsCache.push(W),Me()});else return function(){var be="";if(B.options.StartTime&&e.Expires){if(B.options.StartTime.toString().length!==10)return k(i.error(new Error('params "StartTime" should be 10 digits')));be=B.options.StartTime+";"+(B.options.StartTime+e.Expires*1)}else if(B.options.StartTime&&B.options.ExpiredTime){if(B.options.StartTime.toString().length!==10)return k(i.error(new Error('params "StartTime" should be 10 digits')));if(B.options.ExpiredTime.toString().length!==10)return k(i.error(new Error('params "ExpiredTime" should be 10 digits')));be=B.options.StartTime+";"+B.options.ExpiredTime*1}var ve=i.getAuth({SecretId:e.SecretId||B.options.SecretId,SecretKey:e.SecretKey||B.options.SecretKey,Method:e.Method,Pathname:Se,Query:e.Query,Headers:n,Expires:e.Expires,KeyTime:be,UseRawKey:B.options.UseRawKey,SystemClockOffset:B.options.SystemClockOffset,ForceSignHost:v}),Ee={Authorization:ve,SecurityToken:B.options.SecurityToken||B.options.XCosSecurityToken,SignFrom:"client"};return k(null,Ee),Ee}();return""}function pt(e){var a=this,n=!1,u=!1,v=!1,g=e.headers&&(e.headers.date||e.headers.Date)||e.error&&e.error.ServerTime;try{var k=e.error.Code,B=e.error.Message;(k==="RequestTimeTooSkewed"||k==="AccessDenied"&&B==="Request has expired")&&(u=!0)}catch{}if(e)if(u&&g){var _=Date.parse(g);this.options.CorrectClockSkew&&Math.abs(i.getSkewTime(this.options.SystemClockOffset)-_)>=3e4&&(console.error("error: Local time is too skewed."),this.options.SystemClockOffset=_-Date.now(),n=!0)}else Math.floor(e.statusCode/100)===5?(n=!0,v=!1):(e.message==="timeout"||e.message==="CORS blocked or network error")&&(n=!0,v=a.options.AutoSwitchHost);return{canRetry:n,networkError:v}}function yt(e){var a=e.requestUrl,n=e.clientCalcSign,u=e.networkError;if(!this.options.AutoSwitchHost||!a||!n||!u)return!1;var v=/^https?:\/\/[^\/]*\.cos\.[^\/]*\.myqcloud\.com(\/.*)?$/,g=/^https?:\/\/[^\/]*\.cos\.accelerate\.myqcloud\.com(\/.*)?$/,k=v.test(a)&&!g.test(a);return k}function X(e,a){var n=this;!e.headers&&(e.headers={}),!e.qs&&(e.qs={}),e.VersionId&&(e.qs.versionId=e.VersionId),e.qs=i.clearKey(e.qs),e.headers&&(e.headers=i.clearKey(e.headers)),e.qs&&(e.qs=i.clearKey(e.qs));var u=i.clone(e.qs);e.action&&(u[e.action]="");var v=e.url||e.Url,g=e.SignHost||nt.call(this,{Bucket:e.Bucket,Region:e.Region,Url:v}),k=e.tracker,B=function(ce){var se=n.options.SystemClockOffset;k&&k.setParams({signStartTime:new Date().getTime(),httpRetryTimes:ce-1}),e.SwitchHost&&(g=g.replace(/myqcloud.com/,"tencentcos.cn"));var Se=M(M({},e),{},{Query:u,SignHost:g,ForceSignHost:n.options.ForceSignHost});delete Se.tracker,n.logger.debug({cate:"PROCESS",tag:"base",msg:"开始计算签名, opt=".concat(JSON.stringify(Se))}),it.call(n,{Bucket:e.Bucket||"",Region:e.Region||"",Method:e.method,Key:e.Key,Query:u,Headers:e.headers,SignHost:g,Action:e.Action,ResourceKey:e.ResourceKey,Scope:e.Scope,ForceSignHost:n.options.ForceSignHost,SwitchHost:e.SwitchHost},function(W,Re){if(W){n.logger.error({cate:"PROCESS",tag:"base",msg:"签名获取失败, err=".concat(JSON.stringify(W.message))}),a(W);return}k&&k.setParams({signEndTime:new Date().getTime(),httpStartTime:new Date().getTime()}),e.AuthData=Re,n.logger.debug({cate:"PROCESS",tag:"base",msg:"签名获取成功"}),n.logger.info({cate:"PROCESS",tag:"base",msg:"准备发起请求"}),Ct.call(n,e,function(we,Ne){k&&k.setParams({httpEndTime:new Date().getTime()});var Le=!1,Me=!1;if(we){var Qe=pt.call(n,we);Le=Qe.canRetry||se!==n.options.SystemClockOffset,Me=Qe.networkError,n.logger.error({cate:"PROCESS",tag:"network",msg:"请求失败, err=".concat(JSON.stringify(we),", canRetry=").concat(Le,", networkError=").concat(Me,", tryTimes=").concat(ce)})}if(we&&ce<4&&Le){e.headers&&(delete e.headers.Authorization,delete e.headers.token,delete e.headers.clientIP,delete e.headers.clientUA,e.headers["x-cos-security-token"]&&delete e.headers["x-cos-security-token"],e.headers["x-ci-security-token"]&&delete e.headers["x-ci-security-token"]);var be=yt.call(n,{requestUrl:(we==null?void 0:we.url)||"",clientCalcSign:Re.SignFrom==="client",networkError:Me});e.SwitchHost=be,e.headers["x-cos-sdk-retry"]=!0,n.logger.info({cate:"PROCESS",tag:"base",msg:"重试请求, 重试第".concat(ce,"次")}),B(ce+1)}else n.logger.info({cate:"PROCESS",tag:"base",msg:"请求完成"}),a(we,Ne)})})};B(1)}function Ct(e,a){var n=this,u=e.TaskId;if(!(u&&!n._isRunningTask(u))){var v=e.Bucket,g=e.Region,k=e.Key,B=e.method||"GET",_=e.Url||e.url,ce=e.body,se=e.rawBody;n.options.UseAccelerate&&(g="accelerate"),_=_||ze({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:v,region:g,object:k}),e.SwitchHost&&(_=_.replace(/myqcloud.com/,"tencentcos.cn"));var Se=k?_:"";e.action&&(_=_+"?"+(i.isIOS_QQ?"".concat(e.action,"="):e.action)),e.qsStr&&(_.indexOf("?")>-1?_=_+"&"+e.qsStr:_=_+"?"+e.qsStr);var W={method:B,url:_,headers:e.headers,qs:e.qs,body:ce},Re="x-cos-security-token";if(i.isCIHost(_)&&(Re="x-ci-security-token"),W.headers.Authorization=e.AuthData.Authorization,e.AuthData.Token&&(W.headers.token=e.AuthData.Token),e.AuthData.ClientIP&&(W.headers.clientIP=e.AuthData.ClientIP),e.AuthData.ClientUA&&(W.headers.clientUA=e.AuthData.ClientUA),e.AuthData.SecurityToken&&(W.headers[Re]=e.AuthData.SecurityToken),e.Action&&(W.action=e.Action),W.key=e.Key||e.ResourceKey,W.headers&&(W.headers=i.clearKey(W.headers)),W=i.clearKey(W),e.onProgress&&typeof e.onProgress=="function"){var we=ce&&(ce.size||ce.length)||0;W.onProgress=function(Te){if(!(u&&!n._isRunningTask(u))){var qe=Te?Te.loaded:0;e.onProgress({loaded:qe,total:we})}}}e.onDownloadProgress&&(W.onDownloadProgress=e.onDownloadProgress),e.DataType&&(W.dataType=e.DataType),this.options.Timeout&&(W.timeout=this.options.Timeout),n.options.ForcePathStyle&&(W.pathStyle=n.options.ForcePathStyle);var Ne=i.uuid();n.logger.info({cate:"PROCESS",tag:"network",msg:"[Request] ".concat(Ne,", requestOpt=").concat(JSON.stringify(W))}),n.emit("before-send",W);var Le=W.url.includes("accelerate."),Me=W.qs?Object.keys(W.qs).map(function(Te){return"".concat(Te,"=").concat(W.qs[Te])}).join("&"):"",Qe=Me?W.url+"?"+Me:W.url;if(e.tracker){var be;e.tracker.setParams({url:Qe,httpMethod:W.method,accelerate:Le,httpSize:((be=W.body)===null||be===void 0?void 0:be.size)||0}),e.tracker.parent&&!e.tracker.parent.params.url&&e.tracker.parent.setParams({url:Se,accelerate:Le})}var ve=(n.options.Request||L)(W,function(Te){if(!(Te&&Te.error==="abort")){var qe={options:W,error:Te&&Te.error,statusCode:Te&&Te.statusCode||0,statusMessage:Te&&Te.statusMessage||"",headers:Te&&Te.headers||{},body:Te&&Te.body};n.emit("after-receive",qe);var St=qe.error,st=qe.body,Je={statusCode:qe.statusCode,statusMessage:qe.statusMessage,headers:qe.headers},Ot=St?"[error]":"[success]";n.logger.info({cate:"PROCESS",tag:"network",msg:"[Response] ".concat(Ne,", ").concat(Ot,", response=").concat(JSON.stringify(Je))});var xt,et=function(tt,Ue){if(u&&n.off("inner-kill-task",Ee),!xt){xt=!0;var ot={};if(Je&&Je.statusCode&&(ot.statusCode=Je.statusCode),Je&&Je.headers&&(ot.headers=Je.headers),tt)W.url&&(ot.url=W.url),W.method&&(ot.method=W.method),tt=i.extend(tt||{},ot),a(tt,null);else{if(e.Action==="name/cos:PutObject"){var Tt={};for(var Bt in e.headers){var wt=Bt.toLowerCase();Tt[wt]=e.headers[Bt]}Tt["x-cos-callback"]?Ue.Error?(Ue.CallbackError=i.clone(Ue.Error),delete Ue.Error):Ue.CallbackBody=i.clone(Ue):Tt["x-cos-return-body"]&&(Ue.Error?(Ue.ReturnError=i.clone(Ue.Error),delete Ue.Error):Ue.ReturnBody=i.clone(Ue))}Ue=i.extend(Ue||{},ot),a(null,Ue)}ve=null}};if(St)return et(i.error(St));var ut=Je.statusCode,mt=Math.floor(ut/100)===2;if(se){if(mt)return et(null,{body:st});if(st instanceof Blob){i.readAsBinaryString(st,function(bt){var tt=i.parseResBody(bt),Ue=tt.Error||tt;return et(i.error(new Error(Ue.Message||"response body error"),{code:Ue.Code,error:Ue}))});return}}var kt=i.parseResBody(st),ct=kt.Error||kt;mt?et(null,kt):ct?et(i.error(new Error(ct.Message),{code:ct.Code,error:ct})):ut?et(i.error(new Error(Je.statusMessage),{code:""+ut})):ut&&et(i.error(new Error("statusCode error")))}}),Ee=function(qe){qe.TaskId===u&&(ve&&ve.abort&&ve.abort(),n.off("inner-kill-task",Ee))};u&&n.on("inner-kill-task",Ee)}}var Rt={getService:C,putBucket:d,headBucket:S,getBucket:f,deleteBucket:y,putBucketAcl:x,getBucketAcl:b,putBucketCors:w,getBucketCors:U,deleteBucketCors:z,getBucketLocation:q,getBucketPolicy:l,putBucketPolicy:h,deleteBucketPolicy:T,putBucketTagging:s,getBucketTagging:o,deleteBucketTagging:t,putBucketLifecycle:r,getBucketLifecycle:c,deleteBucketLifecycle:p,putBucketVersioning:I,getBucketVersioning:R,putBucketReplication:K,getBucketReplication:F,deleteBucketReplication:J,putBucketWebsite:Q,getBucketWebsite:ee,deleteBucketWebsite:te,putBucketReferer:Ce,getBucketReferer:ae,putBucketDomain:ie,getBucketDomain:Z,deleteBucketDomain:D,putBucketOrigin:Y,getBucketOrigin:le,deleteBucketOrigin:de,putBucketLogging:oe,getBucketLogging:ue,putBucketInventory:ge,postBucketInventory:pe,getBucketInventory:Be,listBucketInventory:ke,deleteBucketInventory:Oe,putBucketAccelerate:Ge,getBucketAccelerate:Fe,putBucketEncryption:We,getBucketEncryption:Ze,deleteBucketEncryption:_e,getObject:at,headObject:$e,listObjectVersions:rt,putObject:lt,deleteObject:dt,getObjectAcl:ft,putObjectAcl:ht,optionsObject:gt,putObjectCopy:vt,deleteMultipleObject:V,restoreObject:m,putObjectTagging:A,getObjectTagging:O,deleteObjectTagging:G,selectObjectContent:$,appendObject:He,uploadPartCopy:je,multipartInit:re,multipartUpload:fe,multipartComplete:ye,multipartList:me,multipartListPart:Ae,multipartAbort:Ie,request:Pe,getObjectUrl:Ve,getAuth:Ye};function Pt(e,a,n){i.each(["Cors","Acl"],function(u){if(e.slice(-u.length)===u){var v=e.slice(0,-u.length)+u.toUpperCase(),g=i.apiWrapper(e,a),k=!1;n[v]=function(){!k&&console.warn("warning: cos."+v+" has been deprecated. Please Use cos."+e+" instead."),k=!0,g.apply(this,arguments)}}})}H.exports.init=function(e,a){a.transferToTaskMethod(Rt,"putObject"),i.each(Rt,function(n,u){e.prototype[u]=i.apiWrapper(u,n),Pt(u,n,e.prototype)})}},"./src/cos.js":function(H,ne,E){var N=E("./src/util.js"),P=E("./src/event.js"),j=E("./src/task.js"),M=E("./src/base.js"),L=E("./src/advance.js"),i=E("./src/logger.js"),C=E("./package.json"),d={AppId:"",SecretId:"",SecretKey:"",SecurityToken:"",StartTime:0,ExpiredTime:0,ChunkRetryTimes:2,FileParallelLimit:3,ChunkParallelLimit:3,ChunkSize:1024*1024,SliceSize:1024*1024,CopyChunkParallelLimit:20,CopyChunkSize:1024*1024*10,CopySliceSize:1024*1024*10,MaxPartNumber:1e4,ProgressInterval:1e3,Domain:"",ServiceDomain:"",Protocol:"",CompatibilityMode:!1,ForcePathStyle:!1,UseRawKey:!1,Timeout:0,CorrectClockSkew:!0,SystemClockOffset:0,UploadCheckContentMd5:!1,UploadQueueSize:1e4,UploadAddMetaMd5:!1,UploadIdCacheLimit:50,UseAccelerate:!1,ForceSignHost:!0,AutoSwitchHost:!0,CopySourceParser:null,ObjectKeySimplifyCheck:!0,DeepTracker:!1,TrackerDelay:5e3,CustomId:"",BeaconReporter:null,ClsReporter:null,EnableLog:!1,EnableLogcat:!1,LogLevel:"VERBOSE",ClsLogger:null,LogExtras:{}},S=function(y){var x,b,w=this;if(this.options=N.extend(N.clone(d),y||{}),this.options.FileParallelLimit=Math.max(1,this.options.FileParallelLimit),this.options.ChunkParallelLimit=Math.max(1,this.options.ChunkParallelLimit),this.options.ChunkRetryTimes=Math.max(0,this.options.ChunkRetryTimes),this.options.ChunkSize=Math.max(1024*1024,this.options.ChunkSize),this.options.CopyChunkParallelLimit=Math.max(1,this.options.CopyChunkParallelLimit),this.options.CopyChunkSize=Math.max(1024*1024,this.options.CopyChunkSize),this.options.CopySliceSize=Math.max(0,this.options.CopySliceSize),this.options.MaxPartNumber=Math.max(1024,Math.min(1e4,this.options.MaxPartNumber)),this.options.Timeout=Math.max(0,this.options.Timeout),this.options.EnableReporter=this.options.BeaconReporter||this.options.ClsReporter,this.options.AppId&&console.warn('warning: AppId has been deprecated, Please put it at the end of parameter Bucket(E.g: "test-1250000000").'),this.options.SecretId&&this.options.SecretId.indexOf(" ")>-1&&(console.error("error: SecretId格式错误，请检查"),console.error("error: SecretId format is incorrect. Please check")),this.options.SecretKey&&this.options.SecretKey.indexOf(" ")>-1&&(console.error("error: SecretKey格式错误，请检查"),console.error("error: SecretKey format is incorrect. Please check")),N.isNode()&&(console.log("Tip: Next.js、Nuxt.js 等服务端渲染技术可正常使用JavaScript SDK，请忽略下方 nodejs 环境警告"),console.warn("warning: cos-js-sdk-v5 不支持 nodejs 环境使用，请改用 cos-nodejs-sdk-v5，参考文档： https://cloud.tencent.com/document/product/436/8629"),console.warn("warning: cos-js-sdk-v5 does not support nodejs environment. Please use cos-nodejs-sdk-v5 instead. See: https://cloud.tencent.com/document/product/436/8629")),this.options.ForcePathStyle)throw console.warn("cos-js-sdk-v5不再支持使用path-style，仅支持使用virtual-hosted-style，参考文档：https://cloud.tencent.com/document/product/436/96243"),new Error("ForcePathStyle is not supported");P.init(this),j.init(this),this.logger=new i({enableLog:this.options.EnableLog,enableLogcat:this.options.EnableLogcat,level:(x=this.options.LogLevel)!==null&&x!==void 0?x:"VERBOSE",clsLogger:this.options.ClsLogger,logExtras:(b=this.options.LogExtras)!==null&&b!==void 0?b:{}}),this.options.EnableLog&&(P.init(this.logger),this.logger.on("log-message",function(U){w.emit("log-message",U)}))};M.init(S,j),L.init(S,j),S.util={md5:N.md5,xml2json:N.xml2json,json2xml:N.json2xml,encodeBase64:N.encodeBase64},S.getAuthorization=N.getAuth,S.version=C.version,H.exports=S},"./src/event.js":function(H,ne){var E=function(j){var M={},L=function(C){return!M[C]&&(M[C]=[]),M[C]};j.on=function(i,C){i==="task-list-update"&&console.warn('warning: Event "'+i+'" has been deprecated. Please use "list-update" instead.'),L(i).push(C)},j.off=function(i,C){for(var d=L(i),S=d.length-1;S>=0;S--)C===d[S]&&d.splice(S,1)},j.emit=function(i,C){for(var d=L(i).map(function(f){return f}),S=0;S<d.length;S++)d[S](C)}},N=function(){E(this)};H.exports.init=E,H.exports.EventProxy=N},"./src/logger.js":function(H,ne,E){var N=E("./node_modules/@babel/runtime/helpers/classCallCheck.js"),P=E("./node_modules/@babel/runtime/helpers/createClass.js"),j=E("./node_modules/@babel/runtime/helpers/defineProperty.js"),M=E("./package.json"),L=M.version,i=["VERBOSE","DEBUG","INFO","WARN","ERROR"],C=function(){function d(S){var f;N(this,d),j(this,"level","VERBOSE"),j(this,"clsLogger",null),j(this,"logExtras",{}),this.enableLog=(f=S.enableLog)!==null&&f!==void 0?f:!1,this.level=S.level||"VERBOSE",i.includes(this.level)||(this.level="VERBOSE"),this.enableLogcat=S.enableLogcat,this.clsLogger=S.clsLogger,this.logExtras=S.logExtras}return P(d,[{key:"info",value:function(){if(["VERBOSE","INFO"].includes(this.level)){for(var f=arguments.length,y=new Array(f),x=0;x<f;x++)y[x]=arguments[x];this.log.apply(this,["info"].concat(y))}}},{key:"debug",value:function(){if(["VERBOSE","DEBUG"].includes(this.level)){for(var f=arguments.length,y=new Array(f),x=0;x<f;x++)y[x]=arguments[x];this.log.apply(this,["debug"].concat(y))}}},{key:"warn",value:function(){if(["VERBOSE","WARN"].includes(this.level)){for(var f=arguments.length,y=new Array(f),x=0;x<f;x++)y[x]=arguments[x];this.log.apply(this,["warn"].concat(y))}}},{key:"error",value:function(){if(["VERBOSE","ERROR"].includes(this.level)){for(var f=arguments.length,y=new Array(f),x=0;x<f;x++)y[x]=arguments[x];this.log.apply(this,["error"].concat(y))}}},{key:"log",value:function(){if(this.enableLog){var f=arguments.length<=0?void 0:arguments[0],y=arguments.length<=1?void 0:arguments[1],x=y.cate,b=x===void 0?"base":x,w=y.tag,U=w===void 0?"base":w,z=y.msg,q={version:"cos-js-sdk-v5-".concat(L),timestamp:new Date().toISOString(),cate:"[".concat(b.toUpperCase(),"]"),tag:"[".concat(U.toUpperCase(),"]"),msg:z,extras:this.logExtras};this.enableLogcat&&console[f]("[".concat(q.version,"] ").concat(q.timestamp," ").concat(q.cate," ").concat(q.tag," ").concat(q.msg," ").concat(q.extras?JSON.stringify(q.extras):"")),this.clsLogger&&this.clsLogger.log(q,!1),this.emit("log-message",q)}}}])}();H.exports=C},"./src/session.js":function(H,ne,E){var N=E("./src/util.js"),P="cos_sdk_upload_cache",j=30*24*3600,M,L,i=function(){try{var x=JSON.parse(localStorage.getItem(P))}catch{}x||(x=[]),M=x},C=function(){try{M.length?localStorage.setItem(P,JSON.stringify(M)):localStorage.removeItem(P)}catch{}},d=function(){if(!M){i.call(this);for(var x=!1,b=Math.round(Date.now()/1e3),w=M.length-1;w>=0;w--){var U=M[w][2];(!U||U+j<b)&&(M.splice(w,1),x=!0)}x&&C()}},S=function(){L||(L=setTimeout(function(){C(),L=null},400))},f={using:{},setUsing:function(x){f.using[x]=!0},removeUsing:function(x){delete f.using[x]},getFileId:function(x,b,w,U){return x.name&&x.size&&x.lastModifiedDate&&b?N.md5([x.name,x.size,x.lastModifiedDate,b,w,U].join("::")):null},getCopyFileId:function(x,b,w,U,z){var q=b["content-length"],h=b.etag||"",l=b["last-modified"];return x&&w?N.md5([x,q,h,l,w,U,z].join("::")):null},getUploadIdList:function(x){if(!x)return null;d.call(this);for(var b=[],w=0;w<M.length;w++)M[w][0]===x&&b.push(M[w][1]);return b.length?b:null},saveUploadId:function(x,b,w){if(d.call(this),!!x){for(var U=M.length-1;U>=0;U--){var z=M[U];z[0]===x&&z[1]===b&&M.splice(U,1)}M.unshift([x,b,Math.round(Date.now()/1e3)]),M.length>w&&M.splice(w),S()}},removeUploadId:function(x){d.call(this),delete f.using[x];for(var b=M.length-1;b>=0;b--)M[b][1]===x&&M.splice(b,1);S()}};H.exports=f},"./src/task.js":function(H,ne,E){var N=E("./src/session.js"),P=E("./src/util.js"),j={},M=function(C,d){j[d]=C[d],C[d]=function(S,f){S.SkipTask?j[d].call(this,S,f):this._addTask(d,S,f)}},L=function(C){var d=[],S={},f=0,y=0,x=function(l){var T={id:l.id,Bucket:l.Bucket,Region:l.Region,Key:l.Key,FilePath:l.FilePath,state:l.state,loaded:l.loaded,size:l.size,speed:l.speed,percent:l.percent,hashPercent:l.hashPercent,error:l.error};return l.FilePath&&(T.FilePath=l.FilePath),l._custom&&(T._custom=l._custom),T},b=function(){var h,l=function(){h=0,C.emit("task-list-update",{list:P.map(d,x)}),C.emit("list-update",{list:P.map(d,x)})};return function(){h||(h=setTimeout(l))}}(),w=function(){if(!(d.length<=C.options.UploadQueueSize)){for(var l=0;l<y&&l<d.length&&d.length>C.options.UploadQueueSize;){var T=d[l].state==="waiting"||d[l].state==="checking"||d[l].state==="uploading";!d[l]||!T?(S[d[l].id]&&delete S[d[l].id],d.splice(l,1),y--):l++}b()}},U=function(){if(!(f>=C.options.FileParallelLimit)){for(;d[y]&&d[y].state!=="waiting";)y++;if(!(y>=d.length)){var l=d[y];y++,f++,l.state="checking",l.params.onTaskStart&&l.params.onTaskStart(x(l)),!l.params.UploadData&&(l.params.UploadData={});var T=P.formatParams(l.api,l.params);j[l.api].call(C,T,function(s,o){C._isRunningTask(l.id)&&((l.state==="checking"||l.state==="uploading")&&(l.state=s?"error":"success",s&&(l.error=s),f--,b(),U(),l.callback&&l.callback(s,o),l.state==="success"&&(l.params&&(delete l.params.UploadData,delete l.params.Body,delete l.params),delete l.callback)),w())}),b(),setTimeout(U)}}},z=function(l,T){var s=S[l];if(s){var o=s&&s.state==="waiting",t=s&&(s.state==="checking"||s.state==="uploading");if(T==="canceled"&&s.state!=="canceled"||T==="paused"&&o||T==="paused"&&t){s.state=T,C.emit("inner-kill-task",{TaskId:l,toState:T});try{var r=s&&s.params&&s.params.UploadData.UploadId}catch{}T==="canceled"&&r&&N.removeUsing(r),b(),t&&(f--,U()),T==="canceled"&&(s.params&&(delete s.params.UploadData,delete s.params.Body,delete s.params),delete s.callback)}w()}};C._addTasks=function(h){P.each(h,function(l){C._addTask(l.api,l.params,l.callback,!0)}),b()};var q=!0;C._addTask=function(h,l,T,s){l=P.formatParams(h,l);var o=P.uuid();l.TaskId=o,l.onTaskReady&&l.onTaskReady(o),l.TaskReady&&(l.TaskReady(o),q&&console.warn('warning: Param "TaskReady" has been deprecated. Please use "onTaskReady" instead.'),q=!1);var t={params:l,callback:T,api:h,index:d.length,id:o,Bucket:l.Bucket,Region:l.Region,Key:l.Key,FilePath:l.FilePath||"",state:"waiting",loaded:0,size:0,speed:0,percent:0,hashPercent:0,error:null,_custom:l._custom},r=l.onHashProgress;l.onHashProgress=function(p){C._isRunningTask(t.id)&&(t.hashPercent=p.percent,r&&r(p),b())};var c=l.onProgress;return l.onProgress=function(p){C._isRunningTask(t.id)&&(t.state==="checking"&&(t.state="uploading"),t.loaded=p.loaded,t.speed=p.speed,t.percent=p.percent,c&&c(p),b())},P.getFileSize(h,l,function(p,I){if(p)return T(P.error(p));S[o]=t,d.push(t),t.size=I,!s&&b(),U(),w()}),o},C._isRunningTask=function(h){var l=S[h];return!!(l&&(l.state==="checking"||l.state==="uploading"))},C.getTaskList=function(){return P.map(d,x)},C.cancelTask=function(h){z(h,"canceled")},C.pauseTask=function(h){z(h,"paused")},C.restartTask=function(h){var l=S[h];l&&(l.state==="paused"||l.state==="error")&&(l.state="waiting",b(),y=Math.min(y,l.index),U())},C.isUploadRunning=function(){return f||y<d.length}};H.exports.transferToTaskMethod=M,H.exports.init=L},"./src/tracker.js":function(H,ne,E){var N=E("./node_modules/@babel/runtime/helpers/classCallCheck.js"),P=E("./node_modules/@babel/runtime/helpers/createClass.js"),j=E("./node_modules/@babel/runtime/helpers/typeof.js"),M=E("./package.json"),L=null,i=function(T,s){if(!L){if(typeof T!="function")throw new Error("Beacon not found");L=new T({appkey:"0WEB05PY6MHRGK0U",versionCode:M.version,channelID:"js_sdk",openid:"openid",unionid:"unid",strictMode:!1,delay:s,sessionDuration:60*1e3})}return L},C=function(T){return!T||T<0?0:(T/1e3).toFixed(3)},d={getUid:function(){var T=function(){return((1+Math.random())*65536|0).toString(16).substring(1)};return T()+T()+"-"+T()+"-"+T()+"-"+T()+"-"+T()+T()+T()},getNetType:function(){if((typeof navigator>"u"?"undefined":j(navigator))==="object"){var T=navigator.connection||navigator.mozConnection||navigator.webkitConnection;return(T==null?void 0:T.type)||(T==null?void 0:T.effectiveType)||"unknown"}return"unknown"},getProtocol:function(){return(typeof location>"u"?"undefined":j(location))==="object"?location.protocol.replace(/:/,""):"unknown protocol"},getOsType:function(){if((typeof navigator>"u"?"undefined":j(navigator))!=="object")return"unknown os";var T=navigator.userAgent.toLowerCase(),s=/macintosh|mac os x/i.test(navigator.userAgent);return T.indexOf("win32")>=0||T.indexOf("wow32")>=0?"win32":T.indexOf("win64")>=0||T.indexOf("wow64")>=0?"win64":s?"mac":"unknown os"},isMobile:function(){var T=/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i;return!!((typeof navigator>"u"?"undefined":j(navigator))==="object"&&navigator.userAgent.match(T))},isAndroid:function(){var T=/(Android|Adr|Linux)/i;return!!((typeof navigator>"u"?"undefined":j(navigator))==="object"&&navigator.userAgent.match(T))},isIOS:function(){var T=/(iPhone|iPod|iPad|iOS)/i;return!!((typeof navigator>"u"?"undefined":j(navigator))==="object"&&navigator.userAgent.match(T))},isOtherMobile:function(){return S&&!isAndroid&&!isIOS},getUA:function(){if((typeof navigator>"u"?"undefined":j(navigator))!=="object")return"unknown device";var T=navigator.userAgent;return T}},S=d.isMobile(),f=d.isAndroid()?"android":d.isIOS?"ios":"other_mobile",y=d.getOsType(),x=S?f:y,b=d.getUA(),w=d.getProtocol(),U=function(T){return["putObject","sliceUploadFile","uploadFile","uploadFiles"].includes(T)?"UploadTask":T==="getObject"?"DownloadTask":["putObjectCopy","sliceCopyFile"].includes(T)?"CopyTask":T};function z(l){return l.replace(/([A-Z])/g,"_$1").toLowerCase()}function q(l){var T={},s=["sdkVersionName","sdkVersionCode","osName","networkType","requestName","requestResult","bucket","region","appid","accelerate","url","host","requestPath","userAgent","networkProtocol","httpMethod","httpSize","httpSpeed","httpTookTime","httpMd5","httpSign","httpFullTime","httpDomain","partNumber","httpRetryTimes","customId","traceId","realApi"],o=[].concat(s,["errorNode","errorCode","errorName","errorMessage","errorRequestId","errorHttpCode","errorServiceName","errorType","fullError"]),t=l.requestResult==="Success"?s:o;for(var r in l)if(t.includes(r)){var c=z(r);T[c]=l[r]}return T.request_name=l.realApi?U(l.realApi):l.requestName,T}var h=function(){function l(T){N(this,l);var s=T.parent,o=T.traceId,t=T.bucket,r=T.region,c=T.apiName,p=T.realApi,I=T.httpMethod,R=T.fileKey,K=T.fileSize,F=T.accelerate,J=T.customId,Q=T.delay,ee=T.deepTracker,te=T.Beacon,Ce=T.clsReporter,ae=t&&t.substr(t.lastIndexOf("-")+1)||"";this.parent=s,this.deepTracker=ee,this.delay=Q,Ce&&!this.clsReporter&&(this.clsReporter=Ce),this.params={sdkVersionName:"cos-js-sdk-v5",sdkVersionCode:M.version,osName:x,networkType:"",requestName:c||"",requestResult:"",realApi:p,bucket:t,region:r,accelerate:F,httpMethod:I,url:"",host:"",httpDomain:"",requestPath:R||"",userAgent:b,networkProtocol:w,errorType:"",errorCode:"",errorName:"",errorMessage:"",errorRequestId:"",errorHttpCode:0,errorServiceName:"",errorNode:"",httpTookTime:0,httpSize:K||0,httpMd5:0,httpSign:0,httpFullTime:0,httpSpeed:0,md5StartTime:0,md5EndTime:0,signStartTime:0,signEndTime:0,httpStartTime:0,httpEndTime:0,startTime:new Date().getTime(),endTime:0,traceId:o||d.getUid(),appid:ae,partNumber:0,httpRetryTimes:0,customId:J||"",partTime:0},te&&(this.beacon=i(te,Q))}return P(l,[{key:"formatResult",value:function(s,o){var t,r,c,p,I,R,K=new Date().getTime(),F=d.getNetType(),J=s?(s==null?void 0:s.code)||(s==null||(t=s.error)===null||t===void 0?void 0:t.code)||(s==null||(r=s.error)===null||r===void 0?void 0:r.Code):"",Q=s?(s==null?void 0:s.message)||(s==null||(c=s.error)===null||c===void 0?void 0:c.message)||(s==null||(p=s.error)===null||p===void 0?void 0:p.Message):"",ee=Q,te=s?(s==null?void 0:s.resource)||(s==null||(I=s.error)===null||I===void 0?void 0:I.resource)||(s==null||(R=s.error)===null||R===void 0?void 0:R.Resource):"",Ce=s?s==null?void 0:s.statusCode:o.statusCode,ae=s?(s==null?void 0:s.headers)&&(s==null?void 0:s.headers["x-cos-request-id"]):(o==null?void 0:o.headers)&&(o==null?void 0:o.headers["x-cos-request-id"]),ie=s?ae?"Server":"Client":"";this.params.requestName==="getObject"&&(this.params.httpSize=o?o.headers&&o.headers["content-length"]:0);var Z=this.params.realApi==="sliceUploadFile",D=this.params.realApi==="sliceCopyFile";if(Z||D){var Y=this.params.httpSize/1024/this.params.partTime;Object.assign(this.params,{httpSpeed:Y<0?0:Y.toFixed(3)})}else{var le=K-this.params.startTime,de=this.params.httpEndTime-this.params.httpStartTime,oe=this.params.httpSize/1024/(de/1e3),ue=this.params.md5EndTime-this.params.md5StartTime,he=this.params.signEndTime-this.params.signStartTime;this.parent&&(this.parent.addParamValue("httpTookTime",C(de)),this.parent.addParamValue("httpFullTime",C(le)),this.parent.addParamValue("httpMd5",C(ue)),this.parent.addParamValue("httpSign",C(he)),["multipartUpload","uploadPartCopy","putObjectCopy"].includes(this.params.requestName)&&this.parent.addParamValue("partTime",C(de))),Object.assign(this.params,{httpFullTime:C(le),httpMd5:C(ue),httpSign:C(he),httpTookTime:C(de),httpSpeed:oe<0?0:oe.toFixed(3)})}if(Object.assign(this.params,{networkType:F,requestResult:s?"Failure":"Success",errorType:ie,errorCode:J,errorHttpCode:Ce,errorName:ee,errorMessage:Q,errorServiceName:te,errorRequestId:ae}),s&&(!J||!Q)&&(this.params.fullError=s?JSON.stringify(s):""),this.params.url){try{var ge=/^http(s)?:\/\/(.*?)\//.exec(this.params.url);this.params.host=ge[2]}catch{this.params.host=this.params.url}this.params.httpDomain=this.params.host}}},{key:"report",value:function(s,o){if(!(!this.beacon&&!this.clsReporter)){this.formatResult(s,o);var t=q(this.params);this.beacon&&this.sendEventsToBeacon(t),this.clsReporter&&this.sendEventsToCLS(t)}}},{key:"setParams",value:function(s){Object.assign(this.params,s)}},{key:"addParamValue",value:function(s,o){this.params[s]=(+this.params[s]+ +o).toFixed(3)}},{key:"sendEventsToBeacon",value:function(s){var o=this.params.requestName==="sliceUploadFile"||this.params.realApi==="sliceUploadFile";if(!(o&&!this.deepTracker)){var t="qcloud_track_cos_sdk";this.delay===0?this.beacon&&this.beacon.onDirectUserAction(t,s):this.beacon&&this.beacon.onUserAction(t,s)}}},{key:"sendEventsToCLS",value:function(s){var o=this.delay===0;this.clsReporter.log(s,o)}},{key:"generateSubTracker",value:function(s){return Object.assign(s,{parent:this,deepTracker:this.deepTracker,traceId:this.params.traceId,bucket:this.params.bucket,region:this.params.region,accelerate:this.params.accelerate,fileKey:this.params.requestPath,customId:this.params.customId,delay:this.delay,clsReporter:this.clsReporter}),new l(s)}}])}();H.exports=h},"./src/util.js":function(H,ne,E){(function(N){var P=E("./node_modules/@babel/runtime/helpers/typeof.js");function j(V,m){var A=typeof Symbol<"u"&&V[Symbol.iterator]||V["@@iterator"];if(!A){if(Array.isArray(V)||(A=M(V))||m){A&&(V=A);var O=0,G=function(){};return{s:G,n:function(){return O>=V.length?{done:!0}:{done:!1,value:V[O++]}},e:function(me){throw me},f:G}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var $,re=!0,fe=!1;return{s:function(){A=A.call(V)},n:function(){var me=A.next();return re=me.done,me},e:function(me){fe=!0,$=me},f:function(){try{re||A.return==null||A.return()}finally{if(fe)throw $}}}}function M(V,m){if(V){if(typeof V=="string")return L(V,m);var A={}.toString.call(V).slice(8,-1);return A==="Object"&&V.constructor&&(A=V.constructor.name),A==="Map"||A==="Set"?Array.from(V):A==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(A)?L(V,m):void 0}}function L(V,m){(m==null||m>V.length)&&(m=V.length);for(var A=0,O=Array(m);A<m;A++)O[A]=V[A];return O}var i=E("./lib/md5.js"),C=E("./lib/crypto.js"),d=E("./node_modules/fast-xml-parser/src/fxp.js"),S=d.XMLParser,f=d.XMLBuilder,y=new S({ignoreDeclaration:!0,ignoreAttributes:!0,parseTagValue:!1,trimValues:!1}),x=new f,b=E("./lib/base64.js"),w=E("./src/tracker.js"),U="#text",z=function(m){if(le(m))for(var A in m){var O=m[A];typeof O=="string"?A===U&&delete m[A]:Array.isArray(O)?O.forEach(function(G){z(G)}):le(O)&&z(O)}},q=function(m){var A=y.parse(m);return z(A),A},h=function(m){var A=x.build(m);return A};function l(V){return encodeURIComponent(V).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}function T(V,m){var A=[];for(var O in V)V.hasOwnProperty(O)&&A.push(m?l(O).toLowerCase():O);return A.sort(function(G,$){return G=G.toLowerCase(),$=$.toLowerCase(),G===$?0:G>$?1:-1})}var s=function(m,A){var O,G,$,re=[],fe=T(m);for(O=0;O<fe.length;O++)G=fe[O],$=m[G]===void 0||m[G]===null?"":""+m[G],G=A?l(G).toLowerCase():l(G),$=l($)||"",re.push(G+"="+$);return re.join("&")},o=["cache-control","content-disposition","content-encoding","content-length","content-md5","expect","expires","host","if-match","if-modified-since","if-none-match","if-unmodified-since","origin","range","transfer-encoding","pic-operations"],t=function(m){var A={};for(var O in m){var G=O.toLowerCase();(G.indexOf("x-cos-")>-1||G.indexOf("x-ci-")>-1||o.indexOf(G)>-1)&&(A[O]=m[O])}return A},r=function(m){m=m||{};var A=m.SecretId,O=m.SecretKey,G=m.KeyTime,$=(m.method||m.Method||"get").toLowerCase(),re=ie(m.Query||m.params||{}),fe=t(ie(m.Headers||m.headers||{})),ye=m.Key||"",me;m.UseRawKey?me=m.Pathname||m.pathname||"/"+ye:(me=m.Pathname||m.pathname||ye,me.indexOf("/")!==0&&(me="/"+me));var Ae=m.ForceSignHost!==!1;if(!fe.Host&&!fe.host&&m.Bucket&&m.Region&&Ae&&(fe.Host=m.Bucket+".cos."+m.Region+".myqcloud.com"),!A)throw new Error("missing param SecretId");if(!O)throw new Error("missing param SecretKey");var Ie=Math.round(Ze(m.SystemClockOffset)/1e3)-1,Pe=Ie,He=m.Expires||m.expires;He===void 0?Pe+=900:Pe+=He*1||0;var Ye="sha1",Ve=A,xe=G||Ie+";"+Pe,De=G||Ie+";"+Pe,ze=T(fe,!0).join(";").toLowerCase(),nt=T(re,!0).join(";").toLowerCase(),it=C.HmacSHA1(De,O).toString(),pt=[$,me,je.obj2str(re,!0),je.obj2str(fe,!0),""].join(`
`),yt=["sha1",xe,C.SHA1(pt).toString(),""].join(`
`),X=C.HmacSHA1(yt,it).toString(),Ct=["q-sign-algorithm="+Ye,"q-ak="+Ve,"q-sign-time="+xe,"q-key-time="+De,"q-header-list="+ze,"q-url-param-list="+nt,"q-signature="+X].join("&");return Ct},c=function(m,A,O){var G=A/8,$=m.slice(O,O+G);return new Uint8Array($).reverse(),new{8:Uint8Array,16:Uint16Array,32:Uint32Array}[A]($)[0]},p=function(m,A,O,G){var $=m.slice(A,O),re="";return new Uint8Array($).forEach(function(fe){re+=String.fromCharCode(fe)}),G&&(re=decodeURIComponent(escape(re))),re},I=function(m){for(var A={},O=p(m),G={records:[]};m.byteLength;){var $=c(m,32,0),re=c(m,32,4),fe=$-re-16,ye=0,me;for(m=m.slice(12);ye<re;){var Ae=c(m,8,ye),Ie=p(m,ye+1,ye+1+Ae),Pe=c(m,16,ye+Ae+2),He=p(m,ye+Ae+4,ye+Ae+4+Pe);A[Ie]=He,ye+=Ae+4+Pe}if(A[":event-type"]==="Records")me=p(m,ye,ye+fe,!0),G.records.push(me);else if(A[":event-type"]==="Stats")me=p(m,ye,ye+fe,!0),G.stats=je.xml2json(me).Stats;else if(A[":event-type"]==="error"){var Ye=A[":error-code"],Ve=A[":error-message"],xe=new Error(Ve);xe.message=Ve,xe.name=xe.code=Ye,G.error=xe}m=m.slice(ye+fe+4)}return{payload:G.records.join(""),body:O}},R=function(m){var A=this.options.CopySourceParser;if(A)return A(m);var O=m.match(/^([^.]+-\d+)\.cos(v6|-cdc|-cdz|-internal)?\.([^.]+)\.((myqcloud\.com)|(tencentcos\.cn))\/(.+)$/);return O?{Bucket:O[1],Region:O[3],Key:O[7]}:null},K=function(){},F=function(m){var A={};for(var O in m)m.hasOwnProperty(O)&&m[O]!==void 0&&m[O]!==null&&(A[O]=m[O]);return A},J=function(m,A){var O,G=new FileReader;FileReader.prototype.readAsBinaryString?(O=FileReader.prototype.readAsBinaryString,G.onload=function(){A(this.result)}):FileReader.prototype.readAsArrayBuffer?O=function(re){var fe="",ye=new FileReader;ye.onload=function(me){for(var Ae=new Uint8Array(ye.result),Ie=Ae.byteLength,Pe=0;Pe<Ie;Pe++)fe+=String.fromCharCode(Ae[Pe]);A(fe)},ye.readAsArrayBuffer(re)}:console.error("FileReader not support readAsBinaryString"),O.call(G,m)},Q=function(){var V=function(O,G){O=O.split("."),G=G.split(".");for(var $=0;$<G.length;$++)if(O[$]!==G[$])return parseInt(O[$])>parseInt(G[$])?1:-1;return 0},m=function(O){if(!O)return!1;var G=(O.match(/Chrome\/([.\d]+)/)||[])[1],$=(O.match(/QBCore\/([.\d]+)/)||[])[1],re=(O.match(/QQBrowser\/([.\d]+)/)||[])[1],fe=G&&V(G,"53.0.2785.116")<0&&$&&V($,"3.53.991.400")<0&&re&&V(re,"9.0.2524.400")<=0||!1;return fe};return m(typeof navigator<"u"&&navigator.userAgent)}(),ee=function(m,A,O,G,$){var re;if(m.slice?re=m.slice(A,O):m.mozSlice?re=m.mozSlice(A,O):m.webkitSlice&&(re=m.webkitSlice(A,O)),G&&Q){var fe=new FileReader;fe.onload=function(ye){re=null,$(new Blob([fe.result]))},fe.readAsArrayBuffer(re)}else $(re)},te=function(m,A,O,G){O=O||K,m?typeof A=="string"?O(je.md5(A,!0)):Blob&&A instanceof Blob?je.getFileMd5(A,function($,re){O(re)},G):O():O()},Ce=1024*1024,ae=function(m,A,O){var G=m.size,$=0,re=i.getCtx(),fe=function(me){if(me>=G){var Ae=re.digest("hex");A(null,Ae);return}var Ie=Math.min(G,me+Ce);je.fileSlice(m,me,Ie,!1,function(Pe){J(Pe,function(He){Pe=null,re=re.update(He,!0),$+=He.length,He=null,O&&O({loaded:$,total:G,percent:Math.round($/G*1e4)/1e4}),fe(me+Ce)})})};fe(0)};function ie(V){return he(V,function(m){return P(m)==="object"&&m!==null?ie(m):m})}function Z(V,m,A){return V&&m in V?V[m]:A}function D(V,m){return ue(m,function(A,O){V[O]=m[O]}),V}function Y(V){return V instanceof Array}function le(V){return Object.prototype.toString.call(V)==="[object Object]"}function de(V,m){for(var A=!1,O=0;O<V.length;O++)if(m===V[O]){A=!0;break}return A}function oe(V){return Y(V)?V:[V]}function ue(V,m){for(var A in V)V.hasOwnProperty(A)&&m(V[A],A)}function he(V,m){var A=Y(V)?[]:{};for(var O in V)V.hasOwnProperty(O)&&(A[O]=m(V[O],O));return A}function ge(V,m){var A=Y(V),O=A?[]:{};for(var G in V)V.hasOwnProperty(G)&&m(V[G],G)&&(A?O.push(V[G]):O[G]=V[G]);return O}var pe=function(m){var A,O,G,$="";for(A=0,O=m.length/2;A<O;A++)G=parseInt(m[A*2]+m[A*2+1],16),$+=String.fromCharCode(G);return btoa($)},Be=function(){var m=function(){return((1+Math.random())*65536|0).toString(16).substring(1)};return m()+m()+"-"+m()+"-"+m()+"-"+m()+"-"+m()+m()+m()},ke=function(m,A){var O=A.Bucket,G=A.Region,$=A.Key,re=this.options.Domain,fe=!re||typeof re=="string"&&re.indexOf("{Bucket}")>-1,ye=!re||typeof re=="string"&&re.indexOf("{Region}")>-1;if(m.indexOf("Bucket")>-1||m==="deleteMultipleObject"||m==="multipartList"||m==="listObjectVersions"){if(fe&&!O)return"Bucket";if(ye&&!G)return"Region"}else if(m.indexOf("Object")>-1||m.indexOf("multipart")>-1||m==="sliceUploadFile"||m==="abortUploadTask"||m==="uploadFile"){if(fe&&!O)return"Bucket";if(ye&&!G)return"Region";if(!$)return"Key"}return!1},Oe=function(m,A){if(A=D({},A),m!=="getAuth"&&m!=="getV4Auth"&&m!=="getObjectUrl"){var O=A.Headers||{};if(A&&P(A)==="object"){(function(){for(var $ in A)A.hasOwnProperty($)&&$.indexOf("x-cos-")>-1&&(O[$]=A[$])})();var G={"x-cos-mfa":"MFA","Content-MD5":"ContentMD5","Content-Length":"ContentLength","Content-Type":"ContentType",Expect:"Expect",Expires:"Expires","Cache-Control":"CacheControl","Content-Disposition":"ContentDisposition","Content-Encoding":"ContentEncoding",Range:"Range","If-Modified-Since":"IfModifiedSince","If-Unmodified-Since":"IfUnmodifiedSince","If-Match":"IfMatch","If-None-Match":"IfNoneMatch","x-cos-copy-source":"CopySource","x-cos-copy-source-Range":"CopySourceRange","x-cos-metadata-directive":"MetadataDirective","x-cos-copy-source-If-Modified-Since":"CopySourceIfModifiedSince","x-cos-copy-source-If-Unmodified-Since":"CopySourceIfUnmodifiedSince","x-cos-copy-source-If-Match":"CopySourceIfMatch","x-cos-copy-source-If-None-Match":"CopySourceIfNoneMatch","x-cos-acl":"ACL","x-cos-grant-read":"GrantRead","x-cos-grant-write":"GrantWrite","x-cos-grant-full-control":"GrantFullControl","x-cos-grant-read-acp":"GrantReadAcp","x-cos-grant-write-acp":"GrantWriteAcp","x-cos-storage-class":"StorageClass","x-cos-traffic-limit":"TrafficLimit","x-cos-mime-limit":"MimeLimit","x-cos-server-side-encryption-customer-algorithm":"SSECustomerAlgorithm","x-cos-server-side-encryption-customer-key":"SSECustomerKey","x-cos-server-side-encryption-customer-key-MD5":"SSECustomerKeyMD5","x-cos-server-side-encryption":"ServerSideEncryption","x-cos-server-side-encryption-cos-kms-key-id":"SSEKMSKeyId","x-cos-server-side-encryption-context":"SSEContext","Pic-Operations":"PicOperations","x-cos-callback":"Callback","x-cos-callback-var":"CallbackVar","x-cos-return-body":"ReturnBody"};je.each(G,function($,re){A[$]!==void 0&&(O[re]=A[$])}),A.Headers=F(O)}}return A},Ge=function(m,A){return function(O,G){var $=this;typeof O=="function"&&(G=O,O={}),O=Oe(m,O);var re;if($.options.EnableReporter)if(O.calledBySdk==="sliceUploadFile"||O.calledBySdk==="sliceCopyFile")re=O.tracker&&O.tracker.generateSubTracker({apiName:m});else if(["uploadFile","uploadFiles"].includes(m))re=null;else{var fe=0;O.Body&&(fe=typeof O.Body=="string"?O.Body.length:O.Body.size||O.Body.byteLength||0);var ye=$.options.UseAccelerate||typeof $.options.Domain=="string"&&$.options.Domain.includes("accelerate.");re=new w({Beacon:$.options.BeaconReporter,clsReporter:$.options.ClsReporter,bucket:O.Bucket,region:O.Region,apiName:m,realApi:m,accelerate:ye,fileKey:O.Key,fileSize:fe,deepTracker:$.options.DeepTracker,customId:$.options.CustomId,delay:$.options.TrackerDelay})}O.tracker=re;var me=function(xe){return xe&&xe.headers&&(xe.headers["x-ci-request-id"]&&(xe.RequestId=xe.headers["x-ci-request-id"]),xe.headers["x-cos-request-id"]&&(xe.RequestId=xe.headers["x-cos-request-id"]),xe.headers["x-cos-version-id"]&&(xe.VersionId=xe.headers["x-cos-version-id"]),xe.headers["x-cos-delete-marker"]&&(xe.DeleteMarker=xe.headers["x-cos-delete-marker"])),xe},Ae=function(xe,De){re&&re.report(xe,De),G&&G(me(xe),me(De))},Ie=function(){if(m!=="getService"&&m!=="abortUploadTask"){var xe=ke.call($,m,O);if(xe)return"missing param "+xe;if(O.Region){if($.options.CompatibilityMode){if(!/^([a-z\d-.]+)$/.test(O.Region))return"Region format error."}else{if(O.Region.indexOf("cos.")>-1)return'param Region should not be start with "cos."';if(!/^([a-z\d-]+)$/.test(O.Region))return"Region format error."}!$.options.CompatibilityMode&&O.Region.indexOf("-")===-1&&O.Region!=="yfb"&&O.Region!=="default"&&O.Region!=="accelerate"&&console.warn("warning: param Region format error, find help here: https://cloud.tencent.com/document/product/436/6224")}if(O.Bucket){if(!/^([a-z\d-]+)-(\d+)$/.test(O.Bucket))if(O.AppId)O.Bucket=O.Bucket+"-"+O.AppId;else if($.options.AppId)O.Bucket=O.Bucket+"-"+$.options.AppId;else return'Bucket should format as "test-1250000000".';O.AppId&&(console.warn('warning: AppId has been deprecated, Please put it at the end of parameter Bucket(E.g Bucket:"test-1250000000" ).'),delete O.AppId)}!$.options.UseRawKey&&O.Key&&O.Key.substr(0,1)==="/"&&(O.Key=O.Key.substr(1))}},Pe=Ie(),He=["getAuth","getObjectUrl"].includes(m);if(typeof Promise=="function"&&!He&&!G)return new Promise(function(Ve,xe){if(G=function(ze,nt){ze?xe(ze):Ve(nt)},Pe)return Ae(je.error(new Error(Pe)));A.call($,O,Ae)});if(Pe)return Ae(je.error(new Error(Pe)));var Ye=A.call($,O,Ae);if(He)return Ye}},Fe=function(m,A){var O=this,G=0,$=0,re=Date.now(),fe,ye;function me(){if(ye=0,A&&typeof A=="function"){fe=Date.now();var Ae=Math.max(0,Math.round(($-G)/((fe-re)/1e3)*100)/100)||0,Ie;$===0&&m===0?Ie=1:Ie=Math.floor($/m*100)/100||0,re=fe,G=$;try{A({loaded:$,total:m,speed:Ae,percent:Ie})}catch{}}}return function(Ae,Ie){if(Ae&&($=Ae.loaded,m=Ae.total),Ie)clearTimeout(ye),me();else{if(ye)return;ye=setTimeout(me,O.options.ProgressInterval)}}},We=function(m,A,O){var G;if(typeof A.Body=="string"?A.Body=new Blob([A.Body],{type:"text/plain"}):A.Body instanceof ArrayBuffer&&(A.Body=new Blob([A.Body])),A.Body&&(A.Body instanceof Blob||A.Body.toString()==="[object File]"||A.Body.toString()==="[object Blob]"))G=A.Body.size;else{O(je.error(new Error("params body format error, Only allow File|Blob|String.")));return}A.ContentLength=G,O(null,G)},Ze=function(m){return Date.now()+(m||0)},_e=function(m,A){var O=m;return m.message=m.message||null,typeof A=="string"?(m.error=A,m.message=A):P(A)==="object"&&A!==null&&(D(m,A),(A.code||A.name)&&(m.code=A.code||A.name),A.message&&(m.message=A.message),A.stack&&(m.stack=A.stack)),typeof Object.defineProperty=="function"&&(Object.defineProperty(m,"name",{writable:!0,enumerable:!1}),Object.defineProperty(m,"message",{enumerable:!0})),m.name=A&&A.name||m.name||m.code||"Error",m.code||(m.code=m.name),m.error||(m.error=ie(O)),m},$e=function(){return(typeof globalThis>"u"?"undefined":P(globalThis))==="object"&&(globalThis.constructor.name==="DedicatedWorkerGlobalScope"||globalThis.FileReaderSync)},rt=function(){return(typeof window>"u"?"undefined":P(window))!=="object"&&(typeof N>"u"?"undefined":P(N))==="object"&&!0&&!$e()},at=function(m){return/^https?:\/\/([^/]+\.)?ci\.[^/]+/.test(m)},lt=function(){if((typeof navigator>"u"?"undefined":P(navigator))!=="object")return!1;var V=navigator.userAgent,m=!!V.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);return m}(),dt=function(){return(typeof navigator>"u"?"undefined":P(navigator))!=="object"?!1:/\sQQ/i.test(navigator.userAgent)}(),ft=function(m,A){var O=b.encode(m);return A&&(O=O.replaceAll("+","-").replaceAll("/","_").replaceAll("=","")),O},ht=function(m){return m?b.decode(m):""},gt=function(m){var A=m.split("/"),O=[],G=j(A),$;try{for(G.s();!($=G.n()).done;){var re=$.value;re===".."?O.length&&O.pop():re.length&&re!=="."&&O.push(re)}}catch(fe){G.e(fe)}finally{G.f()}return"/"+O.join("/")},vt=function(m){var A;if(m&&typeof m=="string"){var O=m.trim(),G=O.indexOf("<")===0,$=O.indexOf("{")===0;if(G)A=je.xml2json(m)||{};else if($)try{var re=m.replace(/\n/g," "),fe=JSON.parse(re);Object.prototype.toString.call(fe)==="[object Object]"?A=fe:A=m}catch{A=m}else A=m}else A=m||{};return A},je={noop:K,formatParams:Oe,apiWrapper:Ge,xml2json:q,json2xml:h,md5:i,clearKey:F,fileSlice:ee,getBodyMd5:te,getFileMd5:ae,b64:pe,extend:D,isArray:Y,isInArray:de,makeArray:oe,each:ue,map:he,filter:ge,clone:ie,attr:Z,uuid:Be,camSafeUrlEncode:l,throttleOnProgress:Fe,getFileSize:We,getSkewTime:Ze,error:_e,obj2str:s,getAuth:r,parseSelectPayload:I,getSourceParams:R,isBrowser:!0,isNode:rt,isCIHost:at,isIOS_QQ:lt&&dt,encodeBase64:ft,decodeBase64:ht,simplifyPath:gt,readAsBinaryString:J,parseResBody:vt};H.exports=je}).call(this,E("./node_modules/process/browser.js"))}})})})(At);var Ft=At.exports;const Et=It(Ft);function _t(Ke){let Xe=null;const H=Ke.secretId,ne=Ke.secretKey,E=Ke.getAuthorization;return!H&&!ne&&E?Xe=new Et({getAuthorization(N,P){E(Ke).then(j=>{j.ExpiredTime&&typeof j.ExpiredTime=="string"&&(j.ExpiredTime=Nt(j.ExpiredTime).unix()),P(j)})}}):(console.warn("您还未配置getAuthorization，将使用SecretKey授权进行上传"),Xe=new Et({SecretId:H,SecretKey:ne})),Xe}async function Ht({file:Ke,fileName:Xe,onProgress:H,options:ne}){const E=await Kt(Ke,Xe,ne),N=ne,P=_t(N);return new Promise((j,M)=>{P.putObject({Bucket:N.bucket,Region:N.region,Key:E,Body:Ke,onProgress(L){const i=L;i.total>0&&(i.percent=Math.floor(i.loaded/i.total*100)),H(i)}},async function(L,i){if(L!=null){console.error(L),M(L);return}let C={url:N.domain+"/"+E,key:E};if(N.successHandle){C=await N.successHandle(C),j(C);return}j(C)})})}async function qt(Ke){const{getConfig:Xe}=jt(),H=Xe("cos"),ne=Ke.options,E=Lt(Mt(H),ne);return Ke.options=E,await Ht(Ke)}export{_t as getOssClient,qt as upload};
