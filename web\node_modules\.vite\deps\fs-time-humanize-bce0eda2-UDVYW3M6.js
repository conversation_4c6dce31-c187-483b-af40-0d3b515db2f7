import {
  e
} from "./chunk-KVKFRDRF.js";
import {
  he
} from "./chunk-CP75YXJV.js";
import "./chunk-7AOKHL6I.js";
import "./chunk-TJTCSOX4.js";
import "./chunk-3XL3ODE2.js";
import "./chunk-YFT6OQ5R.js";
import "./chunk-TGOZU523.js";
import {
  require_dayjs_min
} from "./chunk-IUY2MIZJ.js";
import {
  merge_default
} from "./chunk-LK7GAOJV.js";
import "./chunk-44FYVRJW.js";
import {
  computed,
  createElementBlock,
  defineComponent,
  openBlock,
  toDisplayString
} from "./chunk-WEJJSMSC.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@fast-crud/fast-extends/dist/fs-time-humanize-bce0eda2.mjs
var import_dayjs = __toESM(require_dayjs_min(), 1);
var N = { exports: {} };
(function(s) {
  (function() {
    var y = Object.assign || /** @param {...any} destination */
    function(n) {
      for (var t, e2 = 1; e2 < arguments.length; e2++) {
        t = arguments[e2];
        for (var u in t)
          v(t, u) && (n[u] = t[u]);
      }
      return n;
    }, D = Array.isArray || function(n) {
      return Object.prototype.toString.call(n) === "[object Array]";
    }, j = r(
      function(n) {
        return n === 1 ? "χρόνος" : "χρόνια";
      },
      function(n) {
        return n === 1 ? "μήνας" : "μήνες";
      },
      function(n) {
        return n === 1 ? "εβδομάδα" : "εβδομάδες";
      },
      function(n) {
        return n === 1 ? "μέρα" : "μέρες";
      },
      function(n) {
        return n === 1 ? "ώρα" : "ώρες";
      },
      function(n) {
        return n === 1 ? "λεπτό" : "λεπτά";
      },
      function(n) {
        return n === 1 ? "δευτερόλεπτο" : "δευτερόλεπτα";
      },
      function(n) {
        return (n === 1 ? "χιλιοστό" : "χιλιοστά") + " του δευτερολέπτου";
      },
      ","
    ), k = {
      af: r(
        "jaar",
        function(n) {
          return "maand" + (n === 1 ? "" : "e");
        },
        function(n) {
          return n === 1 ? "week" : "weke";
        },
        function(n) {
          return n === 1 ? "dag" : "dae";
        },
        function(n) {
          return n === 1 ? "uur" : "ure";
        },
        function(n) {
          return n === 1 ? "minuut" : "minute";
        },
        function(n) {
          return "sekonde" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "millisekonde" + (n === 1 ? "" : "s");
        },
        ","
      ),
      am: r("ዓመት", "ወር", "ሳምንት", "ቀን", "ሰዓት", "ደቂቃ", "ሰከንድ", "ሚሊሰከንድ"),
      ar: y(
        r(
          function(n) {
            return ["سنة", "سنتان", "سنوات"][g(n)];
          },
          function(n) {
            return ["شهر", "شهران", "أشهر"][g(n)];
          },
          function(n) {
            return ["أسبوع", "أسبوعين", "أسابيع"][g(n)];
          },
          function(n) {
            return ["يوم", "يومين", "أيام"][g(n)];
          },
          function(n) {
            return ["ساعة", "ساعتين", "ساعات"][g(n)];
          },
          function(n) {
            return ["دقيقة", "دقيقتان", "دقائق"][g(n)];
          },
          function(n) {
            return ["ثانية", "ثانيتان", "ثواني"][g(n)];
          },
          function(n) {
            return ["جزء من الثانية", "جزآن من الثانية", "أجزاء من الثانية"][g(n)];
          },
          ","
        ),
        {
          delimiter: " ﻭ ",
          _hideCountIf2: true,
          _digitReplacements: ["۰", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"]
        }
      ),
      bg: r(
        function(n) {
          return ["години", "година", "години"][i(n)];
        },
        function(n) {
          return ["месеца", "месец", "месеца"][i(n)];
        },
        function(n) {
          return ["седмици", "седмица", "седмици"][i(n)];
        },
        function(n) {
          return ["дни", "ден", "дни"][i(n)];
        },
        function(n) {
          return ["часа", "час", "часа"][i(n)];
        },
        function(n) {
          return ["минути", "минута", "минути"][i(n)];
        },
        function(n) {
          return ["секунди", "секунда", "секунди"][i(n)];
        },
        function(n) {
          return ["милисекунди", "милисекунда", "милисекунди"][i(n)];
        },
        ","
      ),
      bn: r(
        "বছর",
        "মাস",
        "সপ্তাহ",
        "দিন",
        "ঘন্টা",
        "মিনিট",
        "সেকেন্ড",
        "মিলিসেকেন্ড"
      ),
      ca: r(
        function(n) {
          return "any" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "mes" + (n === 1 ? "" : "os");
        },
        function(n) {
          return "setman" + (n === 1 ? "a" : "es");
        },
        function(n) {
          return "di" + (n === 1 ? "a" : "es");
        },
        function(n) {
          return "hor" + (n === 1 ? "a" : "es");
        },
        function(n) {
          return "minut" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "segon" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "milisegon" + (n === 1 ? "" : "s");
        },
        ","
      ),
      ckb: r(
        "ساڵ",
        "مانگ",
        "هەفتە",
        "ڕۆژ",
        "کاژێر",
        "خولەک",
        "چرکە",
        "میلی چرکە",
        "."
      ),
      cs: r(
        function(n) {
          return ["rok", "roku", "roky", "let"][l(n)];
        },
        function(n) {
          return ["měsíc", "měsíce", "měsíce", "měsíců"][l(n)];
        },
        function(n) {
          return ["týden", "týdne", "týdny", "týdnů"][l(n)];
        },
        function(n) {
          return ["den", "dne", "dny", "dní"][l(n)];
        },
        function(n) {
          return ["hodina", "hodiny", "hodiny", "hodin"][l(n)];
        },
        function(n) {
          return ["minuta", "minuty", "minuty", "minut"][l(n)];
        },
        function(n) {
          return ["sekunda", "sekundy", "sekundy", "sekund"][l(n)];
        },
        function(n) {
          return ["milisekunda", "milisekundy", "milisekundy", "milisekund"][l(n)];
        },
        ","
      ),
      cy: r(
        "flwyddyn",
        "mis",
        "wythnos",
        "diwrnod",
        "awr",
        "munud",
        "eiliad",
        "milieiliad"
      ),
      da: r(
        "år",
        function(n) {
          return "måned" + (n === 1 ? "" : "er");
        },
        function(n) {
          return "uge" + (n === 1 ? "" : "r");
        },
        function(n) {
          return "dag" + (n === 1 ? "" : "e");
        },
        function(n) {
          return "time" + (n === 1 ? "" : "r");
        },
        function(n) {
          return "minut" + (n === 1 ? "" : "ter");
        },
        function(n) {
          return "sekund" + (n === 1 ? "" : "er");
        },
        function(n) {
          return "millisekund" + (n === 1 ? "" : "er");
        },
        ","
      ),
      de: r(
        function(n) {
          return "Jahr" + (n === 1 ? "" : "e");
        },
        function(n) {
          return "Monat" + (n === 1 ? "" : "e");
        },
        function(n) {
          return "Woche" + (n === 1 ? "" : "n");
        },
        function(n) {
          return "Tag" + (n === 1 ? "" : "e");
        },
        function(n) {
          return "Stunde" + (n === 1 ? "" : "n");
        },
        function(n) {
          return "Minute" + (n === 1 ? "" : "n");
        },
        function(n) {
          return "Sekunde" + (n === 1 ? "" : "n");
        },
        function(n) {
          return "Millisekunde" + (n === 1 ? "" : "n");
        },
        ","
      ),
      el: j,
      en: r(
        function(n) {
          return "year" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "month" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "week" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "day" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "hour" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "minute" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "second" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "millisecond" + (n === 1 ? "" : "s");
        }
      ),
      eo: r(
        function(n) {
          return "jaro" + (n === 1 ? "" : "j");
        },
        function(n) {
          return "monato" + (n === 1 ? "" : "j");
        },
        function(n) {
          return "semajno" + (n === 1 ? "" : "j");
        },
        function(n) {
          return "tago" + (n === 1 ? "" : "j");
        },
        function(n) {
          return "horo" + (n === 1 ? "" : "j");
        },
        function(n) {
          return "minuto" + (n === 1 ? "" : "j");
        },
        function(n) {
          return "sekundo" + (n === 1 ? "" : "j");
        },
        function(n) {
          return "milisekundo" + (n === 1 ? "" : "j");
        },
        ","
      ),
      es: r(
        function(n) {
          return "año" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "mes" + (n === 1 ? "" : "es");
        },
        function(n) {
          return "semana" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "día" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "hora" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "minuto" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "segundo" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "milisegundo" + (n === 1 ? "" : "s");
        },
        ","
      ),
      et: r(
        function(n) {
          return "aasta" + (n === 1 ? "" : "t");
        },
        function(n) {
          return "kuu" + (n === 1 ? "" : "d");
        },
        function(n) {
          return "nädal" + (n === 1 ? "" : "at");
        },
        function(n) {
          return "päev" + (n === 1 ? "" : "a");
        },
        function(n) {
          return "tund" + (n === 1 ? "" : "i");
        },
        function(n) {
          return "minut" + (n === 1 ? "" : "it");
        },
        function(n) {
          return "sekund" + (n === 1 ? "" : "it");
        },
        function(n) {
          return "millisekund" + (n === 1 ? "" : "it");
        },
        ","
      ),
      eu: r(
        "urte",
        "hilabete",
        "aste",
        "egun",
        "ordu",
        "minutu",
        "segundo",
        "milisegundo",
        ","
      ),
      fa: r(
        "سال",
        "ماه",
        "هفته",
        "روز",
        "ساعت",
        "دقیقه",
        "ثانیه",
        "میلی ثانیه"
      ),
      fi: r(
        function(n) {
          return n === 1 ? "vuosi" : "vuotta";
        },
        function(n) {
          return n === 1 ? "kuukausi" : "kuukautta";
        },
        function(n) {
          return "viikko" + (n === 1 ? "" : "a");
        },
        function(n) {
          return "päivä" + (n === 1 ? "" : "ä");
        },
        function(n) {
          return "tunti" + (n === 1 ? "" : "a");
        },
        function(n) {
          return "minuutti" + (n === 1 ? "" : "a");
        },
        function(n) {
          return "sekunti" + (n === 1 ? "" : "a");
        },
        function(n) {
          return "millisekunti" + (n === 1 ? "" : "a");
        },
        ","
      ),
      fo: r(
        "ár",
        function(n) {
          return n === 1 ? "mánaður" : "mánaðir";
        },
        function(n) {
          return n === 1 ? "vika" : "vikur";
        },
        function(n) {
          return n === 1 ? "dagur" : "dagar";
        },
        function(n) {
          return n === 1 ? "tími" : "tímar";
        },
        function(n) {
          return n === 1 ? "minuttur" : "minuttir";
        },
        "sekund",
        "millisekund",
        ","
      ),
      fr: r(
        function(n) {
          return "an" + (n >= 2 ? "s" : "");
        },
        "mois",
        function(n) {
          return "semaine" + (n >= 2 ? "s" : "");
        },
        function(n) {
          return "jour" + (n >= 2 ? "s" : "");
        },
        function(n) {
          return "heure" + (n >= 2 ? "s" : "");
        },
        function(n) {
          return "minute" + (n >= 2 ? "s" : "");
        },
        function(n) {
          return "seconde" + (n >= 2 ? "s" : "");
        },
        function(n) {
          return "milliseconde" + (n >= 2 ? "s" : "");
        },
        ","
      ),
      gr: j,
      he: r(
        function(n) {
          return n === 1 ? "שנה" : "שנים";
        },
        function(n) {
          return n === 1 ? "חודש" : "חודשים";
        },
        function(n) {
          return n === 1 ? "שבוע" : "שבועות";
        },
        function(n) {
          return n === 1 ? "יום" : "ימים";
        },
        function(n) {
          return n === 1 ? "שעה" : "שעות";
        },
        function(n) {
          return n === 1 ? "דקה" : "דקות";
        },
        function(n) {
          return n === 1 ? "שניה" : "שניות";
        },
        function(n) {
          return n === 1 ? "מילישנייה" : "מילישניות";
        }
      ),
      hr: r(
        function(n) {
          return n % 10 === 2 || n % 10 === 3 || n % 10 === 4 ? "godine" : "godina";
        },
        function(n) {
          return n === 1 ? "mjesec" : n === 2 || n === 3 || n === 4 ? "mjeseca" : "mjeseci";
        },
        function(n) {
          return n % 10 === 1 && n !== 11 ? "tjedan" : "tjedna";
        },
        function(n) {
          return n === 1 ? "dan" : "dana";
        },
        function(n) {
          return n === 1 ? "sat" : n === 2 || n === 3 || n === 4 ? "sata" : "sati";
        },
        function(n) {
          var t = n % 10;
          return (t === 2 || t === 3 || t === 4) && (n < 10 || n > 14) ? "minute" : "minuta";
        },
        function(n) {
          var t = n % 10;
          return t === 5 || Math.floor(n) === n && n >= 10 && n <= 19 ? "sekundi" : t === 1 ? "sekunda" : t === 2 || t === 3 || t === 4 ? "sekunde" : "sekundi";
        },
        function(n) {
          return n === 1 ? "milisekunda" : n % 10 === 2 || n % 10 === 3 || n % 10 === 4 ? "milisekunde" : "milisekundi";
        },
        ","
      ),
      hi: r(
        "साल",
        function(n) {
          return n === 1 ? "महीना" : "महीने";
        },
        function(n) {
          return n === 1 ? "हफ़्ता" : "हफ्ते";
        },
        "दिन",
        function(n) {
          return n === 1 ? "घंटा" : "घंटे";
        },
        "मिनट",
        "सेकंड",
        "मिलीसेकंड"
      ),
      hu: r(
        "év",
        "hónap",
        "hét",
        "nap",
        "óra",
        "perc",
        "másodperc",
        "ezredmásodperc",
        ","
      ),
      id: r(
        "tahun",
        "bulan",
        "minggu",
        "hari",
        "jam",
        "menit",
        "detik",
        "milidetik"
      ),
      is: r(
        "ár",
        function(n) {
          return "mánuð" + (n === 1 ? "ur" : "ir");
        },
        function(n) {
          return "vik" + (n === 1 ? "a" : "ur");
        },
        function(n) {
          return "dag" + (n === 1 ? "ur" : "ar");
        },
        function(n) {
          return "klukkutím" + (n === 1 ? "i" : "ar");
        },
        function(n) {
          return "mínút" + (n === 1 ? "a" : "ur");
        },
        function(n) {
          return "sekúnd" + (n === 1 ? "a" : "ur");
        },
        function(n) {
          return "millisekúnd" + (n === 1 ? "a" : "ur");
        }
      ),
      it: r(
        function(n) {
          return "ann" + (n === 1 ? "o" : "i");
        },
        function(n) {
          return "mes" + (n === 1 ? "e" : "i");
        },
        function(n) {
          return "settiman" + (n === 1 ? "a" : "e");
        },
        function(n) {
          return "giorn" + (n === 1 ? "o" : "i");
        },
        function(n) {
          return "or" + (n === 1 ? "a" : "e");
        },
        function(n) {
          return "minut" + (n === 1 ? "o" : "i");
        },
        function(n) {
          return "second" + (n === 1 ? "o" : "i");
        },
        function(n) {
          return "millisecond" + (n === 1 ? "o" : "i");
        },
        ","
      ),
      ja: r("年", "ヶ月", "週", "日", "時間", "分", "秒", "ミリ秒"),
      km: r(
        "ឆ្នាំ",
        "ខែ",
        "សប្តាហ៍",
        "ថ្ងៃ",
        "ម៉ោង",
        "នាទី",
        "វិនាទី",
        "មិល្លីវិនាទី"
      ),
      kn: r(
        function(n) {
          return n === 1 ? "ವರ್ಷ" : "ವರ್ಷಗಳು";
        },
        function(n) {
          return n === 1 ? "ತಿಂಗಳು" : "ತಿಂಗಳುಗಳು";
        },
        function(n) {
          return n === 1 ? "ವಾರ" : "ವಾರಗಳು";
        },
        function(n) {
          return n === 1 ? "ದಿನ" : "ದಿನಗಳು";
        },
        function(n) {
          return n === 1 ? "ಗಂಟೆ" : "ಗಂಟೆಗಳು";
        },
        function(n) {
          return n === 1 ? "ನಿಮಿಷ" : "ನಿಮಿಷಗಳು";
        },
        function(n) {
          return n === 1 ? "ಸೆಕೆಂಡ್" : "ಸೆಕೆಂಡುಗಳು";
        },
        function(n) {
          return n === 1 ? "ಮಿಲಿಸೆಕೆಂಡ್" : "ಮಿಲಿಸೆಕೆಂಡುಗಳು";
        }
      ),
      ko: r("년", "개월", "주일", "일", "시간", "분", "초", "밀리 초"),
      ku: r(
        "sal",
        "meh",
        "hefte",
        "roj",
        "seet",
        "deqe",
        "saniye",
        "mîlîçirk",
        ","
      ),
      lo: r(
        "ປີ",
        "ເດືອນ",
        "ອາທິດ",
        "ມື້",
        "ຊົ່ວໂມງ",
        "ນາທີ",
        "ວິນາທີ",
        "ມິນລິວິນາທີ",
        ","
      ),
      lt: r(
        function(n) {
          return n % 10 === 0 || n % 100 >= 10 && n % 100 <= 20 ? "metų" : "metai";
        },
        function(n) {
          return ["mėnuo", "mėnesiai", "mėnesių"][b(n)];
        },
        function(n) {
          return ["savaitė", "savaitės", "savaičių"][b(n)];
        },
        function(n) {
          return ["diena", "dienos", "dienų"][b(n)];
        },
        function(n) {
          return ["valanda", "valandos", "valandų"][b(n)];
        },
        function(n) {
          return ["minutė", "minutės", "minučių"][b(n)];
        },
        function(n) {
          return ["sekundė", "sekundės", "sekundžių"][b(n)];
        },
        function(n) {
          return ["milisekundė", "milisekundės", "milisekundžių"][b(n)];
        },
        ","
      ),
      lv: r(
        function(n) {
          return z(n) ? "gads" : "gadi";
        },
        function(n) {
          return z(n) ? "mēnesis" : "mēneši";
        },
        function(n) {
          return z(n) ? "nedēļa" : "nedēļas";
        },
        function(n) {
          return z(n) ? "diena" : "dienas";
        },
        function(n) {
          return z(n) ? "stunda" : "stundas";
        },
        function(n) {
          return z(n) ? "minūte" : "minūtes";
        },
        function(n) {
          return z(n) ? "sekunde" : "sekundes";
        },
        function(n) {
          return z(n) ? "milisekunde" : "milisekundes";
        },
        ","
      ),
      mk: r(
        function(n) {
          return n === 1 ? "година" : "години";
        },
        function(n) {
          return n === 1 ? "месец" : "месеци";
        },
        function(n) {
          return n === 1 ? "недела" : "недели";
        },
        function(n) {
          return n === 1 ? "ден" : "дена";
        },
        function(n) {
          return n === 1 ? "час" : "часа";
        },
        function(n) {
          return n === 1 ? "минута" : "минути";
        },
        function(n) {
          return n === 1 ? "секунда" : "секунди";
        },
        function(n) {
          return n === 1 ? "милисекунда" : "милисекунди";
        },
        ","
      ),
      mn: r(
        "жил",
        "сар",
        "долоо хоног",
        "өдөр",
        "цаг",
        "минут",
        "секунд",
        "миллисекунд"
      ),
      mr: r(
        function(n) {
          return n === 1 ? "वर्ष" : "वर्षे";
        },
        function(n) {
          return n === 1 ? "महिना" : "महिने";
        },
        function(n) {
          return n === 1 ? "आठवडा" : "आठवडे";
        },
        "दिवस",
        "तास",
        function(n) {
          return n === 1 ? "मिनिट" : "मिनिटे";
        },
        "सेकंद",
        "मिलिसेकंद"
      ),
      ms: r(
        "tahun",
        "bulan",
        "minggu",
        "hari",
        "jam",
        "minit",
        "saat",
        "milisaat"
      ),
      nl: r(
        "jaar",
        function(n) {
          return n === 1 ? "maand" : "maanden";
        },
        function(n) {
          return n === 1 ? "week" : "weken";
        },
        function(n) {
          return n === 1 ? "dag" : "dagen";
        },
        "uur",
        function(n) {
          return n === 1 ? "minuut" : "minuten";
        },
        function(n) {
          return n === 1 ? "seconde" : "seconden";
        },
        function(n) {
          return n === 1 ? "milliseconde" : "milliseconden";
        },
        ","
      ),
      no: r(
        "år",
        function(n) {
          return "måned" + (n === 1 ? "" : "er");
        },
        function(n) {
          return "uke" + (n === 1 ? "" : "r");
        },
        function(n) {
          return "dag" + (n === 1 ? "" : "er");
        },
        function(n) {
          return "time" + (n === 1 ? "" : "r");
        },
        function(n) {
          return "minutt" + (n === 1 ? "" : "er");
        },
        function(n) {
          return "sekund" + (n === 1 ? "" : "er");
        },
        function(n) {
          return "millisekund" + (n === 1 ? "" : "er");
        },
        ","
      ),
      pl: r(
        function(n) {
          return ["rok", "roku", "lata", "lat"][h(n)];
        },
        function(n) {
          return ["miesiąc", "miesiąca", "miesiące", "miesięcy"][h(n)];
        },
        function(n) {
          return ["tydzień", "tygodnia", "tygodnie", "tygodni"][h(n)];
        },
        function(n) {
          return ["dzień", "dnia", "dni", "dni"][h(n)];
        },
        function(n) {
          return ["godzina", "godziny", "godziny", "godzin"][h(n)];
        },
        function(n) {
          return ["minuta", "minuty", "minuty", "minut"][h(n)];
        },
        function(n) {
          return ["sekunda", "sekundy", "sekundy", "sekund"][h(n)];
        },
        function(n) {
          return ["milisekunda", "milisekundy", "milisekundy", "milisekund"][h(n)];
        },
        ","
      ),
      pt: r(
        function(n) {
          return "ano" + (n === 1 ? "" : "s");
        },
        function(n) {
          return n === 1 ? "mês" : "meses";
        },
        function(n) {
          return "semana" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "dia" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "hora" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "minuto" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "segundo" + (n === 1 ? "" : "s");
        },
        function(n) {
          return "milissegundo" + (n === 1 ? "" : "s");
        },
        ","
      ),
      ro: r(
        function(n) {
          return n === 1 ? "an" : "ani";
        },
        function(n) {
          return n === 1 ? "lună" : "luni";
        },
        function(n) {
          return n === 1 ? "săptămână" : "săptămâni";
        },
        function(n) {
          return n === 1 ? "zi" : "zile";
        },
        function(n) {
          return n === 1 ? "oră" : "ore";
        },
        function(n) {
          return n === 1 ? "minut" : "minute";
        },
        function(n) {
          return n === 1 ? "secundă" : "secunde";
        },
        function(n) {
          return n === 1 ? "milisecundă" : "milisecunde";
        },
        ","
      ),
      ru: r(
        function(n) {
          return ["лет", "год", "года"][i(n)];
        },
        function(n) {
          return ["месяцев", "месяц", "месяца"][i(n)];
        },
        function(n) {
          return ["недель", "неделя", "недели"][i(n)];
        },
        function(n) {
          return ["дней", "день", "дня"][i(n)];
        },
        function(n) {
          return ["часов", "час", "часа"][i(n)];
        },
        function(n) {
          return ["минут", "минута", "минуты"][i(n)];
        },
        function(n) {
          return ["секунд", "секунда", "секунды"][i(n)];
        },
        function(n) {
          return ["миллисекунд", "миллисекунда", "миллисекунды"][i(n)];
        },
        ","
      ),
      sq: r(
        function(n) {
          return n === 1 ? "vit" : "vjet";
        },
        "muaj",
        "javë",
        "ditë",
        "orë",
        function(n) {
          return "minut" + (n === 1 ? "ë" : "a");
        },
        function(n) {
          return "sekond" + (n === 1 ? "ë" : "a");
        },
        function(n) {
          return "milisekond" + (n === 1 ? "ë" : "a");
        },
        ","
      ),
      sr: r(
        function(n) {
          return ["години", "година", "године"][i(n)];
        },
        function(n) {
          return ["месеци", "месец", "месеца"][i(n)];
        },
        function(n) {
          return ["недељи", "недеља", "недеље"][i(n)];
        },
        function(n) {
          return ["дани", "дан", "дана"][i(n)];
        },
        function(n) {
          return ["сати", "сат", "сата"][i(n)];
        },
        function(n) {
          return ["минута", "минут", "минута"][i(n)];
        },
        function(n) {
          return ["секунди", "секунда", "секунде"][i(n)];
        },
        function(n) {
          return ["милисекунди", "милисекунда", "милисекунде"][i(n)];
        },
        ","
      ),
      ta: r(
        function(n) {
          return n === 1 ? "வருடம்" : "ஆண்டுகள்";
        },
        function(n) {
          return n === 1 ? "மாதம்" : "மாதங்கள்";
        },
        function(n) {
          return n === 1 ? "வாரம்" : "வாரங்கள்";
        },
        function(n) {
          return n === 1 ? "நாள்" : "நாட்கள்";
        },
        function(n) {
          return n === 1 ? "மணி" : "மணிநேரம்";
        },
        function(n) {
          return "நிமிட" + (n === 1 ? "ம்" : "ங்கள்");
        },
        function(n) {
          return "வினாடி" + (n === 1 ? "" : "கள்");
        },
        function(n) {
          return "மில்லி விநாடி" + (n === 1 ? "" : "கள்");
        }
      ),
      te: r(
        function(n) {
          return "సంవత్స" + (n === 1 ? "రం" : "రాల");
        },
        function(n) {
          return "నెల" + (n === 1 ? "" : "ల");
        },
        function(n) {
          return n === 1 ? "వారం" : "వారాలు";
        },
        function(n) {
          return "రోజు" + (n === 1 ? "" : "లు");
        },
        function(n) {
          return "గంట" + (n === 1 ? "" : "లు");
        },
        function(n) {
          return n === 1 ? "నిమిషం" : "నిమిషాలు";
        },
        function(n) {
          return n === 1 ? "సెకను" : "సెకన్లు";
        },
        function(n) {
          return n === 1 ? "మిల్లీసెకన్" : "మిల్లీసెకన్లు";
        }
      ),
      uk: r(
        function(n) {
          return ["років", "рік", "роки"][i(n)];
        },
        function(n) {
          return ["місяців", "місяць", "місяці"][i(n)];
        },
        function(n) {
          return ["тижнів", "тиждень", "тижні"][i(n)];
        },
        function(n) {
          return ["днів", "день", "дні"][i(n)];
        },
        function(n) {
          return ["годин", "година", "години"][i(n)];
        },
        function(n) {
          return ["хвилин", "хвилина", "хвилини"][i(n)];
        },
        function(n) {
          return ["секунд", "секунда", "секунди"][i(n)];
        },
        function(n) {
          return ["мілісекунд", "мілісекунда", "мілісекунди"][i(n)];
        },
        ","
      ),
      ur: r(
        "سال",
        function(n) {
          return n === 1 ? "مہینہ" : "مہینے";
        },
        function(n) {
          return n === 1 ? "ہفتہ" : "ہفتے";
        },
        "دن",
        function(n) {
          return n === 1 ? "گھنٹہ" : "گھنٹے";
        },
        "منٹ",
        "سیکنڈ",
        "ملی سیکنڈ"
      ),
      sk: r(
        function(n) {
          return ["rok", "roky", "roky", "rokov"][l(n)];
        },
        function(n) {
          return ["mesiac", "mesiace", "mesiace", "mesiacov"][l(n)];
        },
        function(n) {
          return ["týždeň", "týždne", "týždne", "týždňov"][l(n)];
        },
        function(n) {
          return ["deň", "dni", "dni", "dní"][l(n)];
        },
        function(n) {
          return ["hodina", "hodiny", "hodiny", "hodín"][l(n)];
        },
        function(n) {
          return ["minúta", "minúty", "minúty", "minút"][l(n)];
        },
        function(n) {
          return ["sekunda", "sekundy", "sekundy", "sekúnd"][l(n)];
        },
        function(n) {
          return ["milisekunda", "milisekundy", "milisekundy", "milisekúnd"][l(n)];
        },
        ","
      ),
      sl: r(
        function(n) {
          return n % 10 === 1 ? "leto" : n % 100 === 2 ? "leti" : n % 100 === 3 || n % 100 === 4 || Math.floor(n) !== n && n % 100 <= 5 ? "leta" : "let";
        },
        function(n) {
          return n % 10 === 1 ? "mesec" : n % 100 === 2 || Math.floor(n) !== n && n % 100 <= 5 ? "meseca" : n % 10 === 3 || n % 10 === 4 ? "mesece" : "mesecev";
        },
        function(n) {
          return n % 10 === 1 ? "teden" : n % 10 === 2 || Math.floor(n) !== n && n % 100 <= 4 ? "tedna" : n % 10 === 3 || n % 10 === 4 ? "tedne" : "tednov";
        },
        function(n) {
          return n % 100 === 1 ? "dan" : "dni";
        },
        function(n) {
          return n % 10 === 1 ? "ura" : n % 100 === 2 ? "uri" : n % 10 === 3 || n % 10 === 4 || Math.floor(n) !== n ? "ure" : "ur";
        },
        function(n) {
          return n % 10 === 1 ? "minuta" : n % 10 === 2 ? "minuti" : n % 10 === 3 || n % 10 === 4 || Math.floor(n) !== n && n % 100 <= 4 ? "minute" : "minut";
        },
        function(n) {
          return n % 10 === 1 ? "sekunda" : n % 100 === 2 ? "sekundi" : n % 100 === 3 || n % 100 === 4 || Math.floor(n) !== n ? "sekunde" : "sekund";
        },
        function(n) {
          return n % 10 === 1 ? "milisekunda" : n % 100 === 2 ? "milisekundi" : n % 100 === 3 || n % 100 === 4 || Math.floor(n) !== n ? "milisekunde" : "milisekund";
        },
        ","
      ),
      sv: r(
        "år",
        function(n) {
          return "månad" + (n === 1 ? "" : "er");
        },
        function(n) {
          return "veck" + (n === 1 ? "a" : "or");
        },
        function(n) {
          return "dag" + (n === 1 ? "" : "ar");
        },
        function(n) {
          return "timm" + (n === 1 ? "e" : "ar");
        },
        function(n) {
          return "minut" + (n === 1 ? "" : "er");
        },
        function(n) {
          return "sekund" + (n === 1 ? "" : "er");
        },
        function(n) {
          return "millisekund" + (n === 1 ? "" : "er");
        },
        ","
      ),
      sw: y(
        r(
          function(n) {
            return n === 1 ? "mwaka" : "miaka";
          },
          function(n) {
            return n === 1 ? "mwezi" : "miezi";
          },
          "wiki",
          function(n) {
            return n === 1 ? "siku" : "masiku";
          },
          function(n) {
            return n === 1 ? "saa" : "masaa";
          },
          "dakika",
          "sekunde",
          "milisekunde"
        ),
        { _numberFirst: true }
      ),
      tr: r(
        "yıl",
        "ay",
        "hafta",
        "gün",
        "saat",
        "dakika",
        "saniye",
        "milisaniye",
        ","
      ),
      th: r(
        "ปี",
        "เดือน",
        "สัปดาห์",
        "วัน",
        "ชั่วโมง",
        "นาที",
        "วินาที",
        "มิลลิวินาที"
      ),
      uz: r(
        "yil",
        "oy",
        "hafta",
        "kun",
        "soat",
        "minut",
        "sekund",
        "millisekund"
      ),
      uz_CYR: r(
        "йил",
        "ой",
        "ҳафта",
        "кун",
        "соат",
        "минут",
        "секунд",
        "миллисекунд"
      ),
      vi: r(
        "năm",
        "tháng",
        "tuần",
        "ngày",
        "giờ",
        "phút",
        "giây",
        "mili giây",
        ","
      ),
      zh_CN: r("年", "个月", "周", "天", "小时", "分钟", "秒", "毫秒"),
      zh_TW: r("年", "個月", "周", "天", "小時", "分鐘", "秒", "毫秒")
    };
    function r(n, t, e2, u, a, m, f, c, d) {
      var o = { y: n, mo: t, w: e2, d: u, h: a, m, s: f, ms: c };
      return typeof d < "u" && (o.decimal = d), o;
    }
    function g(n) {
      return n === 2 ? 1 : n > 2 && n < 11 ? 2 : 0;
    }
    function h(n) {
      return n === 1 ? 0 : Math.floor(n) !== n ? 1 : n % 10 >= 2 && n % 10 <= 4 && !(n % 100 > 10 && n % 100 < 20) ? 2 : 3;
    }
    function i(n) {
      return Math.floor(n) !== n ? 2 : n % 100 >= 5 && n % 100 <= 20 || n % 10 >= 5 && n % 10 <= 9 || n % 10 === 0 ? 0 : n % 10 === 1 ? 1 : n > 1 ? 2 : 0;
    }
    function l(n) {
      return n === 1 ? 0 : Math.floor(n) !== n ? 1 : n % 10 >= 2 && n % 10 <= 4 && n % 100 < 10 ? 2 : 3;
    }
    function b(n) {
      return n === 1 || n % 10 === 1 && n % 100 > 20 ? 0 : Math.floor(n) !== n || n % 10 >= 2 && n % 100 > 20 || n % 10 >= 2 && n % 100 < 10 ? 1 : 2;
    }
    function z(n) {
      return n % 10 === 1 && n % 100 !== 11;
    }
    function v(n, t) {
      return Object.prototype.hasOwnProperty.call(n, t);
    }
    function R(n) {
      var t = [n.language];
      if (v(n, "fallbacks"))
        if (D(n.fallbacks) && n.fallbacks.length)
          t = t.concat(n.fallbacks);
        else
          throw new Error("fallbacks must be an array with at least one element");
      for (var e2 = 0; e2 < t.length; e2++) {
        var u = t[e2];
        if (v(n.languages, u))
          return n.languages[u];
        if (v(k, u))
          return k[u];
      }
      throw new Error("No language found.");
    }
    function O(n, t, e2) {
      var u = n.unitName, a = n.unitCount, m = e2.spacer, f = e2.maxDecimalPoints, c;
      v(e2, "decimal") ? c = e2.decimal : v(t, "decimal") ? c = t.decimal : c = ".";
      var d;
      "digitReplacements" in e2 ? d = e2.digitReplacements : "_digitReplacements" in t && (d = t._digitReplacements);
      var o, p = f === void 0 ? a : Math.floor(a * Math.pow(10, f)) / Math.pow(10, f), x = p.toString();
      if (t._hideCountIf2 && a === 2)
        o = "", m = "";
      else if (d) {
        o = "";
        for (var w = 0; w < x.length; w++) {
          var _ = x[w];
          _ === "." ? o += c : o += d[_];
        }
      } else
        o = x.replace(".", c);
      var M = t[u], C;
      return typeof M == "function" ? C = M(a) : C = M, t._numberFirst ? C + m + o : o + m + C;
    }
    function q(n, t) {
      var e2, u, a, m, f = t.units, c = t.unitMeasures, d = "largest" in t ? t.largest : 1 / 0;
      if (!f.length)
        return [];
      var o = {};
      for (m = n, u = 0; u < f.length; u++) {
        e2 = f[u];
        var p = c[e2], x = u === f.length - 1;
        a = x ? m / p : Math.floor(m / p), o[e2] = a, m -= a * p;
      }
      if (t.round) {
        var w = d;
        for (u = 0; u < f.length; u++)
          if (e2 = f[u], a = o[e2], a !== 0 && (w--, w === 0)) {
            for (var _ = u + 1; _ < f.length; _++) {
              var M = f[_], C = o[M];
              o[e2] += C * c[M] / c[e2], o[M] = 0;
            }
            break;
          }
        for (u = f.length - 1; u >= 0; u--)
          if (e2 = f[u], a = o[e2], a !== 0) {
            var P = Math.round(a);
            if (o[e2] = P, u === 0)
              break;
            var V = f[u - 1], G = c[V], A = Math.floor(
              P * c[e2] / G
            );
            if (A)
              o[V] += A, o[e2] = 0;
            else
              break;
          }
      }
      var S = [];
      for (u = 0; u < f.length && S.length < d; u++)
        e2 = f[u], a = o[e2], a && S.push({ unitName: e2, unitCount: a });
      return S;
    }
    function H(n, t) {
      var e2 = R(t);
      if (!n.length) {
        var u = t.units, a = u[u.length - 1];
        return O(
          { unitName: a, unitCount: 0 },
          e2,
          t
        );
      }
      var m = t.conjunction, f = t.serialComma, c;
      v(t, "delimiter") ? c = t.delimiter : v(e2, "delimiter") ? c = e2.delimiter : c = ", ";
      for (var d = [], o = 0; o < n.length; o++)
        d.push(O(n[o], e2, t));
      return !m || n.length === 1 ? d.join(c) : n.length === 2 ? d.join(m) : d.slice(0, -1).join(c) + (f ? "," : "") + m + d.slice(-1);
    }
    function Y(n) {
      var t = function(u, a) {
        u = Math.abs(u);
        var m = y({}, t, a || {}), f = q(u, m);
        return H(f, m);
      };
      return y(
        t,
        {
          language: "en",
          spacer: " ",
          conjunction: "",
          serialComma: true,
          units: ["y", "mo", "w", "d", "h", "m", "s"],
          languages: {},
          round: false,
          unitMeasures: {
            y: 315576e5,
            mo: 26298e5,
            w: 6048e5,
            d: 864e5,
            h: 36e5,
            m: 6e4,
            s: 1e3,
            ms: 1
          }
        },
        n
      );
    }
    var E = y(Y({}), {
      getSupportedLanguages: function() {
        var t = [];
        for (var e2 in k)
          v(k, e2) && e2 !== "gr" && t.push(e2);
        return t;
      },
      humanizer: Y
    });
    s.exports ? s.exports = E : this.humanizeDuration = E;
  })();
})(N);
var T = N.exports;
var X = e(T);
var Z = {
  language: "zh_CN",
  largest: 1,
  maxDecimalPoints: 0
};
var U = defineComponent({
  name: "FsTimeHumanize",
  props: {
    /**
     * 日期时间值，支持long,string,date等，由dayjs转化
     */
    modelValue: { required: false, default: void 0 },
    /**
     *  输入格式化，不传则由dayjs自动转化
     */
    valueFormat: { type: String, default: void 0, required: false },
    /**
     *  日期输出格式化
     */
    format: { type: String, default: "YYYY-MM-DD HH:mm:ss", required: false },
    /**
     * 距离时间超过多少毫秒时，直接使用format格式，默认大于3天后
     */
    useFormatGreater: { type: Number, default: 1e3 * 60 * 60 * 24 * 3, required: false },
    /**
     * [HumanizeDuration参数](https://github.com/EvanHahn/HumanizeDuration.js)
     */
    options: {
      type: Object
    },
    /**
     * 前后文本
     * `{ prev: string; after: string }`
     */
    text: {
      type: Object
    }
  },
  setup(s) {
    const y = computed(() => s.modelValue == null || s.modelValue === "" ? "" : (0, import_dayjs.default)(s.modelValue).format("YYYY-MM-DD HH:mm:ss"));
    return {
      formatted: computed(() => {
        var g, h;
        if (s.modelValue == null || s.modelValue === "")
          return "";
        let j;
        s.valueFormat != null ? j = (0, import_dayjs.default)(s.modelValue, s.valueFormat) : j = (0, import_dayjs.default)(s.modelValue);
        let k = (0, import_dayjs.default)().valueOf() - j.valueOf(), r = ((g = s.text) == null ? void 0 : g.ago) ?? "前";
        return k < 0 && (r = ((h = s.text) == null ? void 0 : h.after) ?? "后", k = -k), k > s.useFormatGreater ? j.format(s.format) : X(k, merge_default({}, Z, s.options)) + r;
      }),
      fullText: y
    };
  }
});
var nn = ["title"];
function rn(s, y, D, j, k, r) {
  return openBlock(), createElementBlock("span", { title: s.fullText }, toDisplayString(s.formatted), 9, nn);
}
var sn = he(U, [["render", rn]]);
export {
  sn as default
};
//# sourceMappingURL=fs-time-humanize-bce0eda2-UDVYW3M6.js.map
