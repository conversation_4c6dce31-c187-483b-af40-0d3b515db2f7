const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/header.BPbW56P8.js","assets/index.GuQX7xXE.js","assets/vue.zNq9Glab.js","assets/index.WGbL7eFL.css","assets/main.Mab_8bra.js"])))=>i.map(i=>d[i]);
import{u as d,_ as a}from"./index.GuQX7xXE.js";import{d as l,a as y,M as R,Q as T,o as h,m as n,b as v,g as C,e as M,w as b,f as s,u as r,P as c,I as g}from"./vue.zNq9Glab.js";const k=l({name:"layoutTransverse"}),S=l({...k,setup(x){const u=c(()=>a(()=>import("./header.BPbW56P8.js"),__vite__mapDeps([0,1,2,3]))),_=c(()=>a(()=>import("./main.Mab_8bra.js"),__vite__mapDeps([4,1,2,3]))),e=y(),i=d(),{themeConfig:f}=R(i),p=T(),o=()=>{e.value.layoutMainScrollbarRef.update()},t=()=>{g(()=>{setTimeout(()=>{o(),e.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return h(()=>{t()}),n(()=>p.path,()=>{t()}),n(f,()=>{o()},{deep:!0}),(w,E)=>{const m=v("el-container");return M(),C(m,{class:"layout-container flex-center layout-backtop"},{default:b(()=>[s(r(u)),s(r(_),{ref_key:"layoutMainRef",ref:e},null,512)]),_:1})}}});export{S as default};
