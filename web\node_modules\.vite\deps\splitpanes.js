import "./chunk-44FYVRJW.js";
import {
  computed,
  createBlock,
  createElementBlock,
  getCurrentInstance,
  h,
  inject,
  nextTick,
  normalizeStyle,
  onBeforeUnmount,
  onMounted,
  openBlock,
  provide,
  ref,
  renderSlot,
  resolveDynamicComponent,
  unref,
  useSlots,
  watch
} from "./chunk-WEJJSMSC.js";
import "./chunk-LK32TJAX.js";

// node_modules/splitpanes/dist/splitpanes.es.js
var Se = {
  __name: "splitpanes",
  props: {
    horizontal: { type: Boolean },
    pushOtherPanes: { type: Boolean, default: true },
    dblClickSplitter: { type: Boolean, default: true },
    rtl: { type: Boolean, default: false },
    // Right to left direction.
    firstSplitter: { type: Boolean }
  },
  emits: [
    "ready",
    "resize",
    "resized",
    "pane-click",
    "pane-maximize",
    "pane-add",
    "pane-remove",
    "splitter-click"
  ],
  setup(B, { emit: x }) {
    const v = x, u = B, w = useSlots(), a = ref([]), k = computed(() => a.value.reduce((e, n) => (e[~~n.id] = n) && e, {})), d = computed(() => a.value.length), h2 = ref(null), S = ref(false), c = ref({
      mouseDown: false,
      dragging: false,
      activeSplitter: null,
      cursorOffset: 0
      // Cursor offset within the splitter.
    }), f = ref({
      // Used to detect double click on touch devices.
      splitter: null,
      timeoutId: null
    }), _ = computed(() => ({
      [`splitpanes splitpanes--${u.horizontal ? "horizontal" : "vertical"}`]: true,
      "splitpanes--dragging": c.value.dragging
    })), y = () => {
      document.addEventListener("mousemove", r, { passive: false }), document.addEventListener("mouseup", P), "ontouchstart" in window && (document.addEventListener("touchmove", r, { passive: false }), document.addEventListener("touchend", P));
    }, D = () => {
      document.removeEventListener("mousemove", r, { passive: false }), document.removeEventListener("mouseup", P), "ontouchstart" in window && (document.removeEventListener("touchmove", r, { passive: false }), document.removeEventListener("touchend", P));
    }, R = (e, n) => {
      const t = e.target.closest(".splitpanes__splitter");
      if (t) {
        const { left: i, top: s } = t.getBoundingClientRect(), { clientX: l, clientY: o } = "ontouchstart" in window && e.touches ? e.touches[0] : e;
        c.value.cursorOffset = u.horizontal ? o - s : l - i;
      }
      y(), c.value.mouseDown = true, c.value.activeSplitter = n;
    }, r = (e) => {
      c.value.mouseDown && (e.preventDefault(), c.value.dragging = true, requestAnimationFrame(() => {
        J(Y(e)), v("resize", a.value.map((n) => ({ min: n.min, max: n.max, size: n.size })));
      }));
    }, P = () => {
      c.value.dragging && v("resized", a.value.map((e) => ({ min: e.min, max: e.max, size: e.size }))), c.value.mouseDown = false, setTimeout(() => {
        c.value.dragging = false, D();
      }, 100);
    }, O = (e, n) => {
      "ontouchstart" in window && (e.preventDefault(), u.dblClickSplitter && (f.value.splitter === n ? (clearTimeout(f.value.timeoutId), f.value.timeoutId = null, A(e, n), f.value.splitter = null) : (f.value.splitter = n, f.value.timeoutId = setTimeout(() => f.value.splitter = null, 500)))), c.value.dragging || v("splitter-click", a.value[n]);
    }, A = (e, n) => {
      let t = 0;
      a.value = a.value.map((i, s) => (i.size = s === n ? i.max : i.min, s !== n && (t += i.min), i)), a.value[n].size -= t, v("pane-maximize", a.value[n]), v("resized", a.value.map((i) => ({ min: i.min, max: i.max, size: i.size })));
    }, X = (e, n) => {
      v("pane-click", k.value[n]);
    }, Y = (e) => {
      const n = h2.value.getBoundingClientRect(), { clientX: t, clientY: i } = "ontouchstart" in window && e.touches ? e.touches[0] : e;
      return {
        x: t - (u.horizontal ? 0 : c.value.cursorOffset) - n.left,
        y: i - (u.horizontal ? c.value.cursorOffset : 0) - n.top
      };
    }, W = (e) => {
      e = e[u.horizontal ? "y" : "x"];
      const n = h2.value[u.horizontal ? "clientHeight" : "clientWidth"];
      return u.rtl && !u.horizontal && (e = n - e), e * 100 / n;
    }, J = (e) => {
      const n = c.value.activeSplitter;
      let t = {
        prevPanesSize: U(n),
        nextPanesSize: b(n),
        prevReachedMinPanes: 0,
        nextReachedMinPanes: 0
      };
      const i = 0 + (u.pushOtherPanes ? 0 : t.prevPanesSize), s = 100 - (u.pushOtherPanes ? 0 : t.nextPanesSize), l = Math.max(Math.min(W(e), s), i);
      let o = [n, n + 1], m = a.value[o[0]] || null, z = a.value[o[1]] || null;
      const F = m.max < 100 && l >= m.max + t.prevPanesSize, re = z.max < 100 && l <= 100 - (z.max + b(n + 1));
      if (F || re) {
        F ? (m.size = m.max, z.size = Math.max(100 - m.max - t.prevPanesSize - t.nextPanesSize, 0)) : (m.size = Math.max(100 - z.max - t.prevPanesSize - b(n + 1), 0), z.size = z.max);
        return;
      }
      if (u.pushOtherPanes) {
        const I = K(t, l);
        if (!I) return;
        ({ sums: t, panesToResize: o } = I), m = a.value[o[0]] || null, z = a.value[o[1]] || null;
      }
      m !== null && (m.size = Math.min(Math.max(l - t.prevPanesSize - t.prevReachedMinPanes, m.min), m.max)), z !== null && (z.size = Math.min(Math.max(100 - l - t.nextPanesSize - t.nextReachedMinPanes, z.min), z.max));
    }, K = (e, n) => {
      const t = c.value.activeSplitter, i = [t, t + 1];
      return n < e.prevPanesSize + a.value[i[0]].min && (i[0] = Q(t).index, e.prevReachedMinPanes = 0, i[0] < t && a.value.forEach((s, l) => {
        l > i[0] && l <= t && (s.size = s.min, e.prevReachedMinPanes += s.min);
      }), e.prevPanesSize = U(i[0]), i[0] === void 0) ? (e.prevReachedMinPanes = 0, a.value[0].size = a.value[0].min, a.value.forEach((s, l) => {
        l > 0 && l <= t && (s.size = s.min, e.prevReachedMinPanes += s.min);
      }), a.value[i[1]].size = 100 - e.prevReachedMinPanes - a.value[0].min - e.prevPanesSize - e.nextPanesSize, null) : n > 100 - e.nextPanesSize - a.value[i[1]].min && (i[1] = V(t).index, e.nextReachedMinPanes = 0, i[1] > t + 1 && a.value.forEach((s, l) => {
        l > t && l < i[1] && (s.size = s.min, e.nextReachedMinPanes += s.min);
      }), e.nextPanesSize = b(i[1] - 1), i[1] === void 0) ? (e.nextReachedMinPanes = 0, a.value.forEach((s, l) => {
        l < d.value - 1 && l >= t + 1 && (s.size = s.min, e.nextReachedMinPanes += s.min);
      }), a.value[i[0]].size = 100 - e.prevPanesSize - b(i[0] - 1), null) : { sums: e, panesToResize: i };
    }, U = (e) => a.value.reduce((n, t, i) => n + (i < e ? t.size : 0), 0), b = (e) => a.value.reduce((n, t, i) => n + (i > e + 1 ? t.size : 0), 0), Q = (e) => [...a.value].reverse().find((t) => t.index < e && t.size > t.min) || {}, V = (e) => a.value.find((t) => t.index > e + 1 && t.size > t.min) || {}, Z = () => {
      var n;
      Array.from(((n = h2.value) == null ? void 0 : n.children) || []).forEach((t) => {
        const i = t.classList.contains("splitpanes__pane"), s = t.classList.contains("splitpanes__splitter");
        !i && !s && (t.remove(), console.warn("Splitpanes: Only <pane> elements are allowed at the root of <splitpanes>. One of your DOM nodes was removed."));
      });
    }, $ = (e, n, t = false) => {
      const i = e - 1, s = document.createElement("div");
      s.classList.add("splitpanes__splitter"), t || (s.onmousedown = (l) => R(l, i), typeof window < "u" && "ontouchstart" in window && (s.ontouchstart = (l) => R(l, i)), s.onclick = (l) => O(l, i + 1)), u.dblClickSplitter && (s.ondblclick = (l) => A(l, i + 1)), n.parentNode.insertBefore(s, n);
    }, ee = (e) => {
      e.onmousedown = void 0, e.onclick = void 0, e.ondblclick = void 0, e.remove();
    }, N = () => {
      var t;
      const e = Array.from(((t = h2.value) == null ? void 0 : t.children) || []);
      e.forEach((i) => {
        i.className.includes("splitpanes__splitter") && ee(i);
      });
      let n = 0;
      e.forEach((i) => {
        i.className.includes("splitpanes__pane") && (!n && u.firstSplitter ? $(n, i, true) : n && $(n, i), n++);
      });
    }, ie = ({ uid: e, ...n }) => {
      const t = k.value[e];
      Object.entries(n).forEach(([i, s]) => t[i] = s);
    }, ne = (e) => {
      var t;
      let n = -1;
      Array.from(((t = h2.value) == null ? void 0 : t.children) || []).some((i) => (i.className.includes("splitpanes__pane") && n++, i.isSameNode(e.el))), a.value.splice(n, 0, { ...e, index: n }), a.value.forEach((i, s) => i.index = s), S.value && nextTick(() => {
        N(), q({ addedPane: a.value[n] }), v("pane-add", { index: n, panes: a.value.map((i) => ({ min: i.min, max: i.max, size: i.size })) });
      });
    }, te = (e) => {
      const n = a.value.findIndex((i) => i.id === e), t = a.value.splice(n, 1)[0];
      a.value.forEach((i, s) => i.index = s), nextTick(() => {
        N(), q({ removedPane: { ...t } }), v("pane-remove", { removed: t, panes: a.value.map((i) => ({ min: i.min, max: i.max, size: i.size })) });
      });
    }, q = (e = {}) => {
      !e.addedPane && !e.removedPane ? ae() : a.value.some((n) => n.givenSize !== null || n.min || n.max < 100) ? le(e) : se(), S.value && v("resized", a.value.map((n) => ({ min: n.min, max: n.max, size: n.size })));
    }, se = () => {
      const e = 100 / d.value;
      let n = 0;
      const t = [], i = [];
      a.value.forEach((s) => {
        s.size = Math.max(Math.min(e, s.max), s.min), n -= s.size, s.size >= s.max && t.push(s.id), s.size <= s.min && i.push(s.id);
      }), n > 0.1 && L(n, t, i);
    }, ae = () => {
      let e = 100;
      const n = [], t = [];
      let i = 0;
      a.value.forEach((l) => {
        e -= l.size, l.givenSize !== null && i++, l.size >= l.max && n.push(l.id), l.size <= l.min && t.push(l.id);
      });
      let s = 100;
      e > 0.1 && (a.value.forEach((l) => {
        l.givenSize === null && (l.size = Math.max(Math.min(e / (d.value - i), l.max), l.min)), s -= l.size;
      }), s > 0.1 && L(s, n, t));
    }, le = ({ addedPane: e, removedPane: n } = {}) => {
      let t = 100 / d.value, i = 0;
      const s = [], l = [];
      ((e == null ? void 0 : e.givenSize) ?? null) !== null && (t = (100 - e.givenSize) / (d.value - 1).value), a.value.forEach((o) => {
        i -= o.size, o.size >= o.max && s.push(o.id), o.size <= o.min && l.push(o.id);
      }), !(Math.abs(i) < 0.1) && (a.value.forEach((o) => {
        (e == null ? void 0 : e.givenSize) !== null && (e == null ? void 0 : e.id) === o.id || (o.size = Math.max(Math.min(t, o.max), o.min)), i -= o.size, o.size >= o.max && s.push(o.id), o.size <= o.min && l.push(o.id);
      }), i > 0.1 && L(i, s, l));
    }, L = (e, n, t) => {
      let i;
      e > 0 ? i = e / (d.value - n.length) : i = e / (d.value - t.length), a.value.forEach((s, l) => {
        if (e > 0 && !n.includes(s.id)) {
          const o = Math.max(Math.min(s.size + i, s.max), s.min), m = o - s.size;
          e -= m, s.size = o;
        } else if (!t.includes(s.id)) {
          const o = Math.max(Math.min(s.size + i, s.max), s.min), m = o - s.size;
          e -= m, s.size = o;
        }
      }), Math.abs(e) > 0.1 && nextTick(() => {
        S.value && console.warn("Splitpanes: Could not resize panes correctly due to their constraints.");
      });
    };
    watch(() => u.firstSplitter, () => N()), watch(() => u.dblClickSplitter, (e) => {
      [...h2.value.querySelectorAll(".splitpanes__splitter")].forEach((t, i) => {
        t.ondblclick = e ? (s) => A(s, i) : void 0;
      });
    }), onBeforeUnmount(() => S.value = false), onMounted(() => {
      Z(), N(), q(), v("ready"), S.value = true;
    });
    const oe = () => {
      var e;
      return h(
        "div",
        { ref: h2, class: _.value },
        (e = w.default) == null ? void 0 : e.call(w)
      );
    };
    return provide("panes", a), provide("indexedPanes", k), provide("horizontal", computed(() => u.horizontal)), provide("requestUpdate", ie), provide("onPaneAdd", ne), provide("onPaneRemove", te), provide("onPaneClick", X), (e, n) => (openBlock(), createBlock(resolveDynamicComponent(oe)));
  }
};
var Pe = {
  __name: "pane",
  props: {
    size: { type: [Number, String] },
    minSize: { type: [Number, String], default: 0 },
    maxSize: { type: [Number, String], default: 100 }
  },
  setup(B) {
    var R;
    const x = B, v = inject("requestUpdate"), u = inject("onPaneAdd"), w = inject("horizontal"), a = inject("onPaneRemove"), k = inject("onPaneClick"), d = (R = getCurrentInstance()) == null ? void 0 : R.uid, h2 = inject("indexedPanes"), S = computed(() => h2.value[d]), c = ref(null), f = computed(() => {
      const r = isNaN(x.size) || x.size === void 0 ? 0 : parseFloat(x.size);
      return Math.max(Math.min(r, y.value), _.value);
    }), _ = computed(() => {
      const r = parseFloat(x.minSize);
      return isNaN(r) ? 0 : r;
    }), y = computed(() => {
      const r = parseFloat(x.maxSize);
      return isNaN(r) ? 100 : r;
    }), D = computed(() => {
      var r;
      return `${w.value ? "height" : "width"}: ${(r = S.value) == null ? void 0 : r.size}%`;
    });
    return onMounted(() => {
      u({
        id: d,
        el: c.value,
        min: _.value,
        max: y.value,
        // The given size (useful to know the user intention).
        givenSize: x.size === void 0 ? null : f.value,
        size: f.value
        // The computed current size at any time.
      });
    }), watch(() => f.value, (r) => v({ uid: d, size: r })), watch(() => _.value, (r) => v({ uid: d, min: r })), watch(() => y.value, (r) => v({ uid: d, max: r })), onBeforeUnmount(() => a(d)), (r, P) => (openBlock(), createElementBlock("div", {
      ref_key: "paneEl",
      ref: c,
      class: "splitpanes__pane",
      onClick: P[0] || (P[0] = (O) => unref(k)(O, r._.uid)),
      style: normalizeStyle(D.value)
    }, [
      renderSlot(r.$slots, "default")
    ], 4));
  }
};
export {
  Pe as Pane,
  Se as Splitpanes
};
//# sourceMappingURL=splitpanes.js.map
